{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "https://macra.gov.mw/schemas/database", "title": "MACRA Portal Database Schema", "description": "Database schema for Malawi Communications Regulatory Authority (MACRA) licensing portal", "type": "object", "definitions": {"users": {"type": "object", "description": "System users including applicants, evaluators, and administrators", "properties": {"user_id": {"type": "string", "format": "uuid", "description": "Unique user identifier"}, "email": {"type": "string", "format": "email", "description": "User email address (unique)"}, "password_hash": {"type": "string", "description": "Hashed password"}, "first_name": {"type": "string", "minLength": 1, "maxLength": 100}, "last_name": {"type": "string", "minLength": 1, "maxLength": 100}, "phone": {"type": "string", "pattern": "^[0-9]{10}$", "description": "10-digit phone number"}, "role": {"type": "string", "enum": ["applicant", "evaluator", "administrator", "super_admin"], "description": "User role in the system"}, "status": {"type": "string", "enum": ["active", "inactive", "suspended"], "default": "active"}, "profile_image": {"type": ["string", "null"], "description": "URL to profile image"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "last_login": {"type": ["string", "null"], "format": "date-time"}}, "required": ["user_id", "email", "password_hash", "first_name", "last_name", "role", "created_at"], "additionalProperties": false}, "organizations": {"type": "object", "description": "Organizations/companies applying for licenses", "properties": {"organization_id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "minLength": 1, "maxLength": 255}, "registration_number": {"type": "string", "description": "Company registration number"}, "tpin": {"type": "string", "pattern": "^[0-9]+$", "description": "Tax Payer Identification Number"}, "website": {"type": ["string", "null"], "format": "uri"}, "email": {"type": "string", "format": "email"}, "phone": {"type": "string", "pattern": "^[0-9]{10}$"}, "fax": {"type": ["string", "null"], "pattern": "^[0-9]{10}$"}, "postal_address": {"type": "string", "description": "Must include P.O. Box, P/Bag, Private Bag, or PO Box"}, "physical_address": {"type": "string"}, "date_incorporation": {"type": "string", "format": "date"}, "place_incorporation": {"type": "string"}, "profile_description": {"type": "string", "description": "Organization background and activities"}, "created_by": {"type": "string", "format": "uuid", "description": "Foreign key to users.user_id"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}, "required": ["organization_id", "name", "registration_number", "tpin", "email", "phone", "postal_address", "physical_address", "date_incorporation", "place_incorporation", "created_by", "created_at"], "additionalProperties": false}, "contact_persons": {"type": "object", "description": "Contact persons for organizations", "properties": {"contact_id": {"type": "string", "format": "uuid"}, "organization_id": {"type": "string", "format": "uuid", "description": "Foreign key to organizations.organization_id"}, "name": {"type": "string", "minLength": 1, "maxLength": 255}, "designation": {"type": "string", "minLength": 1, "maxLength": 255}, "email": {"type": "string", "format": "email"}, "phone": {"type": "string", "pattern": "^[0-9]{10}$"}, "website": {"type": ["string", "null"], "format": "uri"}, "is_primary": {"type": "boolean", "default": false}, "created_at": {"type": "string", "format": "date-time"}}, "required": ["contact_id", "organization_id", "name", "designation", "email", "phone", "created_at"], "additionalProperties": false}, "management_team": {"type": "object", "description": "Key management team members for organizations", "properties": {"member_id": {"type": "string", "format": "uuid"}, "organization_id": {"type": "string", "format": "uuid", "description": "Foreign key to organizations.organization_id"}, "name": {"type": "string", "minLength": 1, "maxLength": 255}, "position": {"type": "string", "minLength": 1, "maxLength": 255}, "qualifications": {"type": "string", "description": "Educational and professional qualifications"}, "experience": {"type": "string", "description": "Work experience details"}, "cv_document_id": {"type": ["string", "null"], "format": "uuid", "description": "Foreign key to documents.document_id"}, "created_at": {"type": "string", "format": "date-time"}}, "required": ["member_id", "organization_id", "name", "position", "created_at"], "additionalProperties": false}, "license_types": {"type": "object", "description": "Types of licenses available in the system", "properties": {"license_type_id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "minLength": 1, "maxLength": 255}, "code": {"type": "string", "minLength": 1, "maxLength": 50, "description": "Short code for license type (e.g., 'TV_BROADCAST', 'RADIO_CAMPUS')"}, "category": {"type": "string", "enum": ["international", "national", "regional", "district"]}, "service_type": {"type": "string", "enum": ["facilities", "network", "application", "content"]}, "description": {"type": "string"}, "validity_period_months": {"type": "integer", "minimum": 1, "description": "License validity period in months"}, "fee_amount": {"type": "number", "minimum": 0, "description": "License fee amount"}, "is_active": {"type": "boolean", "default": true}, "created_at": {"type": "string", "format": "date-time"}}, "required": ["license_type_id", "name", "code", "category", "service_type", "validity_period_months", "fee_amount", "created_at"], "additionalProperties": false}, "applications": {"type": "object", "description": "License applications submitted by organizations", "properties": {"application_id": {"type": "string", "format": "uuid"}, "application_number": {"type": "string", "pattern": "^[A-Z]{2,3}-[0-9]{4}-[0-9]{2,3}$", "description": "Human-readable application number (e.g., LIC-2024-001)"}, "organization_id": {"type": "string", "format": "uuid", "description": "Foreign key to organizations.organization_id"}, "license_type_id": {"type": "string", "format": "uuid", "description": "Foreign key to license_types.license_type_id"}, "status": {"type": "string", "enum": ["draft", "submitted", "under_review", "evaluation", "approved", "rejected", "withdrawn"], "default": "draft"}, "current_step": {"type": "integer", "minimum": 1, "maximum": 6, "description": "Current step in application process (1-6)"}, "progress_percentage": {"type": "integer", "minimum": 0, "maximum": 100}, "submitted_at": {"type": ["string", "null"], "format": "date-time"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}, "required": ["application_id", "application_number", "organization_id", "license_type_id", "status", "current_step", "created_at"], "additionalProperties": false}, "application_business_plan": {"type": "object", "description": "Business plan information for applications", "properties": {"business_plan_id": {"type": "string", "format": "uuid"}, "application_id": {"type": "string", "format": "uuid", "description": "Foreign key to applications.application_id"}, "volume_prices_description": {"type": "string", "description": "Projected volume of business and pricing strategy for first 5 years"}, "services_description": {"type": "string", "description": "Range of services to be provided and components"}, "years_in_operation": {"type": "string", "enum": ["0-3", "3+"], "description": "Number of years the business has been in operation"}, "created_at": {"type": "string", "format": "date-time"}}, "required": ["business_plan_id", "application_id", "volume_prices_description", "services_description", "years_in_operation", "created_at"], "additionalProperties": false}, "application_technical_details": {"type": "object", "description": "Technical capacity details for Individual License Form A applications", "properties": {"technical_id": {"type": "string", "format": "uuid"}, "application_id": {"type": "string", "format": "uuid", "description": "Foreign key to applications.application_id"}, "network_layout_description": {"type": ["string", "null"], "description": "Description of proposed network layout and business/transmission sites"}, "implementation_schedule": {"type": ["string", "null"], "description": "Implementation schedule and growth plan details"}, "disaster_recovery_plan": {"type": ["string", "null"], "description": "Disaster recovery plan details"}, "resource_requirements": {"type": ["string", "null"], "description": "Resource requirements including numbering and spectrum"}, "created_at": {"type": "string", "format": "date-time"}}, "required": ["technical_id", "application_id", "created_at"], "additionalProperties": false}, "application_isp_service_info": {"type": "object", "description": "ISP-specific service information for ISP license applications", "properties": {"isp_service_id": {"type": "string", "format": "uuid"}, "application_id": {"type": "string", "format": "uuid", "description": "Foreign key to applications.application_id"}, "service_category": {"type": "string", "enum": ["national", "regional", "district", "community"], "description": "ISP service category"}, "primary_service_type": {"type": "string", "enum": ["broadband", "fiber", "wireless", "satellite", "mobile"], "description": "Primary type of internet service"}, "coverage_area": {"type": "string", "enum": ["national", "regional", "urban", "rural", "mixed"], "description": "Proposed coverage area"}, "max_bandwidth_mbps": {"type": "integer", "minimum": 1, "description": "Maximum bandwidth capacity in Mbps"}, "expected_customers_year1": {"type": "integer", "minimum": 1, "description": "Expected number of customers in first year"}, "target_market": {"type": "string", "enum": ["residential", "business", "government", "education", "mixed"], "description": "Primary target market segment"}, "created_at": {"type": "string", "format": "date-time"}}, "required": ["isp_service_id", "application_id", "service_category", "primary_service_type", "coverage_area", "max_bandwidth_mbps", "expected_customers_year1", "target_market", "created_at"], "additionalProperties": false}, "application_isp_business_plan": {"type": "object", "description": "ISP-specific business plan information", "properties": {"isp_business_id": {"type": "string", "format": "uuid"}, "application_id": {"type": "string", "format": "uuid", "description": "Foreign key to applications.application_id"}, "isp_services_description": {"type": "string", "description": "Range of ISP services to be provided and components"}, "planned_start_date": {"type": "string", "format": "date", "description": "Planned service launch date"}, "total_investment_mwk": {"type": "number", "minimum": 0, "description": "Total investment amount in Malawi Kwacha"}, "planned_employees": {"type": "integer", "minimum": 1, "description": "Planned number of employees"}, "projected_annual_revenue_mwk": {"type": "number", "minimum": 0, "description": "Projected annual revenue in Malawi Kwacha"}, "created_at": {"type": "string", "format": "date-time"}}, "required": ["isp_business_id", "application_id", "isp_services_description", "planned_start_date", "total_investment_mwk", "planned_employees", "projected_annual_revenue_mwk", "created_at"], "additionalProperties": false}, "application_mobile_network_info": {"type": "object", "description": "Mobile network-specific information for mobile license applications", "properties": {"mobile_network_id": {"type": "string", "format": "uuid"}, "application_id": {"type": "string", "format": "uuid", "description": "Foreign key to applications.application_id"}, "network_category": {"type": "string", "enum": ["national", "regional", "district", "community"], "description": "Mobile network category"}, "primary_service_type": {"type": "string", "enum": ["2g", "3g", "4g", "5g", "mvno"], "description": "Primary type of mobile network service"}, "coverage_area": {"type": "string", "enum": ["national", "regional", "urban", "rural", "mixed"], "description": "Proposed coverage area"}, "spectrum_bands": {"type": "string", "enum": ["700mhz", "850mhz", "900mhz", "1800mhz", "2100mhz", "2600mhz", "multiple"], "description": "Required spectrum bands"}, "expected_subscribers_year1": {"type": "integer", "minimum": 1, "description": "Expected number of subscribers in first year"}, "target_market": {"type": "string", "enum": ["consumer", "enterprise", "government", "iot", "mixed"], "description": "Primary target market segment"}, "created_at": {"type": "string", "format": "date-time"}}, "required": ["mobile_network_id", "application_id", "network_category", "primary_service_type", "coverage_area", "spectrum_bands", "expected_subscribers_year1", "target_market", "created_at"], "additionalProperties": false}, "application_mobile_business_plan": {"type": "object", "description": "Mobile network-specific business plan information", "properties": {"mobile_business_id": {"type": "string", "format": "uuid"}, "application_id": {"type": "string", "format": "uuid", "description": "Foreign key to applications.application_id"}, "mobile_services_description": {"type": "string", "description": "Range of mobile network services to be provided and components"}, "planned_launch_date": {"type": "string", "format": "date", "description": "Planned network launch date"}, "total_investment_mwk": {"type": "number", "minimum": 0, "description": "Total investment amount in Malawi Kwacha"}, "planned_employees": {"type": "integer", "minimum": 1, "description": "Planned number of employees"}, "projected_annual_revenue_mwk": {"type": "number", "minimum": 0, "description": "Projected annual revenue in Malawi Kwacha"}, "created_at": {"type": "string", "format": "date-time"}}, "required": ["mobile_business_id", "application_id", "mobile_services_description", "planned_launch_date", "total_investment_mwk", "planned_employees", "projected_annual_revenue_mwk", "created_at"], "additionalProperties": false}, "application_postal_service_info": {"type": "object", "description": "Postal service-specific information for postal license applications", "properties": {"postal_service_id": {"type": "string", "format": "uuid"}, "application_id": {"type": "string", "format": "uuid", "description": "Foreign key to applications.application_id"}, "service_category": {"type": "string", "enum": ["mail", "courier", "express", "logistics"], "description": "Postal service category"}, "service_type": {"type": "string", "enum": ["collection", "sorting", "delivery", "integrated"], "description": "Type of postal service"}, "service_coverage": {"type": "string", "enum": ["national", "regional", "district", "local"], "description": "Service coverage area"}, "created_at": {"type": "string", "format": "date-time"}}, "required": ["postal_service_id", "application_id", "service_category", "service_type", "service_coverage", "created_at"], "additionalProperties": false}, "application_postal_business_plan": {"type": "object", "description": "Postal service-specific business plan information", "properties": {"postal_business_id": {"type": "string", "format": "uuid"}, "application_id": {"type": "string", "format": "uuid", "description": "Foreign key to applications.application_id"}, "postal_services_description": {"type": "string", "description": "Range of postal services to be provided and components"}, "service_areas_routes": {"type": "string", "description": "Proposed service areas and delivery routes"}, "created_at": {"type": "string", "format": "date-time"}}, "required": ["postal_business_id", "application_id", "postal_services_description", "service_areas_routes", "created_at"], "additionalProperties": false}, "application_radio_broadcasting_info": {"type": "object", "description": "Radio broadcasting-specific information for radio license applications", "properties": {"radio_broadcasting_id": {"type": "string", "format": "uuid"}, "application_id": {"type": "string", "format": "uuid", "description": "Foreign key to applications.application_id"}, "license_category": {"type": "string", "enum": ["national", "regional", "district", "community"], "description": "Radio broadcasting license category"}, "license_type": {"type": "string", "enum": ["commercial", "community", "educational", "religious", "campus"], "description": "Type of radio license"}, "broadcasting_service": {"type": "string", "enum": ["fm", "am", "digital", "internet"], "description": "Broadcasting service type"}, "created_at": {"type": "string", "format": "date-time"}}, "required": ["radio_broadcasting_id", "application_id", "license_category", "license_type", "broadcasting_service", "created_at"], "additionalProperties": false}, "application_radio_business_plan": {"type": "object", "description": "Radio broadcasting-specific business plan information", "properties": {"radio_business_id": {"type": "string", "format": "uuid"}, "application_id": {"type": "string", "format": "uuid", "description": "Foreign key to applications.application_id"}, "programming_content": {"type": "string", "description": "Programming content and format strategy"}, "coverage_area_market": {"type": "string", "description": "Coverage area and target market analysis"}, "revenue_model": {"type": "string", "description": "Revenue model and advertising strategy"}, "created_at": {"type": "string", "format": "date-time"}}, "required": ["radio_business_id", "application_id", "programming_content", "coverage_area_market", "revenue_model", "created_at"], "additionalProperties": false}, "application_satellite_service_info": {"type": "object", "description": "Satellite communications-specific information for satellite license applications", "properties": {"satellite_service_id": {"type": "string", "format": "uuid"}, "application_id": {"type": "string", "format": "uuid", "description": "Foreign key to applications.application_id"}, "license_category": {"type": "string", "enum": ["national", "regional", "district", "community"], "description": "Satellite license category"}, "license_type": {"type": "string", "enum": ["vsat", "satellite-internet", "earth-station", "satellite-gateway"], "description": "Type of satellite license"}, "created_at": {"type": "string", "format": "date-time"}}, "required": ["satellite_service_id", "application_id", "license_category", "license_type", "created_at"], "additionalProperties": false}, "application_satellite_business_plan": {"type": "object", "description": "Satellite communications-specific business plan information", "properties": {"satellite_business_id": {"type": "string", "format": "uuid"}, "application_id": {"type": "string", "format": "uuid", "description": "Foreign key to applications.application_id"}, "satellite_services_description": {"type": "string", "description": "Range of satellite communication services to be provided and components"}, "created_at": {"type": "string", "format": "date-time"}}, "required": ["satellite_business_id", "application_id", "satellite_services_description", "created_at"], "additionalProperties": false}, "application_tv_broadcasting_info": {"type": "object", "description": "TV broadcasting-specific information for TV license applications", "properties": {"tv_broadcasting_id": {"type": "string", "format": "uuid"}, "application_id": {"type": "string", "format": "uuid", "description": "Foreign key to applications.application_id"}, "license_category": {"type": "string", "enum": ["national", "regional", "district", "community"], "description": "TV broadcasting license category"}, "license_type": {"type": "string", "enum": ["terrestrial-tv", "digital-tv", "cable-tv", "iptv"], "description": "Type of TV broadcasting license"}, "created_at": {"type": "string", "format": "date-time"}}, "required": ["tv_broadcasting_id", "application_id", "license_category", "license_type", "created_at"], "additionalProperties": false}, "application_tv_business_plan": {"type": "object", "description": "TV broadcasting-specific business plan information", "properties": {"tv_business_id": {"type": "string", "format": "uuid"}, "application_id": {"type": "string", "format": "uuid", "description": "Foreign key to applications.application_id"}, "tv_services_description": {"type": "string", "description": "Range of TV broadcasting services to be provided and components"}, "created_at": {"type": "string", "format": "date-time"}}, "required": ["tv_business_id", "application_id", "tv_services_description", "created_at"], "additionalProperties": false}, "application_university_radio_info": {"type": "object", "description": "University radio-specific information for university radio license applications", "properties": {"university_radio_id": {"type": "string", "format": "uuid"}, "application_id": {"type": "string", "format": "uuid", "description": "Foreign key to applications.application_id"}, "proposed_station_name": {"type": "string", "description": "Proposed station name"}, "licence_type": {"type": "string", "enum": ["sound", "tv"], "description": "Type of licence (sound or TV broadcasting)"}, "sound_licence_type": {"type": ["string", "null"], "enum": ["fm", "am", "digital", "community", "campus"], "description": "Type of sound licence if applicable"}, "tv_licence_type": {"type": ["string", "null"], "enum": ["terrestrial", "satellite", "cable", "digital", "community"], "description": "Type of TV licence if applicable"}, "station_address": {"type": "string", "description": "Physical address where the broadcasting station will be located"}, "created_at": {"type": "string", "format": "date-time"}}, "required": ["university_radio_id", "application_id", "proposed_station_name", "licence_type", "station_address", "created_at"], "additionalProperties": false}, "application_university_radio_programming": {"type": "object", "description": "University radio programming and market research information", "properties": {"programming_id": {"type": "string", "format": "uuid"}, "application_id": {"type": "string", "format": "uuid", "description": "Foreign key to applications.application_id"}, "market_appeal_audience": {"type": "string", "description": "How the proposed programme service will cater for the demand and interests of its intended audience"}, "market_appeal_unique": {"type": "string", "description": "How the broadcasting service will be different from existing services in the coverage area"}, "market_research": {"type": "string", "description": "Market research findings and evidence of demand for the proposed service"}, "codes_operation": {"type": "string", "description": "Procedures for handling comments and complaints about the proposed service"}, "programme_format": {"type": "string", "enum": ["youth", "talk", "religious", "educational", "music", "general"], "description": "Programme format"}, "programme_approach": {"type": "string", "description": "Proposed broadcasting service approach"}, "programme_start": {"type": "string", "format": "time", "description": "Start time for programming"}, "programme_end": {"type": "string", "format": "time", "description": "End time for programming"}, "programme_peak": {"type": "string", "format": "time", "description": "Peak time for programming"}, "programme_sourcing": {"type": "string", "enum": ["external", "local"], "description": "Programme sourcing (external or locally generated)"}, "programme_objectives": {"type": "string", "description": "Expected broadcasting approach and objectives"}, "programme_benefit": {"type": "string", "description": "Expected broadcasting service benefit to the community"}, "programme_promote": {"type": "string", "description": "Strategy for promoting local Malawian content"}, "created_at": {"type": "string", "format": "date-time"}}, "required": ["programming_id", "application_id", "market_appeal_audience", "market_appeal_unique", "market_research", "codes_operation", "programme_format", "programme_approach", "programme_start", "programme_end", "programme_peak", "programme_sourcing", "programme_objectives", "programme_benefit", "programme_promote", "created_at"], "additionalProperties": false}, "application_university_radio_financial": {"type": "object", "description": "University radio financial capacity information", "properties": {"financial_id": {"type": "string", "format": "uuid"}, "application_id": {"type": "string", "format": "uuid", "description": "Foreign key to applications.application_id"}, "funding_source": {"type": "string", "enum": ["grants", "donations", "sponsorships", "advertising", "membership", "other"], "description": "Primary funding source"}, "created_at": {"type": "string", "format": "date-time"}}, "required": ["financial_id", "application_id", "funding_source", "created_at"], "additionalProperties": false}, "application_university_radio_technical": {"type": "object", "description": "University radio technical capacity information", "properties": {"technical_id": {"type": "string", "format": "uuid"}, "application_id": {"type": "string", "format": "uuid", "description": "Foreign key to applications.application_id"}, "transmission_sites": {"type": "string", "description": "Details about transmission sites and linking arrangements"}, "created_at": {"type": "string", "format": "date-time"}}, "required": ["technical_id", "application_id", "transmission_sites", "created_at"], "additionalProperties": false}, "application_university_radio_conclusion": {"type": "object", "description": "University radio application conclusion and declaration", "properties": {"conclusion_id": {"type": "string", "format": "uuid"}, "application_id": {"type": "string", "format": "uuid", "description": "Foreign key to applications.application_id"}, "motivation": {"type": "string", "description": "Why the Authority should grant the licence"}, "other_matters": {"type": ["string", "null"], "description": "Other matters the Authority should be aware of"}, "applicant_name": {"type": "string", "description": "Applicant full name for signature"}, "applicant_title": {"type": "string", "description": "Applicant title/position"}, "signature_date": {"type": "string", "format": "date", "description": "Date of signature"}, "commissioner_name": {"type": ["string", "null"], "description": "Commissioner of Oath name"}, "commissioner_date": {"type": ["string", "null"], "format": "date", "description": "Commissioner of Oath date"}, "commissioner_location": {"type": ["string", "null"], "description": "Location where oath was taken"}, "declaration_agreed": {"type": "boolean", "description": "Whether applicant agreed to the declaration"}, "accuracy_confirmed": {"type": "boolean", "description": "Whether applicant confirmed accuracy of information"}, "compliance_confirmed": {"type": "boolean", "description": "Whether applicant confirmed compliance with regulations"}, "submission_authorized": {"type": "boolean", "description": "Whether applicant authorized submission"}, "created_at": {"type": "string", "format": "date-time"}}, "required": ["conclusion_id", "application_id", "motivation", "applicant_name", "applicant_title", "signature_date", "declaration_agreed", "accuracy_confirmed", "compliance_confirmed", "submission_authorized", "created_at"], "additionalProperties": false}, "application_undertaking": {"type": "object", "description": "Undertaking and declaration details for applications", "properties": {"undertaking_id": {"type": "string", "format": "uuid"}, "application_id": {"type": "string", "format": "uuid", "description": "Foreign key to applications.application_id"}, "declaration_type": {"type": "string", "enum": ["I", "We"], "description": "Whether declaration is individual (I) or organizational (We)"}, "signed_name": {"type": "string", "description": "Name of the person signing the undertaking"}, "signed_date": {"type": "string", "format": "date", "description": "Date when undertaking was signed"}, "company_stamp_document_id": {"type": ["string", "null"], "format": "uuid", "description": "Foreign key to documents.document_id for company stamp"}, "evaluation_criteria_agreed": {"type": "boolean", "description": "Whether applicant agreed to evaluation criteria"}, "compliance_confirmed": {"type": "boolean", "description": "Whether applicant confirmed compliance with requirements"}, "created_at": {"type": "string", "format": "date-time"}}, "required": ["undertaking_id", "application_id", "declaration_type", "signed_name", "signed_date", "evaluation_criteria_agreed", "compliance_confirmed", "created_at"], "additionalProperties": false}, "documents": {"type": "object", "description": "File uploads and document storage", "properties": {"document_id": {"type": "string", "format": "uuid"}, "application_id": {"type": ["string", "null"], "format": "uuid", "description": "Foreign key to applications.application_id (null for non-application documents)"}, "document_type": {"type": "string", "enum": ["certificate_incorporation", "memorandum_association", "shareholding_structure", "partnership_agreement", "ownership_interest", "board_directors", "capital_expenditure", "financial_performance", "investment_appraisal", "market_assessment", "proof_financial", "audited_accounts", "technical_expertise", "technical_rollout", "technical_personnel", "network_layout", "implementation_schedule", "network_diagram", "disaster_recovery", "resource_requirements", "network_architecture", "financial_capacity", "financial_projections", "operational_infrastructure", "staffing_plan", "service_quality_standards", "security_safety_measures", "technology_systems", "broadcasting_equipment", "frequency_coverage_plan", "studio_transmission_setup", "satellite_network_layout", "satellite_system_architecture", "tv_broadcasting_network_layout", "tv_broadcasting_system_architecture", "programming_content_plan", "articles_association", "local_support_signatures", "editorial_policy", "business_plan", "proof_funding_source", "income_expenditure_statement", "existing_sites_documentation", "roll_out_plans", "company_stamp", "cv_document", "other"]}, "file_name": {"type": "string", "minLength": 1, "maxLength": 255}, "file_path": {"type": "string", "description": "Server file path or cloud storage URL"}, "file_size": {"type": "integer", "minimum": 0, "description": "File size in bytes"}, "mime_type": {"type": "string", "description": "MIME type of the file"}, "uploaded_by": {"type": "string", "format": "uuid", "description": "Foreign key to users.user_id"}, "is_required": {"type": "boolean", "default": false}, "created_at": {"type": "string", "format": "date-time"}}, "required": ["document_id", "document_type", "file_name", "file_path", "file_size", "mime_type", "uploaded_by", "created_at"], "additionalProperties": false}, "evaluations": {"type": "object", "description": "License application evaluations", "properties": {"evaluation_id": {"type": "string", "format": "uuid"}, "application_id": {"type": "string", "format": "uuid", "description": "Foreign key to applications.application_id"}, "evaluator_id": {"type": "string", "format": "uuid", "description": "Foreign key to users.user_id"}, "evaluation_type": {"type": "string", "enum": ["individual_license_a", "class_license_b", "network_service", "broadcasting", "campus_radio", "isp_license", "mobile_license", "postal_license", "radio_broadcasting", "satellite_communications", "tv_broadcasting", "university_radio"], "description": "Type of evaluation based on license form"}, "status": {"type": "string", "enum": ["draft", "completed", "approved", "rejected"], "default": "draft"}, "total_score": {"type": "number", "minimum": 0, "maximum": 100, "description": "Overall evaluation score as percentage"}, "recommendation": {"type": "string", "enum": ["approve", "conditional_approve", "reject"]}, "evaluator_notes": {"type": ["string", "null"], "description": "Additional notes from evaluator"}, "shareholding_compliant": {"type": ["boolean", "null"], "description": "Whether shareholding requirements are met"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "completed_at": {"type": ["string", "null"], "format": "date-time"}}, "required": ["evaluation_id", "application_id", "evaluator_id", "evaluation_type", "status", "created_at"], "additionalProperties": false}, "evaluation_criteria": {"type": "object", "description": "Individual evaluation criteria scores", "properties": {"criteria_id": {"type": "string", "format": "uuid"}, "evaluation_id": {"type": "string", "format": "uuid", "description": "Foreign key to evaluations.evaluation_id"}, "category": {"type": "string", "description": "Main evaluation category (e.g., 'business_plan', 'technical_capacity')"}, "subcategory": {"type": "string", "description": "Specific criterion within category"}, "score": {"type": "number", "minimum": 0, "maximum": 100, "description": "Score for this specific criterion"}, "weight": {"type": "number", "minimum": 0, "maximum": 1, "description": "Weight of this criterion in overall score"}, "max_marks": {"type": ["integer", "null"], "minimum": 0, "description": "Maximum marks for this criterion (for mark-based systems)"}, "awarded_marks": {"type": ["integer", "null"], "minimum": 0, "description": "Awarded marks for this criterion"}, "created_at": {"type": "string", "format": "date-time"}}, "required": ["criteria_id", "evaluation_id", "category", "subcategory", "score", "weight", "created_at"], "additionalProperties": false}, "licenses": {"type": "object", "description": "Issued licenses", "properties": {"license_id": {"type": "string", "format": "uuid"}, "license_number": {"type": "string", "pattern": "^LIC-[0-9]{4}-[0-9]{2}-[0-9]{3}$", "description": "Human-readable license number (e.g., LIC-2024-10-001)"}, "application_id": {"type": "string", "format": "uuid", "description": "Foreign key to applications.application_id"}, "organization_id": {"type": "string", "format": "uuid", "description": "Foreign key to organizations.organization_id"}, "license_type_id": {"type": "string", "format": "uuid", "description": "Foreign key to license_types.license_type_id"}, "status": {"type": "string", "enum": ["active", "expired", "suspended", "revoked", "under_review"], "default": "active"}, "issue_date": {"type": "string", "format": "date"}, "expiry_date": {"type": "string", "format": "date"}, "issued_by": {"type": "string", "format": "uuid", "description": "Foreign key to users.user_id"}, "conditions": {"type": ["string", "null"], "description": "Special conditions attached to the license"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}, "required": ["license_id", "license_number", "application_id", "organization_id", "license_type_id", "status", "issue_date", "expiry_date", "issued_by", "created_at"], "additionalProperties": false}, "transactions": {"type": "object", "description": "Financial transactions for license fees and payments", "properties": {"transaction_id": {"type": "string", "format": "uuid"}, "transaction_number": {"type": "string", "description": "Human-readable transaction reference"}, "application_id": {"type": ["string", "null"], "format": "uuid", "description": "Foreign key to applications.application_id"}, "license_id": {"type": ["string", "null"], "format": "uuid", "description": "Foreign key to licenses.license_id"}, "organization_id": {"type": "string", "format": "uuid", "description": "Foreign key to organizations.organization_id"}, "transaction_type": {"type": "string", "enum": ["application_fee", "license_fee", "renewal_fee", "penalty", "refund"]}, "amount": {"type": "number", "minimum": 0, "description": "Transaction amount"}, "currency": {"type": "string", "default": "MWK", "description": "Currency code"}, "status": {"type": "string", "enum": ["pending", "completed", "failed", "cancelled", "refunded"], "default": "pending"}, "payment_method": {"type": ["string", "null"], "enum": ["bank_transfer", "mobile_money", "cash", "cheque", "online"], "description": "Payment method used"}, "reference_number": {"type": ["string", "null"], "description": "External payment reference"}, "description": {"type": "string", "description": "Transaction description"}, "created_at": {"type": "string", "format": "date-time"}, "completed_at": {"type": ["string", "null"], "format": "date-time"}}, "required": ["transaction_id", "transaction_number", "organization_id", "transaction_type", "amount", "currency", "status", "description", "created_at"], "additionalProperties": false}, "audit_trail": {"type": "object", "description": "System audit trail for tracking changes and actions", "properties": {"audit_id": {"type": "string", "format": "uuid"}, "user_id": {"type": "string", "format": "uuid", "description": "Foreign key to users.user_id"}, "action": {"type": "string", "enum": ["create", "update", "delete", "view", "approve", "reject", "submit", "login", "logout"], "description": "Action performed"}, "module": {"type": "string", "enum": ["license", "spectrum", "transaction", "user", "evaluation", "application"], "description": "System module affected"}, "entity_type": {"type": "string", "description": "Type of entity affected (e.g., 'application', 'license', 'user')"}, "entity_id": {"type": ["string", "null"], "format": "uuid", "description": "ID of the affected entity"}, "old_values": {"type": ["object", "null"], "description": "Previous values before change (JSON object)"}, "new_values": {"type": ["object", "null"], "description": "New values after change (JSON object)"}, "ip_address": {"type": ["string", "null"], "description": "IP address of the user"}, "user_agent": {"type": ["string", "null"], "description": "Browser user agent string"}, "description": {"type": "string", "description": "Human-readable description of the action"}, "created_at": {"type": "string", "format": "date-time"}}, "required": ["audit_id", "user_id", "action", "module", "entity_type", "description", "created_at"], "additionalProperties": false}, "notifications": {"type": "object", "description": "System notifications for users", "properties": {"notification_id": {"type": "string", "format": "uuid"}, "user_id": {"type": "string", "format": "uuid", "description": "Foreign key to users.user_id"}, "type": {"type": "string", "enum": ["application_status", "evaluation_assigned", "payment_due", "license_expiry", "system_alert"], "description": "Type of notification"}, "title": {"type": "string", "minLength": 1, "maxLength": 255, "description": "Notification title"}, "message": {"type": "string", "description": "Notification message content"}, "is_read": {"type": "boolean", "default": false}, "priority": {"type": "string", "enum": ["low", "medium", "high", "urgent"], "default": "medium"}, "related_entity_type": {"type": ["string", "null"], "description": "Type of related entity (e.g., 'application', 'license')"}, "related_entity_id": {"type": ["string", "null"], "format": "uuid", "description": "ID of related entity"}, "action_url": {"type": ["string", "null"], "description": "URL for notification action"}, "expires_at": {"type": ["string", "null"], "format": "date-time", "description": "When notification expires"}, "created_at": {"type": "string", "format": "date-time"}, "read_at": {"type": ["string", "null"], "format": "date-time"}}, "required": ["notification_id", "user_id", "type", "title", "message", "created_at"], "additionalProperties": false}}, "relationships": {"description": "Entity relationships in the MACRA portal database", "users_organizations": "One-to-Many: users.user_id -> organizations.created_by", "organizations_contact_persons": "One-to-Many: organizations.organization_id -> contact_persons.organization_id", "organizations_management_team": "One-to-Many: organizations.organization_id -> management_team.organization_id", "organizations_applications": "One-to-Many: organizations.organization_id -> applications.organization_id", "license_types_applications": "One-to-Many: license_types.license_type_id -> applications.license_type_id", "applications_business_plan": "One-to-One: applications.application_id -> application_business_plan.application_id", "applications_technical_details": "One-to-One: applications.application_id -> application_technical_details.application_id", "applications_isp_service_info": "One-to-One: applications.application_id -> application_isp_service_info.application_id", "applications_isp_business_plan": "One-to-One: applications.application_id -> application_isp_business_plan.application_id", "applications_mobile_network_info": "One-to-One: applications.application_id -> application_mobile_network_info.application_id", "applications_mobile_business_plan": "One-to-One: applications.application_id -> application_mobile_business_plan.application_id", "applications_postal_service_info": "One-to-One: applications.application_id -> application_postal_service_info.application_id", "applications_postal_business_plan": "One-to-One: applications.application_id -> application_postal_business_plan.application_id", "applications_radio_broadcasting_info": "One-to-One: applications.application_id -> application_radio_broadcasting_info.application_id", "applications_radio_business_plan": "One-to-One: applications.application_id -> application_radio_business_plan.application_id", "applications_satellite_service_info": "One-to-One: applications.application_id -> application_satellite_service_info.application_id", "applications_satellite_business_plan": "One-to-One: applications.application_id -> application_satellite_business_plan.application_id", "applications_tv_broadcasting_info": "One-to-One: applications.application_id -> application_tv_broadcasting_info.application_id", "applications_tv_business_plan": "One-to-One: applications.application_id -> application_tv_business_plan.application_id", "applications_university_radio_info": "One-to-One: applications.application_id -> application_university_radio_info.application_id", "applications_university_radio_programming": "One-to-One: applications.application_id -> application_university_radio_programming.application_id", "applications_university_radio_financial": "One-to-One: applications.application_id -> application_university_radio_financial.application_id", "applications_university_radio_technical": "One-to-One: applications.application_id -> application_university_radio_technical.application_id", "applications_university_radio_conclusion": "One-to-One: applications.application_id -> application_university_radio_conclusion.application_id", "applications_undertaking": "One-to-One: applications.application_id -> application_undertaking.application_id", "applications_documents": "One-to-Many: applications.application_id -> documents.application_id", "applications_evaluations": "One-to-Many: applications.application_id -> evaluations.application_id", "applications_licenses": "One-to-One: applications.application_id -> licenses.application_id", "users_evaluations": "One-to-Many: users.user_id -> evaluations.evaluator_id", "evaluations_criteria": "One-to-Many: evaluations.evaluation_id -> evaluation_criteria.evaluation_id", "documents_management_team": "One-to-One: documents.document_id -> management_team.cv_document_id", "documents_company_stamp": "One-to-One: documents.document_id -> application_undertaking.company_stamp_document_id", "users_documents": "One-to-Many: users.user_id -> documents.uploaded_by", "organizations_transactions": "One-to-Many: organizations.organization_id -> transactions.organization_id", "applications_transactions": "One-to-Many: applications.application_id -> transactions.application_id", "licenses_transactions": "One-to-Many: licenses.license_id -> transactions.license_id", "users_audit_trail": "One-to-Many: users.user_id -> audit_trail.user_id", "users_notifications": "One-to-Many: users.user_id -> notifications.user_id"}, "form_mappings": {"description": "Mapping of Individual License Form A and Form B fields to database entities", "individual_license_form_a": {"description": "Individual License Form A (6 steps) - 70% minimum score required"}, "step_1_license_selection": {"license_category": "applications.license_type_id -> license_types.category", "license_type": "applications.license_type_id -> license_types.service_type"}, "step_2_applicant_details": {"applicant_name": "organizations.name", "applicant_profile": "organizations.profile_description", "applicant_website": "organizations.website", "applicant_email": "organizations.email", "applicant_phone": "organizations.phone", "applicant_fax": "organizations.fax", "applicant_reg_no": "organizations.registration_number", "applicant_tpin": "organizations.tpin", "applicant_postal": "organizations.postal_address", "applicant_physical": "organizations.physical_address", "applicant_contact": "contact_persons.name", "applicant_contact_designation": "contact_persons.designation", "applicant_contact_phone": "contact_persons.phone", "applicant_contact_email": "contact_persons.email", "applicant_contact_website": "contact_persons.website", "applicant_date_incorporation": "organizations.date_incorporation", "applicant_place_incorporation": "organizations.place_incorporation", "management_team_count": "management_team (count)", "management_team_members": "management_team (multiple records)"}, "step_3_business_plan": {"business_volume_prices": "application_business_plan.volume_prices_description", "business_services": "application_business_plan.services_description", "business_years": "application_business_plan.years_in_operation", "business_documents": "documents (multiple with specific document_types)"}, "step_4_technical_capacity": {"network_layout_description": "application_technical_details.network_layout_description", "implementation_schedule": "application_technical_details.implementation_schedule", "disaster_recovery_plan": "application_technical_details.disaster_recovery_plan", "resource_requirements": "application_technical_details.resource_requirements", "technical_documents": "documents (multiple with technical document_types)"}, "step_5_evaluation_criteria": {"agree_evaluation_criteria": "application_undertaking.evaluation_criteria_agreed"}, "step_6_undertaking": {"declaration_type": "application_undertaking.declaration_type", "signed_name": "application_undertaking.signed_name", "signed_date": "application_undertaking.signed_date", "company_stamp": "application_undertaking.company_stamp_document_id -> documents.document_id", "confirm_compliance": "application_undertaking.compliance_confirmed"}, "individual_license_form_b": {"description": "Individual License Form B (5 steps) - Class License - 55% minimum score required", "step_1_license_selection": {"license_category": "applications.license_type_id -> license_types.category", "license_type": "applications.license_type_id -> license_types.service_type"}, "step_2_applicant_details": {"applicant_name": "organizations.name", "applicant_profile": "organizations.profile_description", "applicant_website": "organizations.website", "applicant_email": "organizations.email", "applicant_phone": "organizations.phone", "applicant_fax": "organizations.fax", "applicant_reg_no": "organizations.registration_number", "applicant_tpin": "organizations.tpin", "applicant_postal": "organizations.postal_address", "applicant_physical": "organizations.physical_address", "applicant_contact": "contact_persons.name", "applicant_contact_designation": "contact_persons.designation", "applicant_contact_phone": "contact_persons.phone", "applicant_contact_email": "contact_persons.email", "applicant_contact_website": "contact_persons.website", "applicant_date_incorporation": "organizations.date_incorporation", "applicant_place_incorporation": "organizations.place_incorporation", "legal_constitution": "documents (document_type: certificate_incorporation)", "legal_memorandum": "documents (document_type: memorandum_association)"}, "step_3_business_plan": {"business_services": "application_business_plan.services_description", "business_market_assessment": "documents (document_type: market_assessment)", "business_proof_financial": "documents (document_type: proof_financial)"}, "step_4_technical_plan": {"technical_rollout": "documents (document_type: technical_rollout)", "technical_personnel": "documents (document_type: technical_personnel)", "technical_network_layout": "documents (document_type: network_layout)", "technical_implementation": "documents (document_type: implementation_schedule)", "technical_network": "documents (document_type: network_diagram)", "technical_resources": "documents (document_type: resource_requirements)"}, "step_5_evaluation_undertaking": {"agree_evaluation_criteria": "application_undertaking.evaluation_criteria_agreed", "declaration_type": "application_undertaking.declaration_type", "signed_name": "application_undertaking.signed_name", "signed_date": "application_undertaking.signed_date", "company_stamp": "application_undertaking.company_stamp_document_id -> documents.document_id", "confirm_compliance": "application_undertaking.compliance_confirmed"}}, "mobile_license_application": {"description": "Mobile Network License Application (5 steps) - 55% minimum score required", "step_1_license_selection": {"license_category": "applications.license_type_id -> license_types.category", "license_type": "applications.license_type_id -> license_types.service_type"}, "step_2_applicant_details": {"company_name": "organizations.name", "company_profile": "organizations.profile_description", "company_website": "organizations.website", "company_email": "organizations.email", "company_phone": "organizations.phone", "company_fax": "organizations.fax", "company_reg_no": "organizations.registration_number", "company_tpin": "organizations.tpin", "company_postal": "organizations.postal_address", "company_physical": "organizations.physical_address", "contact_name": "contact_persons.name", "contact_designation": "contact_persons.designation", "contact_phone": "contact_persons.phone", "contact_email": "contact_persons.email", "date_incorporation": "organizations.date_incorporation", "place_incorporation": "organizations.place_incorporation"}, "step_3_mobile_network_info": {"network_category": "application_mobile_network_info.network_category", "primary_service_type": "application_mobile_network_info.primary_service_type", "coverage_area": "application_mobile_network_info.coverage_area", "spectrum_bands": "application_mobile_network_info.spectrum_bands", "expected_subscribers_year1": "application_mobile_network_info.expected_subscribers_year1", "target_market": "application_mobile_network_info.target_market"}, "step_4_business_plan": {"mobile_services_description": "application_mobile_business_plan.mobile_services_description", "planned_launch_date": "application_mobile_business_plan.planned_launch_date", "total_investment_mwk": "application_mobile_business_plan.total_investment_mwk", "planned_employees": "application_mobile_business_plan.planned_employees", "projected_annual_revenue_mwk": "application_mobile_business_plan.projected_annual_revenue_mwk", "business_documents": "documents (multiple with mobile-specific document_types)"}, "step_5_evaluation_undertaking": {"agree_evaluation_criteria": "application_undertaking.evaluation_criteria_agreed", "declaration_type": "application_undertaking.declaration_type", "signed_name": "application_undertaking.signed_name", "signed_date": "application_undertaking.signed_date", "company_stamp": "application_undertaking.company_stamp_document_id -> documents.document_id", "confirm_compliance": "application_undertaking.compliance_confirmed"}}, "radio_broadcasting_license_application": {"description": "Radio Broadcasting License Application (6 steps) - 60% minimum score required", "step_1_license_selection": {"license_category": "application_radio_broadcasting_info.license_category", "license_type": "application_radio_broadcasting_info.license_type", "broadcasting_service": "application_radio_broadcasting_info.broadcasting_service"}, "step_2_applicant_details": {"applicant_name": "organizations.name", "applicant_profile": "organizations.profile_description", "applicant_website": "organizations.website", "applicant_email": "organizations.email", "applicant_phone": "organizations.phone", "applicant_fax": "organizations.fax", "applicant_reg_no": "organizations.registration_number", "applicant_tpin": "organizations.tpin", "applicant_postal": "organizations.postal_address", "applicant_physical": "organizations.physical_address", "applicant_contact": "contact_persons.name", "applicant_contact_designation": "contact_persons.designation", "applicant_contact_phone": "contact_persons.phone", "applicant_contact_email": "contact_persons.email", "applicant_contact_website": "contact_persons.website", "applicant_date_incorporation": "organizations.date_incorporation", "applicant_place_incorporation": "organizations.place_incorporation", "legal_constitution": "documents (document_type: certificate_incorporation)", "legal_memorandum": "documents (document_type: memorandum_association)"}, "step_3_business_plan": {"business_programming": "application_radio_business_plan.programming_content", "business_coverage": "application_radio_business_plan.coverage_area_market", "business_revenue": "application_radio_business_plan.revenue_model", "business_financial_projections": "documents (document_type: financial_projections)", "business_market_assessment": "documents (document_type: market_assessment)", "business_proof_financial": "documents (document_type: proof_financial)"}, "step_4_technical_capacity": {"technical_equipment": "documents (document_type: broadcasting_equipment)", "technical_frequency": "documents (document_type: frequency_coverage_plan)", "technical_studio": "documents (document_type: studio_transmission_setup)", "technical_personnel": "documents (document_type: technical_personnel)", "technical_implementation": "documents (document_type: implementation_schedule)"}, "step_5_evaluation_criteria": {"agree_evaluation_criteria": "application_undertaking.evaluation_criteria_agreed"}, "step_6_undertaking": {"i_we": "application_undertaking.declaration_type", "signed_name": "application_undertaking.signed_name", "current_date": "application_undertaking.signed_date", "applicant_stamp": "application_undertaking.company_stamp_document_id -> documents.document_id", "confirm_compliance": "application_undertaking.compliance_confirmed"}}, "satellite_communications_license_application": {"description": "Satellite Communications License Application (5 steps) - 55% minimum score required", "step_1_license_selection": {"license_category": "application_satellite_service_info.license_category", "license_type": "application_satellite_service_info.license_type"}, "step_2_applicant_details": {"applicant_name": "organizations.name", "applicant_profile": "organizations.profile_description", "applicant_website": "organizations.website", "applicant_email": "organizations.email", "applicant_phone": "organizations.phone", "applicant_fax": "organizations.fax", "applicant_reg_no": "organizations.registration_number", "applicant_tpin": "organizations.tpin", "applicant_postal": "organizations.postal_address", "applicant_physical": "organizations.physical_address", "applicant_contact": "contact_persons.name", "applicant_contact_designation": "contact_persons.designation", "applicant_contact_phone": "contact_persons.phone", "applicant_contact_email": "contact_persons.email", "applicant_contact_website": "contact_persons.website", "applicant_date_incorporation": "organizations.date_incorporation", "applicant_place_incorporation": "organizations.place_incorporation", "legal_constitution": "documents (document_type: certificate_incorporation)", "legal_memorandum": "documents (document_type: memorandum_association)"}, "step_3_business_plan": {"business_services": "application_satellite_business_plan.satellite_services_description", "business_market_assessment": "documents (document_type: market_assessment)", "business_proof_financial": "documents (document_type: proof_financial)"}, "step_4_operational_technical_plan": {"technical_rollout": "documents (document_type: technical_rollout)", "technical_personnel": "documents (document_type: technical_personnel)", "technical_network_layout": "documents (document_type: satellite_network_layout)", "technical_implementation": "documents (document_type: implementation_schedule)", "technical_network": "documents (document_type: satellite_system_architecture)", "technical_resources": "documents (document_type: resource_requirements)"}, "step_5_evaluation_undertaking": {"agree_evaluation_criteria": "application_undertaking.evaluation_criteria_agreed", "i_we": "application_undertaking.declaration_type", "signed_name": "application_undertaking.signed_name", "current_date": "application_undertaking.signed_date", "applicant_stamp": "application_undertaking.company_stamp_document_id -> documents.document_id", "confirm_compliance": "application_undertaking.compliance_confirmed"}}, "tv_broadcasting_license_application": {"description": "TV Broadcasting License Application (5 steps) - 55% minimum score required", "step_1_license_selection": {"license_category": "application_tv_broadcasting_info.license_category", "license_type": "application_tv_broadcasting_info.license_type"}, "step_2_applicant_details": {"applicant_name": "organizations.name", "applicant_profile": "organizations.profile_description", "applicant_website": "organizations.website", "applicant_email": "organizations.email", "applicant_phone": "organizations.phone", "applicant_fax": "organizations.fax", "applicant_reg_no": "organizations.registration_number", "applicant_tpin": "organizations.tpin", "applicant_postal": "organizations.postal_address", "applicant_physical": "organizations.physical_address", "applicant_contact": "contact_persons.name", "applicant_contact_designation": "contact_persons.designation", "applicant_contact_phone": "contact_persons.phone", "applicant_contact_email": "contact_persons.email", "applicant_contact_website": "contact_persons.website", "applicant_date_incorporation": "organizations.date_incorporation", "applicant_place_incorporation": "organizations.place_incorporation", "legal_constitution": "documents (document_type: certificate_incorporation)", "legal_memorandum": "documents (document_type: memorandum_association)"}, "step_3_business_plan": {"business_services": "application_tv_business_plan.tv_services_description", "business_market_assessment": "documents (document_type: market_assessment)", "business_proof_financial": "documents (document_type: proof_financial)"}, "step_4_operational_technical_plan": {"technical_rollout": "documents (document_type: technical_rollout)", "technical_personnel": "documents (document_type: technical_personnel)", "technical_network_layout": "documents (document_type: tv_broadcasting_network_layout)", "technical_implementation": "documents (document_type: implementation_schedule)", "technical_network": "documents (document_type: tv_broadcasting_system_architecture)", "technical_resources": "documents (document_type: resource_requirements)", "technical_programming": "documents (document_type: programming_content_plan)"}, "step_5_evaluation_undertaking": {"agree_evaluation_criteria": "application_undertaking.evaluation_criteria_agreed", "i_we": "application_undertaking.declaration_type", "signed_name": "application_undertaking.signed_name", "current_date": "application_undertaking.signed_date", "applicant_stamp": "application_undertaking.company_stamp_document_id -> documents.document_id", "confirm_compliance": "application_undertaking.compliance_confirmed"}}, "university_radio_license_application": {"description": "University Radio License Application (5 steps) - Declaration-based application", "step_1_license_selection": {"proposed_station_name": "application_university_radio_info.proposed_station_name", "licence_type": "application_university_radio_info.licence_type", "sound_licence_type": "application_university_radio_info.sound_licence_type", "tv_licence_type": "application_university_radio_info.tv_licence_type", "station_address": "application_university_radio_info.station_address"}, "step_2_applicant_details": {"applicant_name": "organizations.name", "applicant_profile": "organizations.profile_description", "applicant_website": "organizations.website", "applicant_email": "organizations.email", "applicant_phone": "organizations.phone", "applicant_fax": "organizations.fax", "applicant_reg_no": "organizations.registration_number", "applicant_tpin": "organizations.tpin", "applicant_postal": "organizations.postal_address", "applicant_physical": "organizations.physical_address", "applicant_contact": "contact_persons.name", "applicant_contact_designation": "contact_persons.designation", "applicant_contact_phone": "contact_persons.phone", "applicant_contact_email": "contact_persons.email", "applicant_contact_website": "contact_persons.website", "applicant_date_incorporation": "organizations.date_incorporation", "legal_constitution": "documents (document_type: certificate_incorporation)", "legal_memorandum": "documents (document_type: memorandum_association)", "legal_articles": "documents (document_type: articles_association)"}, "step_3_programming_market_research": {"market_appeal_audience": "application_university_radio_programming.market_appeal_audience", "market_appeal_unique": "application_university_radio_programming.market_appeal_unique", "market_research": "application_university_radio_programming.market_research", "local_support_signatures": "documents (document_type: local_support_signatures)", "codes_operation": "application_university_radio_programming.codes_operation", "programme_format": "application_university_radio_programming.programme_format", "programme_approach": "application_university_radio_programming.programme_approach", "programme_start": "application_university_radio_programming.programme_start", "programme_end": "application_university_radio_programming.programme_end", "programme_peak": "application_university_radio_programming.programme_peak", "programme_sourcing": "application_university_radio_programming.programme_sourcing", "programme_objectives": "application_university_radio_programming.programme_objectives", "programme_benefit": "application_university_radio_programming.programme_benefit", "programme_promote": "application_university_radio_programming.programme_promote", "editorial_policy": "documents (document_type: editorial_policy)"}, "step_4_financial_technical_capacity": {"business_plan": "documents (document_type: business_plan)", "funding_source": "application_university_radio_financial.funding_source", "proof_funding_source": "documents (document_type: proof_funding_source)", "income_expenditure_statement": "documents (document_type: income_expenditure_statement)", "technical_personnel": "documents (document_type: technical_personnel)", "transmission_sites": "application_university_radio_technical.transmission_sites", "existing_sites_documentation": "documents (document_type: existing_sites_documentation)", "roll_out_plans": "documents (document_type: roll_out_plans)"}, "step_5_conclusion_declaration": {"motivation": "application_university_radio_conclusion.motivation", "other_matters": "application_university_radio_conclusion.other_matters", "declaration_agreed": "application_university_radio_conclusion.declaration_agreed", "applicant_name": "application_university_radio_conclusion.applicant_name", "applicant_title": "application_university_radio_conclusion.applicant_title", "signature_date": "application_university_radio_conclusion.signature_date", "commissioner_name": "application_university_radio_conclusion.commissioner_name", "commissioner_date": "application_university_radio_conclusion.commissioner_date", "commissioner_location": "application_university_radio_conclusion.commissioner_location", "accuracy_confirmed": "application_university_radio_conclusion.accuracy_confirmed", "compliance_confirmed": "application_university_radio_conclusion.compliance_confirmed", "submission_authorized": "application_university_radio_conclusion.submission_authorized"}}}, "evaluation_mappings": {"description": "Mapping of Individual License Form A and Class License Form B evaluation criteria to database", "individual_license_form_a": {"description": "Individual License Form A evaluation criteria - 70% minimum score required"}, "financial_capacity_20_percent": {"financial_documents": "evaluation_criteria (category: financial_capacity, subcategory: financial_documents)", "capital_adequacy": "evaluation_criteria (category: financial_capacity, subcategory: capital_adequacy)", "financial_projections": "evaluation_criteria (category: financial_capacity, subcategory: financial_projections)", "credit_worthiness": "evaluation_criteria (category: financial_capacity, subcategory: credit_worthiness)"}, "business_plan_20_percent": {"market_analysis": "evaluation_criteria (category: business_plan, subcategory: market_analysis)", "business_model": "evaluation_criteria (category: business_plan, subcategory: business_model)", "revenue_projections": "evaluation_criteria (category: business_plan, subcategory: revenue_projections)", "growth_strategy": "evaluation_criteria (category: business_plan, subcategory: growth_strategy)"}, "technical_operational_40_percent": {"technical_expertise": "evaluation_criteria (category: technical_capacity, subcategory: technical_expertise)", "network_design": "evaluation_criteria (category: technical_capacity, subcategory: network_design)", "implementation_plan": "evaluation_criteria (category: technical_capacity, subcategory: implementation_plan)", "operational_readiness": "evaluation_criteria (category: technical_capacity, subcategory: operational_readiness)"}, "organization_setup_10_percent": {"management_structure": "evaluation_criteria (category: organization_setup, subcategory: management_structure)", "governance": "evaluation_criteria (category: organization_setup, subcategory: governance)", "compliance_framework": "evaluation_criteria (category: organization_setup, subcategory: compliance_framework)"}, "socio_economic_10_percent": {"job_creation": "evaluation_criteria (category: socio_economic, subcategory: job_creation)", "local_content": "evaluation_criteria (category: socio_economic, subcategory: local_content)", "community_impact": "evaluation_criteria (category: socio_economic, subcategory: community_impact)"}, "class_license_form_b": {"description": "Class License Form B evaluation criteria - 55% minimum score required", "business_plan_40_percent": {"market_analysis_strategy": "evaluation_criteria (category: business_plan, subcategory: market_analysis_strategy)", "financial_projections_viability": "evaluation_criteria (category: business_plan, subcategory: financial_projections_viability)", "service_offerings_innovation": "evaluation_criteria (category: business_plan, subcategory: service_offerings_innovation)", "risk_management_mitigation": "evaluation_criteria (category: business_plan, subcategory: risk_management_mitigation)"}, "technical_operational_50_percent": {"technical_infrastructure_equipment": "evaluation_criteria (category: technical_capacity, subcategory: technical_infrastructure_equipment)", "network_design_architecture": "evaluation_criteria (category: technical_capacity, subcategory: network_design_architecture)", "operational_procedures_maintenance": "evaluation_criteria (category: technical_capacity, subcategory: operational_procedures_maintenance)", "quality_assurance_standards": "evaluation_criteria (category: technical_capacity, subcategory: quality_assurance_standards)", "security_compliance_measures": "evaluation_criteria (category: technical_capacity, subcategory: security_compliance_measures)"}, "organization_setup_10_percent": {"corporate_structure_governance": "evaluation_criteria (category: organization_setup, subcategory: corporate_structure_governance)", "management_team_expertise": "evaluation_criteria (category: organization_setup, subcategory: management_team_expertise)", "legal_compliance_documentation": "evaluation_criteria (category: organization_setup, subcategory: legal_compliance_documentation)"}}, "mobile_license": {"description": "Mobile Network License evaluation criteria - 55% minimum score required", "business_plan_40_percent": {"market_analysis_strategy": "evaluation_criteria (category: business_plan, subcategory: market_analysis_strategy)", "financial_projections_viability": "evaluation_criteria (category: business_plan, subcategory: financial_projections_viability)", "service_offerings_innovation": "evaluation_criteria (category: business_plan, subcategory: service_offerings_innovation)", "investment_commitment": "evaluation_criteria (category: business_plan, subcategory: investment_commitment)"}, "technical_operational_50_percent": {"network_infrastructure_design": "evaluation_criteria (category: technical_capacity, subcategory: network_infrastructure_design)", "spectrum_utilization_efficiency": "evaluation_criteria (category: technical_capacity, subcategory: spectrum_utilization_efficiency)", "coverage_rollout_plan": "evaluation_criteria (category: technical_capacity, subcategory: coverage_rollout_plan)", "technical_personnel_expertise": "evaluation_criteria (category: technical_capacity, subcategory: technical_personnel_expertise)", "quality_service_standards": "evaluation_criteria (category: technical_capacity, subcategory: quality_service_standards)"}, "organization_setup_10_percent": {"corporate_structure_governance": "evaluation_criteria (category: organization_setup, subcategory: corporate_structure_governance)", "management_team_experience": "evaluation_criteria (category: organization_setup, subcategory: management_team_experience)", "legal_compliance_framework": "evaluation_criteria (category: organization_setup, subcategory: legal_compliance_framework)"}}, "radio_broadcasting": {"description": "Radio Broadcasting License evaluation criteria - 60% minimum score required", "business_plan_programming_35_percent": {"programming_content_strategy": "evaluation_criteria (category: business_plan, subcategory: programming_content_strategy)", "target_audience_analysis": "evaluation_criteria (category: business_plan, subcategory: target_audience_analysis)", "market_coverage_strategy": "evaluation_criteria (category: business_plan, subcategory: market_coverage_strategy)", "revenue_advertising_model": "evaluation_criteria (category: business_plan, subcategory: revenue_advertising_model)"}, "technical_capacity_infrastructure_30_percent": {"broadcasting_equipment_specifications": "evaluation_criteria (category: technical_capacity, subcategory: broadcasting_equipment_specifications)", "frequency_allocation_plan": "evaluation_criteria (category: technical_capacity, subcategory: frequency_allocation_plan)", "studio_transmission_setup": "evaluation_criteria (category: technical_capacity, subcategory: studio_transmission_setup)", "technical_personnel_qualifications": "evaluation_criteria (category: technical_capacity, subcategory: technical_personnel_qualifications)", "implementation_rollout_schedule": "evaluation_criteria (category: technical_capacity, subcategory: implementation_rollout_schedule)"}, "financial_capacity_20_percent": {"financial_projections_viability": "evaluation_criteria (category: financial_capacity, subcategory: financial_projections_viability)", "proof_financial_resources": "evaluation_criteria (category: financial_capacity, subcategory: proof_financial_resources)", "market_assessment_analysis": "evaluation_criteria (category: financial_capacity, subcategory: market_assessment_analysis)"}, "organization_setup_management_15_percent": {"corporate_structure_governance": "evaluation_criteria (category: organization_setup, subcategory: corporate_structure_governance)", "management_team_experience": "evaluation_criteria (category: organization_setup, subcategory: management_team_experience)", "legal_compliance_documentation": "evaluation_criteria (category: organization_setup, subcategory: legal_compliance_documentation)"}}, "satellite_communications": {"description": "Satellite Communications License evaluation criteria - 55% minimum score required", "business_plan_40_percent": {"satellite_services_strategy": "evaluation_criteria (category: business_plan, subcategory: satellite_services_strategy)", "market_assessment_analysis": "evaluation_criteria (category: business_plan, subcategory: market_assessment_analysis)", "financial_capacity_proof": "evaluation_criteria (category: business_plan, subcategory: financial_capacity_proof)", "business_viability_model": "evaluation_criteria (category: business_plan, subcategory: business_viability_model)"}, "technical_operational_50_percent": {"satellite_system_architecture": "evaluation_criteria (category: technical_capacity, subcategory: satellite_system_architecture)", "network_layout_design": "evaluation_criteria (category: technical_capacity, subcategory: network_layout_design)", "technical_rollout_plan": "evaluation_criteria (category: technical_capacity, subcategory: technical_rollout_plan)", "technical_personnel_expertise": "evaluation_criteria (category: technical_capacity, subcategory: technical_personnel_expertise)", "implementation_schedule_plan": "evaluation_criteria (category: technical_capacity, subcategory: implementation_schedule_plan)", "resource_requirements_coordination": "evaluation_criteria (category: technical_capacity, subcategory: resource_requirements_coordination)"}, "organization_setup_10_percent": {"corporate_structure_governance": "evaluation_criteria (category: organization_setup, subcategory: corporate_structure_governance)", "management_team_experience": "evaluation_criteria (category: organization_setup, subcategory: management_team_experience)", "legal_compliance_framework": "evaluation_criteria (category: organization_setup, subcategory: legal_compliance_framework)"}}, "tv_broadcasting": {"description": "TV Broadcasting License evaluation criteria - 55% minimum score required", "business_plan_40_percent": {"tv_services_strategy": "evaluation_criteria (category: business_plan, subcategory: tv_services_strategy)", "market_assessment_analysis": "evaluation_criteria (category: business_plan, subcategory: market_assessment_analysis)", "financial_capacity_proof": "evaluation_criteria (category: business_plan, subcategory: financial_capacity_proof)", "business_viability_model": "evaluation_criteria (category: business_plan, subcategory: business_viability_model)"}, "technical_operational_50_percent": {"tv_broadcasting_system_architecture": "evaluation_criteria (category: technical_capacity, subcategory: tv_broadcasting_system_architecture)", "network_layout_design": "evaluation_criteria (category: technical_capacity, subcategory: network_layout_design)", "technical_rollout_plan": "evaluation_criteria (category: technical_capacity, subcategory: technical_rollout_plan)", "technical_personnel_expertise": "evaluation_criteria (category: technical_capacity, subcategory: technical_personnel_expertise)", "implementation_schedule_plan": "evaluation_criteria (category: technical_capacity, subcategory: implementation_schedule_plan)", "resource_requirements_coordination": "evaluation_criteria (category: technical_capacity, subcategory: resource_requirements_coordination)", "programming_content_strategy": "evaluation_criteria (category: technical_capacity, subcategory: programming_content_strategy)"}, "organization_setup_10_percent": {"corporate_structure_governance": "evaluation_criteria (category: organization_setup, subcategory: corporate_structure_governance)", "management_team_experience": "evaluation_criteria (category: organization_setup, subcategory: management_team_experience)", "legal_compliance_framework": "evaluation_criteria (category: organization_setup, subcategory: legal_compliance_framework)"}}, "university_radio": {"description": "University Radio License evaluation criteria - Declaration-based application with comprehensive review", "programming_content_40_percent": {"market_appeal_strategy": "evaluation_criteria (category: programming_content, subcategory: market_appeal_strategy)", "programming_format_approach": "evaluation_criteria (category: programming_content, subcategory: programming_format_approach)", "local_content_promotion": "evaluation_criteria (category: programming_content, subcategory: local_content_promotion)", "editorial_policy_compliance": "evaluation_criteria (category: programming_content, subcategory: editorial_policy_compliance)", "community_engagement": "evaluation_criteria (category: programming_content, subcategory: community_engagement)"}, "financial_technical_capacity_40_percent": {"financial_sustainability": "evaluation_criteria (category: financial_capacity, subcategory: financial_sustainability)", "funding_source_reliability": "evaluation_criteria (category: financial_capacity, subcategory: funding_source_reliability)", "technical_infrastructure": "evaluation_criteria (category: technical_capacity, subcategory: technical_infrastructure)", "technical_personnel_expertise": "evaluation_criteria (category: technical_capacity, subcategory: technical_personnel_expertise)", "transmission_site_planning": "evaluation_criteria (category: technical_capacity, subcategory: transmission_site_planning)"}, "organization_setup_20_percent": {"legal_compliance_documentation": "evaluation_criteria (category: organization_setup, subcategory: legal_compliance_documentation)", "management_structure": "evaluation_criteria (category: organization_setup, subcategory: management_structure)", "institutional_capacity": "evaluation_criteria (category: organization_setup, subcategory: institutional_capacity)"}}}, "license_type_specific_requirements": {"description": "Specific requirements and differences between license types", "individual_license_form_a": {"steps": 6, "minimum_score": 70, "evaluation_categories": {"financial_capacity": 20, "business_plan": 20, "technical_operational": 40, "organization_setup": 10, "socio_economic_impact": 10}, "required_documents": ["certificate_incorporation", "memorandum_association", "shareholding_structure", "partnership_agreement", "ownership_interest", "board_directors", "capital_expenditure", "financial_performance", "investment_appraisal", "market_assessment", "proof_financial", "audited_accounts", "technical_expertise", "technical_rollout", "technical_personnel", "network_layout", "implementation_schedule", "network_diagram", "disaster_recovery", "resource_requirements", "company_stamp"], "special_features": ["Management team (3-7 members required)", "Years in operation tracking (0-3 or 3+)", "Detailed technical capacity section", "Disaster recovery plan required", "Socio-economic impact evaluation"]}, "class_license_form_b": {"steps": 5, "minimum_score": 55, "evaluation_categories": {"business_plan": 40, "technical_operational": 50, "organization_setup": 10}, "required_documents": ["certificate_incorporation", "memorandum_association", "market_assessment", "proof_financial", "technical_rollout", "technical_personnel", "network_layout", "implementation_schedule", "network_diagram", "resource_requirements", "company_stamp"], "special_features": ["Simplified business plan section", "No management team details required", "No years in operation tracking", "No disaster recovery plan required", "No socio-economic impact evaluation", "Combined evaluation and undertaking step", "Focus on technical infrastructure and equipment"]}, "mobile_license": {"steps": 5, "minimum_score": 55, "evaluation_categories": {"business_plan": 40, "technical_operational": 50, "organization_setup": 10}, "required_documents": ["certificate_incorporation", "memorandum_association", "shareholding_structure", "financial_capacity", "network_architecture", "technical_rollout", "technical_personnel", "company_stamp"], "special_features": ["Mobile network category selection (national, regional, district, community)", "Service type specification (2G, 3G, 4G, 5G, MVNO)", "Coverage area planning (national, regional, urban, rural, mixed)", "Spectrum band requirements (700MHz, 850MHz, 900MHz, 1800MHz, 2100MHz, 2600MHz)", "Subscriber projections for first year", "Target market segmentation (consumer, enterprise, government, IoT, mixed)", "Investment amount in Malawi Kwacha", "Employee planning and revenue projections", "Mandatory shareholding compliance", "Combined evaluation and undertaking step"]}, "radio_broadcasting": {"steps": 6, "minimum_score": 60, "evaluation_categories": {"business_plan_programming": 35, "technical_capacity_infrastructure": 30, "financial_capacity": 20, "organization_setup_management": 15}, "required_documents": ["certificate_incorporation", "memorandum_association", "financial_projections", "market_assessment", "proof_financial", "broadcasting_equipment", "frequency_coverage_plan", "studio_transmission_setup", "technical_personnel", "implementation_schedule", "company_stamp"], "special_features": ["Radio broadcasting license category selection (national, regional, district, community)", "License type specification (commercial, community, educational, religious, campus)", "Broadcasting service selection (FM, AM, digital, internet)", "Programming content and format strategy planning", "Coverage area and target market analysis", "Revenue model and advertising strategy", "Broadcasting equipment specifications and technical setup", "Frequency allocation and coverage planning", "Studio and transmission facility setup", "Technical personnel qualifications and experience", "Implementation schedule and rollout plan", "Separate evaluation criteria step before undertaking", "Mandatory shareholding compliance", "Company stamp upload with preview functionality"]}, "satellite_communications": {"steps": 5, "minimum_score": 55, "evaluation_categories": {"business_plan": 40, "technical_operational": 50, "organization_setup": 10}, "required_documents": ["certificate_incorporation", "memorandum_association", "market_assessment", "proof_financial", "technical_rollout", "technical_personnel", "satellite_network_layout", "implementation_schedule", "satellite_system_architecture", "resource_requirements", "company_stamp"], "special_features": ["Satellite communications license category selection (national, regional, district, community)", "License type specification (VSAT, satellite internet, earth station, satellite gateway)", "Range of satellite communication services description (VSAT, satellite internet, earth station services, etc.)", "Detailed market assessment for satellite services", "Proof of financial capacity for satellite infrastructure", "Technical and service rollout plan for satellite communications for next 5 years", "Proposed technical personnel and their resumes", "Proposed satellite network layout including earth stations, VSAT terminals, and gateway locations", "Implementation schedule and growth plan", "Detailed satellite system architecture, equipment specifications, frequency coordination plans, and orbital slot requirements", "Resource requirements (frequency spectrum, orbital slots, earth station coordinates)", "Combined evaluation criteria and undertaking step", "Mandatory shareholding compliance", "Company stamp upload with preview functionality"]}, "tv_broadcasting": {"steps": 5, "minimum_score": 55, "evaluation_categories": {"business_plan": 40, "technical_operational": 50, "organization_setup": 10}, "required_documents": ["certificate_incorporation", "memorandum_association", "market_assessment", "proof_financial", "technical_rollout", "technical_personnel", "tv_broadcasting_network_layout", "implementation_schedule", "tv_broadcasting_system_architecture", "resource_requirements", "programming_content_plan", "company_stamp"], "special_features": ["TV broadcasting license category selection (national, regional, district, community)", "License type specification (terrestrial TV, digital TV, cable TV, IPTV)", "Range of TV broadcasting services description (terrestrial TV, digital TV, cable TV, IPTV, etc.)", "Detailed market assessment for TV broadcasting services", "Proof of financial capacity for TV broadcasting infrastructure", "Technical and service rollout plan for TV broadcasting for next 5 years", "Proposed technical personnel and their resumes", "Proposed TV broadcasting network layout including transmitters, studios, and coverage areas", "Implementation schedule and growth plan", "Detailed TV broadcasting system architecture, equipment specifications, frequency coordination plans, and transmission requirements", "Resource requirements (frequency spectrum, transmitter locations, studio facilities)", "Programming content plan and content acquisition strategy", "Combined evaluation criteria and undertaking step", "Mandatory shareholding compliance", "Company stamp upload with preview functionality"]}, "university_radio": {"steps": 5, "minimum_score": "Declaration-based", "evaluation_categories": {"programming_content": 40, "financial_technical_capacity": 40, "organization_setup": 20}, "required_documents": ["certificate_incorporation", "memorandum_association", "articles_association", "local_support_signatures", "editorial_policy", "business_plan", "proof_funding_source", "income_expenditure_statement", "technical_personnel", "existing_sites_documentation", "roll_out_plans"], "special_features": ["University radio license application for educational institutions", "Sound or TV broadcasting license type selection", "Specific sound license types (FM, AM, digital, community, campus)", "Specific TV license types (terrestrial, satellite, cable, digital, community)", "Proposed station name and physical address specification", "Comprehensive programming and market research section", "Market appeal and audience targeting analysis", "Detailed market research findings and evidence of demand", "Required 50 signatures from local community with personal characteristics", "Complaints and codes of operation procedures", "Programming format selection (youth, talk, religious, educational, music, general)", "Broadcasting service approach and objectives", "Programming schedule (start time, end time, peak time)", "Programme sourcing (external vs locally generated)", "Local Malawian content promotion strategy", "Editorial policy document upload with detailed guidelines", "Financial capacity with multiple funding sources (grants, donations, sponsorships, advertising, membership)", "Business plan and proof of funding source documentation", "Income and expenditure statement requirements", "Technical capacity with personnel resumes", "Transmission sites and linking arrangements details", "Existing sites documentation and site sharing negotiations", "Roll out plans with targets and timeframes", "Conclusion section with motivation for license approval", "Declaration with Commissioner of Oath certification", "Multiple confirmation checkboxes (accuracy, compliance, submission authorization)", "Shareholder and management team information collection", "Other interested parties documentation", "Draft saving and progress tracking functionality"]}}}