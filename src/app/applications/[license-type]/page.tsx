'use client';

import { useEffect } from 'react';
import { useParams } from 'next/navigation';
import LicenseManagementTable from '../../../components/license/LicenseManagementTable';

// License type configuration with backend license type IDs
const licenseTypeConfig = {
  'postal': {
    name: 'Postal Services',
    description: 'Manage postal and courier service license applications for domestic and international operations. View applications, filter by category and status.',
    searchPlaceholder: 'Search postal applications, companies, application numbers...',
    emptyStateIcon: 'ri-mail-line',
    emptyStateMessage: 'No postal license applications are available.',
    licenseTypeFilter: 'postal' // This will be used to filter by license type name
  },
  'telecommunications': {
    name: 'Telecommunications',
    description: 'Manage telecommunications and spectrum license applications for mobile networks, ISPs, and broadcasting services. View applications, filter by category and status.',
    searchPlaceholder: 'Search telecommunications applications, companies, application numbers...',
    emptyStateIcon: 'ri-signal-tower-line',
    emptyStateMessage: 'No telecommunications license applications are available.',
    licenseTypeFilter: 'telecommunications'
  },
  'standards': {
    name: 'Standards Compliance',
    description: 'Manage standards compliance and type approval certificate applications. View applications, filter by category and status.',
    searchPlaceholder: 'Search standards applications, companies, application numbers...',
    emptyStateIcon: 'ri-award-line',
    emptyStateMessage: 'No standards applications are available.',
    licenseTypeFilter: 'standards'
  },
  'clf': {
    name: 'CLF (Converged Licensing Framework)',
    description: 'Manage CLF licenses including Network Facility, Network Service, Application Service, and Content Service licenses.',
    searchPlaceholder: 'Search CLF licenses, companies, license IDs...',
    emptyStateIcon: 'ri-collage-line',
    emptyStateMessage: 'No CLF license applications are available.',
    licenseTypeFilter: 'clf'
  }
};

export default function LicenseTypeApplicationsPage() {
  const params = useParams();
  const licenseType = params['license-type'] as string;

  // Get current license type configuration
  const currentConfig = licenseTypeConfig[licenseType as keyof typeof licenseTypeConfig];

  useEffect(() => {
    if (!currentConfig) {
      // Invalid license type, redirect to default
      window.location.href = '/applications/telecommunications';
      return;
    }
  }, [licenseType, currentConfig]);

  if (!currentConfig) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <i className="ri-error-warning-line text-4xl text-red-500 mb-4"></i>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Invalid License Type</h3>
          <p className="text-gray-500">The requested license type was not found.</p>
        </div>
      </div>
    );
  }

  return (
    <LicenseManagementTable
      licenseTypeFilter={currentConfig.licenseTypeFilter}
      title={`${currentConfig.name} License Management`}
      description={currentConfig.description}
      searchPlaceholder={currentConfig.searchPlaceholder}
      emptyStateIcon={currentConfig.emptyStateIcon}
      emptyStateMessage={currentConfig.emptyStateMessage}
      departmentType={licenseType}
    />
  );
}
