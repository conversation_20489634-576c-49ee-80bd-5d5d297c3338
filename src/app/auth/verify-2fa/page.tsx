'use client';

import { useEffect, useState } from 'react';
import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';
import { authService } from '@/services/auth.service';
import {
  ArrowPathIcon,
  CheckCircleIcon,
  XCircleIcon,
} from '@heroicons/react/24/solid';
import Cookies from "js-cookie";

export default function SetupTwoFactorPage() {
  const router = useRouter();
  const [user_id, setUserId ] = useState('');
  const [unique, setUnique] = useState<string | ''>('');
  const [code, setCode] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [loading, setLoading] = useState(true);

  const searchParams = useSearchParams();
  const userId = searchParams.get('i') || '';
  const u = searchParams.get('unique') || '';
  const c = searchParams.get('c') || '';
  

  useEffect(() => {
    console.log('User Id', userId);
    console.log('Unique', u);
    console.log('Code', c);
    setUserId(userId);
    setUnique(u);
    setCode(c);
    if (!user_id || !unique || !code) {
      setError('Missing user session. Redirecting to login..');
      setLoading(false);
      setTimeout(() => {
        router.replace('/auth/login');
      }, 10000);
      
      return;
    }

    const verify2FA = async () => {
      try {
        const { access_token, user, message } = await authService.verify2FA({ unique, user_id, code });
        setSuccess(message || 'Your account has been verified successfully!');
        if (access_token && user) {
          // Save to cookies
          Cookies.set('auth_token', access_token, { expires: 1 });
          Cookies.set('auth_user', JSON.stringify(user), { expires: 1 });
        }
        setTimeout ( () => {
          router.push('/auth/login');
        }, 7000);
        
      } catch (err: any) {
        const message: string =
          err?.response?.data?.message ||
          err?.message ||
          'Failed to initiate 2FA setup. Redirecting to login...';
        
        setError(message);
        setTimeout(() => {
          router.replace('/auth/login');
        }, 7000);
        
      } finally {
        setLoading(false);
      }
    };
    if (code && unique && code) {
      verify2FA();
    } else {
      setError('Missing verification information. Redirecting to login..');
      router.replace('/auth/login');
    }
    
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <ArrowPathIcon className="w-16 h-16 animate-spin text-gray-500 dark:text-gray-300" /> <br />
        <span className='place-content-center justify-center text-center text-gray-500 dark:text-gray-300'>Verifying your OTP...</span>
      </div>
    );
  }

  const alreadyEnabled = success.toLowerCase().includes('enabled');
  return (
    <div className="min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900">
      <div className="sm:mx-auto sm:w-full sm:max-w-md text-center">
        <Image src="/images/macra-logo.png" alt="MACRA Logo" width={50} height={50} className="mx-auto h-16 w-auto" />
        <h2 className="mt-6 text-3xl font-extrabold text-gray-900 dark:text-white">
          {!success ? 'Account Verification' : 'Account Verification Success!'}
        </h2>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white dark:bg-gray-800 py-8 px-6 shadow rounded-lg sm:px-10">
          {error && !alreadyEnabled && (
            <div className="flex flex-col flex-auto items-center justify-center">
              <div className="w-16 h-16 mb-4 rounded-full bg-red-100 dark:bg-red-900 flex items-center justify-center shadow-md">
                <XCircleIcon className="w-10 h-10 animate-pulse" />
              </div>
              <div className="mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 px-4 py-3 rounded-md flex items-center">
                {error}
              </div>
            </div>
          )}

          {(success || alreadyEnabled ) && (
            <div className="flex flex-col flex-auto items-center justify-center">
              <div className="w-16 h-16 mb-4 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center animate-bounce shadow-md">
                <svg
                  className="w-8 h-8 text-green-600 dark:text-green-300"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="3"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <div className="text-center text-gray-600 dark:text-gray-400">
                {success} <br />
              </div>
            </div>
          )}

        </div>
      </div>
    </div>
  );
}
