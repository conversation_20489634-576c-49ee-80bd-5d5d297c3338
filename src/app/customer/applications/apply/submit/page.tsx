'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import CustomerLayout from '@/components/customer/CustomerLayout';
import ApplicationLayout from '@/components/applications/ApplicationLayout';
import { useAuth } from '@/contexts/AuthContext';
import { applicationService } from '@/services/applicationService';
import { licenseCategoryService } from '@/services/licenseCategoryService';
import { LicenseType } from '@/services/licenseTypeService';


const SubmitPage: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isAuthenticated, loading: authLoading } = useAuth();

  // URL parameters
  const licenseCategoryId = searchParams.get('license_category_id');
  const applicationId = searchParams.get('application_id');

  // State management
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [loadingWarning, setLoadingWarning] = useState<string | null>(null);
  const [confirmationAccepted, setConfirmationAccepted] = useState(false);

  // License data
  const [licenseType, setLicenseType] = useState<LicenseType | null>(null);

  // Application summary data
  const [applicationSummary, setApplicationSummary] = useState<any>(null);

  // Load application data and summary
  useEffect(() => {
    const loadData = async () => {
      if (!applicationId || !isAuthenticated || authLoading) return;

      try {
        setIsLoading(true);
        setError(null);
        setLoadingWarning(null);

        console.log('🔄 Loading application data for submission:', applicationId);

        // Load license category and type data
        if (licenseCategoryId) {
          try {
            const categoryData = await licenseCategoryService.getLicenseCategory(licenseCategoryId);
            if (categoryData && categoryData.license_type) {
              setLicenseType(categoryData.license_type);
            }
          } catch (licenseError) {
            console.warn('Could not load license category data:', licenseError);
          }
        }

        // Load application data
        const application = await applicationService.getApplication(applicationId);
        console.log('📋 Application loaded:', application);

        // Load application summary from all form sections
        try {
          const allSections = await Promise.all([
            applicationFormDataService.getFormSection(applicationId, 'applicant-info').catch(() => null),
            applicationFormDataService.getFormSection(applicationId, 'address-info').catch(() => null),
            applicationFormDataService.getFormSection(applicationId, 'contact-info').catch(() => null),
            applicationFormDataService.getFormSection(applicationId, 'management').catch(() => null),
            applicationFormDataService.getFormSection(applicationId, 'service-scope').catch(() => null),
            applicationFormDataService.getFormSection(applicationId, 'legal-history').catch(() => null)
          ]);

          const [applicantInfo, addressInfo, contactInfo, management, serviceScope, legalHistory] = allSections;

          setApplicationSummary({
            applicantInfo,
            addressInfo,
            contactInfo,
            management,
            serviceScope,
            legalHistory,
            application
          });

          console.log('✅ Application summary loaded');
        } catch (summaryError: any) {
          console.error('❌ Error loading application summary:', summaryError);
          setLoadingWarning('Could not load complete application summary. You can still submit your application.');
        }

      } catch (err: any) {
        console.error('❌ Error loading application data:', err);
        setError('Failed to load application data');
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [applicationId, isAuthenticated, authLoading]);

  // Submit application
  const handleSubmit = async (): Promise<boolean> => {
    if (!applicationId) {
      setError('Application ID is required');
      return false;
    }

    if (!confirmationAccepted) {
      setError('You must confirm the declaration before submitting');
      return false;
    }

    setIsSubmitting(true);
    try {
      console.log('🚀 Submitting application:', applicationId);

      // Update application status to submitted
      await applicationService.updateApplication(applicationId, {
        status: 'submitted',
        submitted_at: new Date().toISOString(),
        progress_percentage: 100,
        current_step: 8 // Final step
      });

      console.log('✅ Application submitted successfully');

      // Redirect to success page
      router.push(`/customer/applications/submitted?application_id=${applicationId}`);
      return true;

    } catch (error: any) {
      console.error('❌ Error submitting application:', error);
      setError('Failed to submit application. Please try again.');
      return false;
    } finally {
      setIsSubmitting(false);
    }
  };

  // Navigation functions
  const handleNext = async () => {
    await handleSubmit();
  };

  const handlePrevious = () => {
    router.push(`/customer/applications/apply/documents?license_category_id=${licenseCategoryId}&application_id=${applicationId}`);
  };

  if (authLoading || isLoading) {
    return (
      <CustomerLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading application for submission...</p>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  if (error) {
    return (
      <CustomerLayout>
        <div className="max-w-4xl mx-auto p-6">
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
            <div className="flex items-center">
              <i className="ri-error-warning-line text-red-600 dark:text-red-400 text-2xl mr-4"></i>
              <div>
                <h3 className="text-lg font-medium text-red-800 dark:text-red-200">Error Loading Application</h3>
                <p className="text-red-700 dark:text-red-300 mt-1">{error}</p>
                <button
                  onClick={() => router.back()}
                  className="mt-4 inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 rounded-md text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-red-900/20 hover:bg-red-50 dark:hover:bg-red-900/40"
                >
                  <i className="ri-arrow-left-line mr-2"></i>
                  Go Back
                </button>
              </div>
            </div>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  return (
    <CustomerLayout>
      <ApplicationLayout
        onNext={handleNext}
        onPrevious={handlePrevious}
        onSave={async () => true} // No save needed on submit page
        showNextButton={true}
        showPreviousButton={true}
        showSaveButton={false}
        nextButtonText={isSubmitting ? "Submitting..." : "Submit Application"}
        previousButtonText="Back to Documents"
        nextButtonDisabled={!confirmationAccepted || isSubmitting}
        isSaving={isSubmitting}
      >
        {/* Header */}
        <div className="mb-6">
          <h2 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            Submit Application
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Review your application details and submit for review by MACRA.
          </p>
          {applicationId && (
            <div className="mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <p className="text-sm text-blue-700 dark:text-blue-300">
                <i className="ri-file-text-line mr-1"></i>
                Application ID: {applicationId}
              </p>
            </div>
          )}
          {loadingWarning && (
            <div className="mt-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
              <p className="text-sm text-yellow-700 dark:text-yellow-300">
                ⚠️ {loadingWarning}
              </p>
            </div>
          )}
        </div>

        {/* Application Summary */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
          <div className="space-y-6">
            <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                Application Summary
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                Your application is ready for submission to MACRA for review.
              </p>
            </div>

            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                  <i className="ri-user-line mr-2"></i>
                  Applicant Information
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {applicationSummary?.applicantInfo ? '✅ Completed' : '❌ Missing'}
                </p>
              </div>

              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                  <i className="ri-map-pin-line mr-2"></i>
                  Address Information
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {applicationSummary?.addressInfo ? '✅ Completed' : '❌ Missing'}
                </p>
              </div>

              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                  <i className="ri-phone-line mr-2"></i>
                  Contact Information
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {applicationSummary?.contactInfo ? '✅ Completed' : '❌ Missing'}
                </p>
              </div>

              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                  <i className="ri-team-line mr-2"></i>
                  Management Information
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {applicationSummary?.management ? '✅ Completed' : '❌ Missing'}
                </p>
              </div>

              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                  <i className="ri-service-line mr-2"></i>
                  Service Scope
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {applicationSummary?.serviceScope ? '✅ Completed' : '❌ Missing'}
                </p>
              </div>

              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                  <i className="ri-shield-check-line mr-2"></i>
                  Legal History
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {applicationSummary?.legalHistory ? '✅ Completed' : '❌ Missing'}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Important Information */}
        <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg mb-6">
          <h4 className="text-md font-medium text-blue-900 dark:text-blue-100 mb-2">
            <i className="ri-information-line mr-2"></i>
            Important Information
          </h4>
          <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
            <li>• Your application will be reviewed by MACRA within 30 business days</li>
            <li>• You will receive email notifications about the status of your application</li>
            <li>• Additional documentation may be requested during the review process</li>
            <li>• Application fees are non-refundable</li>
            <li>• You can track your application status in your dashboard</li>
          </ul>
        </div>

        {/* Warning */}
        <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg mb-6">
          <div className="flex">
            <i className="ri-alert-line text-yellow-600 dark:text-yellow-400 mr-2 mt-0.5"></i>
            <div>
              <h4 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                Before Submitting
              </h4>
              <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
                Please ensure all information is accurate and complete. Once submitted, 
                you will not be able to modify your application without contacting MACRA support.
              </p>
            </div>
          </div>
        </div>

        {/* Final Declaration */}
        <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
          <div className="flex items-start">
            <input
              type="checkbox"
              id="confirmation"
              checked={confirmationAccepted}
              onChange={(e) => setConfirmationAccepted(e.target.checked)}
              className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded mt-1"
            />
            <label htmlFor="confirmation" className="ml-2 block text-sm text-gray-900 dark:text-gray-100">
              I confirm that all information provided in this application is true, accurate, and complete to the best of my knowledge. 
              I understand that providing false or misleading information may result in the rejection of my application or 
              revocation of any license granted. I agree to the terms and conditions of the licensing process. 
              <span className="text-red-500">*</span>
            </label>
          </div>
          {!confirmationAccepted && (
            <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
              You must accept this declaration to submit your application.
            </p>
          )}
        </div>

      </ApplicationLayout>
    </CustomerLayout>
  );
};

export default SubmitPage;
