'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import CustomerLayout from '@/components/customer/CustomerLayout';

const CustomerLicensesPage = () => {
  const { isAuthenticated, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading) {
      if (!isAuthenticated) {
        router.push('/customer/auth/login');
      } else {
        // Redirect to the actual my-licenses page
        router.push('/customer/my-licenses');
      }
    }
  }, [isAuthenticated, loading, router]);

  // Show loading state while checking authentication or redirecting
  if (loading || !isAuthenticated) {
    return (
      <CustomerLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading...</p>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  return null; // This should not be reached due to redirect
};

export default CustomerLicensesPage;