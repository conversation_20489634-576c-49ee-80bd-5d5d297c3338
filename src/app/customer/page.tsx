'use client';

import React, { useState, useEffect, useMemo } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import CustomerLayout from '@/components/customer/CustomerLayout';
import LicenseCard from '@/components/customer/LicenseCard';
import PaymentCard from '@/components/customer/PaymentCard';
import Loader from '@/components/Loader';
import { useAuth } from '@/contexts/AuthContext';
import { customerApi, License, Application, Payment } from '@/lib/customer-api';
import { measurePageLoad, measureApiCall } from '@/utils/performance';

const CustomerDashboard = () => {
  const { user, isAuthenticated } = useAuth();
  const router = useRouter();

  // Performance monitoring
  useEffect(() => {
    const endPageLoad = measurePageLoad('customer-dashboard');
    return endPageLoad;
  }, []);

  const [dashboardData, setDashboardData] = useState({
    licenses: [] as License[],
    applications: [] as Application[],
    payments: [] as Payment[],
    stats: {
      activeLicenses: 0,
      pendingApplications: 0,
      expiringSoon: 0,
      paymentsDue: 0,
      totalPaymentAmount: 0
    }
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  // Fetch dashboard data with optimized parallel requests
  useEffect(() => {
    const fetchDashboardData = async () => {
      if (!isAuthenticated) return;

      try {
        setIsLoading(true);
        setError('');

        // Measure API performance
        const endApiCall = measureApiCall('dashboard-data');

        // Use Promise.all for better performance
        const [licensesRes, applicationsRes, paymentsRes, statsRes] = await Promise.all([
          customerApi.getLicenses({ limit: 10 }).catch(() => ({ data: [] })),
          customerApi.getApplications({ limit: 10 }).catch(() => ({ data: [] })),
          customerApi.getPayments({ limit: 10 }).catch(() => ({ data: [] })),
          customerApi.getDashboardStats().catch(() => ({}))
        ]);

        endApiCall();

        // Process data efficiently
        const licenses = licensesRes.data || licensesRes || [];
        const applications = applicationsRes.data || applicationsRes || [];
        const payments = paymentsRes.data || paymentsRes || [];

        // Process stats or calculate from data
        let stats;
        if (statsRes && Object.keys(statsRes).length > 0) {
          stats = statsRes.data || statsRes;
        } else {
          // Calculate stats from fetched data
          const activeLicenses = licenses.filter((l: License) => l.status === 'active').length;
          const pendingApplications = applications.filter((a: Application) =>
            ['submitted', 'under_review'].includes(a.status)
          ).length;

          // Check for licenses expiring in next 30 days
          const thirtyDaysFromNow = new Date();
          thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
          const expiringSoon = licenses.filter((l: License) => {
            const expirationDate = new Date(l.expirationDate);
            return l.status === 'active' && expirationDate <= thirtyDaysFromNow;
          }).length;

          const pendingPayments = payments.filter((p: Payment) =>
            ['pending', 'overdue'].includes(p.status)
          );
          const totalPaymentAmount = pendingPayments.reduce((sum: number, p: Payment) => sum + p.amount, 0);

          stats = {
            activeLicenses,
            pendingApplications,
            expiringSoon,
            paymentsDue: pendingPayments.length,
            totalPaymentAmount
          };
        }

        setDashboardData({
          licenses,
          applications,
          payments,
          stats
        });

      } catch (err: unknown) {
        console.error('Error fetching dashboard data:', err);
        setError('Failed to load dashboard data. Please try refreshing the page.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardData();
  }, [isAuthenticated]);

  // Memoize expensive calculations
  const quickStats = useMemo(() => {
    const { licenses, applications, payments } = dashboardData;
    
    return {
      totalLicenses: licenses.length,
      activeLicenses: licenses.filter(l => l.status === 'active').length,
      pendingApplications: applications.filter(a => ['submitted', 'under_review'].includes(a.status)).length,
      overduePayments: payments.filter(p => p.status === 'overdue').length
    };
  }, [dashboardData]);

  // Memoize filtered data for display
  const displayData = useMemo(() => {
    const { licenses, applications, payments } = dashboardData;
    
    return {
      recentLicenses: licenses.slice(0, 3),
      recentApplications: applications.slice(0, 3),
      urgentPayments: payments.filter(p => ['pending', 'overdue'].includes(p.status)).slice(0, 3)
    };
  }, [dashboardData]);

  // Show loading state
  if ( isLoading) {
    return (
      <CustomerLayout>
        <div className="flex items-center justify-center min-h-96">
          <Loader message="Loading your dashboard..." />
        </div>
      </CustomerLayout>
    );
  }

  // Show error state
  if (error) {
    return (
      <CustomerLayout>
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
          <p>{error}</p>
          <button
            type="button"
            onClick={() => window.location.reload()}
            className="mt-2 text-sm underline hover:no-underline"
          >
            Try again
          </button>
        </div>
      </CustomerLayout>
    );
  }

  // Prepare status data with real values
  const statusData = [
    {
      title: 'Active Licenses',
      value: dashboardData.stats.activeLicenses,
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      bgColor: 'bg-white',
      iconBgColor: 'bg-green-100',
      iconTextColor: 'text-green-600',
      linkText: 'View all',
      linkHref: '/customer/licenses'
    },
    {
      title: 'Pending Applications',
      value: dashboardData.stats.pendingApplications,
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      bgColor: 'bg-white',
      iconBgColor: 'bg-yellow-100',
      iconTextColor: 'text-yellow-600',
      linkText: 'View all',
      linkHref: '/customer/applications'
    },
    {
      title: 'Expiring Soon',
      value: dashboardData.stats.expiringSoon,
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      bgColor: 'bg-white',
      iconBgColor: 'bg-orange-100',
      iconTextColor: 'text-orange-600',
      linkText: 'View all',
      linkHref: '/customer/licenses?filter=expiring'
    },
    {
      title: 'Payments Due',
      value: dashboardData.stats.totalPaymentAmount > 0
        ? `MK${dashboardData.stats.totalPaymentAmount.toLocaleString()}`
        : 'MK0',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      bgColor: 'bg-white',
      iconBgColor: 'bg-red-100',
      iconTextColor: 'text-red-600',
      linkText: 'View all',
      linkHref: '/customer/payments'
    }
  ];

  // Transform license data for display (using memoized data)
  const recentLicenses = displayData.recentLicenses.slice(0, 2).map((license: License) => {
    // Determine display status
    let displayStatus: 'Active' | 'Expiring Soon' | 'Expired' | 'Pending';
    if (license.status === 'active') {
      // Check if expiring soon (within 30 days)
      const expirationDate = new Date(license.expirationDate);
      const thirtyDaysFromNow = new Date();
      thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
      displayStatus = expirationDate <= thirtyDaysFromNow ? 'Expiring Soon' : 'Active';
    } else if (license.status === 'expired') {
      displayStatus = 'Expired';
    } else {
      displayStatus = 'Pending';
    }

    return {
      id: license.id,
      title: license.type || 'License',
      licenseNumber: license.licenseNumber,
      status: displayStatus,
      issueDate: new Date(license.issueDate).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      }),
      expirationDate: new Date(license.expirationDate).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    };
  });

  // Transform payment data for display (using memoized data)
  const upcomingPayments = displayData.urgentPayments
    .map((payment: Payment) => {
      const dueDate = new Date(payment.dueDate);
      const today = new Date();
      const diffTime = dueDate.getTime() - today.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      let dueDateText;
      if (diffDays < 0) {
        dueDateText = `Overdue by ${Math.abs(diffDays)} days`;
      } else if (diffDays === 0) {
        dueDateText = 'Due today';
      } else if (diffDays === 1) {
        dueDateText = 'Due tomorrow';
      } else {
        dueDateText = `Due in ${diffDays} days`;
      }

      return {
        id: payment.id,
        title: payment.description || `Payment for ${payment.relatedLicense || payment.relatedApplication || 'Service'}`,
        amount: `MK${payment.amount.toLocaleString()}`,
        dueDate: dueDateText,
        status: payment.status === 'overdue' ? 'Overdue' as const : 'Due' as const,
        description: dueDateText
      };
    });

  return (
    <CustomerLayout>
      <div className="max-w-7xl mx-auto">
        {/* Page header */}
        <div className="mb-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div>
              <h1 className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                Welcome, {user?.first_name || 'Customer'}!
              </h1>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Manage your licenses and applications from your personal dashboard.
              </p>
            </div>
            <div className="flex space-x-3">
              <div className="relative">
                <button
                  type="button"
                  className="inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-button bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary whitespace-nowrap"
                >
                  <div className="w-4 h-4 flex items-center justify-center mr-2">
                    <i className="ri-calendar-line"></i>
                  </div>
                  {new Date().toLocaleDateString('en-US', { 
                    month: 'short', 
                    day: 'numeric', 
                    year: 'numeric' 
                  })}
                </button>
              </div>
              <Link
                href="/customer/applications"
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-button text-white bg-primary hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors"
              >
                <div className="w-4 h-4 flex items-center justify-center mr-2">
                  <i className="ri-add-line"></i>
                </div>
                New Application
              </Link>
            </div>
          </div>
        </div>

        {/* Key Metrics Section */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg mb-6 overflow-hidden">
          <div className="p-6">
            <h3 className="text-lg font-medium leading-4 text-gray-900 dark:text-gray-100 mb-4">Key Metrics</h3>
            <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
              {statusData.map((item, index) => (
                <div key={index} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 flex flex-col space-y-4">
                  <div className="flex place-content-start items-center">
                    <div className={`flex-shrink-0 ${item.iconBgColor} ${item.iconBgColor.includes('green') ? 'dark:bg-green-900' : item.iconBgColor.includes('yellow') ? 'dark:bg-yellow-900' : item.iconBgColor.includes('orange') ? 'dark:bg-orange-900' : item.iconBgColor.includes('red') ? 'dark:bg-red-900' : 'dark:bg-gray-600'} rounded-md p-3`}>
                      <div className={`w-6 h-6 flex items-center justify-center ${item.iconTextColor} ${item.iconTextColor.includes('green') ? 'dark:text-green-400' : item.iconTextColor.includes('yellow') ? 'dark:text-yellow-400' : item.iconTextColor.includes('orange') ? 'dark:text-orange-400' : item.iconTextColor.includes('red') ? 'dark:text-red-400' : 'dark:text-gray-400'}`}>
                        {item.icon}
                      </div>
                    </div>
                    <div className="ml-4 flex flex-col">
                      <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">{item.title}</h4>
                      <div className="mt-1 flex items-baseline">
                        <div className="text-2xl font-semibold text-gray-900 dark:text-gray-100">{item.value}</div>
                      </div>
                    </div>
                  </div>
                  <div>
                    <Link 
                      href={item.linkHref} 
                      className="inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 dark:text-gray-300 hover:text-white bg-white dark:bg-gray-600 border border-primary rounded-full hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors duration-300 transform hover:-translate-x-1 after:content-['_↗']"
                    >
                      {item.linkText}
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Quick Stats Overview */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg mb-6 overflow-hidden">
          <div className="p-6">
            <h3 className="text-lg font-medium leading-4 text-gray-900 dark:text-gray-100 mb-4">Quick Overview</h3>
            <div className="grid grid-cols-2 gap-4 sm:grid-cols-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{quickStats.totalLicenses}</div>
                <div className="text-sm text-gray-500 dark:text-gray-400">Total Licenses</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600 dark:text-green-400">{quickStats.activeLicenses}</div>
                <div className="text-sm text-gray-500 dark:text-gray-400">Active</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{quickStats.pendingApplications}</div>
                <div className="text-sm text-gray-500 dark:text-gray-400">Pending Apps</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600 dark:text-red-400">{quickStats.overduePayments}</div>
                <div className="text-sm text-gray-500 dark:text-gray-400">Overdue</div>
              </div>
            </div>
          </div>
        </div>

        {/* Main dashboard content */}
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
          {/* Left Column */}
          <div className="lg:col-span-2 space-y-6">
            {/* My Licenses */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">My Licenses</h3>
                  <Link href="/customer/licenses" className="text-sm text-primary hover:text-primary">
                    View all →
                  </Link>
                </div>
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  {recentLicenses.map((license) => (
                    <LicenseCard key={license.id} {...license} />
                  ))}
                </div>
                {recentLicenses.length === 0 && (
                  <div className="text-center py-8">
                    <div className="w-16 h-16 mx-auto bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4">
                      <i className="ri-key-line text-2xl text-gray-400 dark:text-gray-500"></i>
                    </div>
                    <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">No licenses yet</h4>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Start by submitting your first license application using the &ldquo;New Application&rdquo; button above.
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Right Column */}
          <div className="space-y-6">
            {/* Application Process */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">License Application Process</h3>
                <div className="space-y-4">
                  {[
                    {
                      step: 1,
                      title: 'Submit Application',
                      description: 'Fill out the application form with your details and submit required documents.',
                      icon: 'ri-file-edit-line',
                      bgColor: 'bg-blue-100 dark:bg-blue-900',
                      textColor: 'text-blue-600 dark:text-blue-400'
                    },
                    {
                      step: 2,
                      title: 'Application Review',
                      description: 'Our team reviews your application and may request additional information if needed.',
                      icon: 'ri-search-eye-line',
                      bgColor: 'bg-yellow-100 dark:bg-yellow-900',
                      textColor: 'text-yellow-600 dark:text-yellow-400'
                    },
                    {
                      step: 3,
                      title: 'Payment',
                      description: 'Once approved, you\'ll receive an invoice for the license fee that must be paid.',
                      icon: 'ri-bank-card-line',
                      bgColor: 'bg-green-100 dark:bg-green-900',
                      textColor: 'text-green-600 dark:text-green-400'
                    },
                    {
                      step: 4,
                      title: 'License Issuance',
                      description: 'After payment confirmation, your license will be issued and available for download.',
                      icon: 'ri-award-line',
                      bgColor: 'bg-purple-100 dark:bg-purple-900',
                      textColor: 'text-purple-600 dark:text-purple-400'
                    }
                  ].map((item) => (
                    <div key={item.step} className="flex items-start">
                      <div className="flex-shrink-0">
                        <div className={`flex items-center justify-center h-10 w-10 rounded-full ${item.bgColor} ${item.textColor}`}>
                          <i className={`${item.icon} text-lg`}></i>
                        </div>
                      </div>
                      <div className="ml-4">
                        <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">{item.title}</h4>
                        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">{item.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Upcoming Payments */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Upcoming Payments</h3>
                  <Link href="/customer/payments" className="text-sm text-primary hover:text-primary">
                    View all →
                  </Link>
                </div>
                <div className="space-y-4">
                  {upcomingPayments.map((payment) => (
                    <PaymentCard key={payment.id} {...payment} />
                  ))}
                  {upcomingPayments.length === 0 && (
                    <div className="text-center py-6">
                      <div className="w-12 h-12 mx-auto bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-3">
                        <i className="ri-money-dollar-circle-line text-xl text-gray-400 dark:text-gray-500"></i>
                      </div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">No pending payments</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </CustomerLayout>
  );
};

export default CustomerDashboard;