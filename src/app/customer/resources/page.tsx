'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import CustomerLayout from '@/components/customer/CustomerLayout';
import { useAuth } from '@/contexts/AuthContext';
import TextInput from '@/components/forms/TextInput';
import TextArea from '@/components/forms/TextArea';
import Select from '@/components/forms/Select';

interface FileUpload {
  name: string;
  size: number;
  file: File;
}

const RequestResourcePage = () => {
  const { isAuthenticated, loading } = useAuth();
  const router = useRouter();
  const [mounted, setMounted] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    requestType: '',
    subject: '',
    description: '',
    relatedLicense: '',
    contactMethod: [] as string[]
  });

  const [uploadedFiles, setUploadedFiles] = useState<FileUpload[]>([]);
  const [charCount, setCharCount] = useState(0);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [ticketNumber, setTicketNumber] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Handle mounting to prevent hydration issues
  useEffect(() => {
    setMounted(true);
  }, []);

  // Redirect to customer login if not authenticated
  useEffect(() => {
    if (mounted && !loading && !isAuthenticated) {
      router.push('/customer/auth/login');
    }
  }, [mounted, loading, isAuthenticated, router]);

  // Show loading state during hydration or auth check
  if (!mounted || loading) {
    return (
      <CustomerLayout>
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-gray-600 dark:text-gray-400">Loading...</p>
            </div>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  // Don't render the form if not authenticated
  if (!isAuthenticated) {
    return null;
  }

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Update character count for description
    if (name === 'description') {
      setCharCount(value.length);
    }
  };

  // Handle checkbox changes for contact method
  const handleContactMethodChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      contactMethod: checked 
        ? [...prev.contactMethod, value]
        : prev.contactMethod.filter(method => method !== value)
    }));
  };

  // Handle file upload
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    
    files.forEach(file => {
      if (file.size <= 10 * 1024 * 1024) { // 10MB limit
        const newFile: FileUpload = {
          name: file.name,
          size: file.size,
          file: file
        };
        setUploadedFiles(prev => [...prev, newFile]);
      } else {
        alert(`File ${file.name} is too large. Maximum size is 10MB.`);
      }
    });

    // Reset file input
    e.target.value = '';
  };

  // Remove uploaded file
  const removeFile = (fileName: string) => {
    setUploadedFiles(prev => prev.filter(file => file.name !== fileName));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Generate random ticket number
      const randomNum = Math.floor(Math.random() * 1000) + 1;
      const newTicketNumber = `#REQ-2025-${String(randomNum).padStart(3, '0')}`;
      setTicketNumber(newTicketNumber);

      // Here you would typically send the data to your API
      // await submitResourceRequest(formData, uploadedFiles);

      // Show success modal
      setShowSuccessModal(true);
    } catch (error) {
      console.error('Error submitting request:', error);
      alert('Failed to submit request. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Save as draft
  const saveDraft = () => {
    // Here you would typically save to localStorage or API
    alert('Draft saved successfully! You can continue editing later.');
  };

  // Close success modal and reset form
  const closeSuccessModal = () => {
    setShowSuccessModal(false);
    setFormData({
      requestType: '',
      subject: '',
      description: '',
      relatedLicense: '',
      contactMethod: []
    });
    setUploadedFiles([]);
    setCharCount(0);
  };

  return (
    <CustomerLayout>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 mb-4">
            <Link href="/customer" className="hover:text-primary">Dashboard</Link>
            <i className="ri-arrow-right-s-line"></i>
            <span className="text-gray-900 dark:text-gray-100">Request Resource</span>
          </div>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Request Resource</h1>
              <p className="mt-2 text-gray-600 dark:text-gray-400">Submit requests for additional resources, support, or services from MACRA.</p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Form */}
          <div className="lg:col-span-2">
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Request Type */}
                <Select
                  label={
                    <span>
                      <i className="ri-folder-line mr-2 text-primary"></i>
                      Request Type
                    </span>
                  }
                  id="requestType"
                  name="requestType"
                  required
                  value={formData.requestType}
                  onChange={handleInputChange}
                >
                  <option value="">Select request type</option>
                  <option value="technical-support">Technical Support</option>
                  <option value="license-modification">License Modification</option>
                  <option value="spectrum-allocation">Spectrum Allocation</option>
                  <option value="compliance-guidance">Compliance Guidance</option>
                  <option value="documentation">Documentation Request</option>
                  <option value="training">Training & Capacity Building</option>
                  <option value="consultation">Regulatory Consultation</option>
                  <option value="other">Other</option>
                </Select>

                {/* Subject */}
                <TextInput
                  label={
                    <span>
                      <i className="ri-text mr-2 text-primary"></i>
                      Subject
                    </span>
                  }
                  id="subject"
                  name="subject"
                  required
                  value={formData.subject}
                  onChange={handleInputChange}
                  placeholder="Brief description of your request"
                />

                {/* Description */}
                <div>
                  <TextArea
                    label={
                      <span>
                        <i className="ri-file-text-line mr-2 text-primary"></i>
                        Detailed Description
                      </span>
                    }
                    id="description"
                    name="description"
                    rows={6}
                    required
                    value={formData.description}
                    onChange={handleInputChange}
                    placeholder="Provide detailed information about your request, including any specific requirements or context..."
                    maxLength={1000}
                  />
                  <div className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                    <span className={charCount > 1000 ? 'text-red-500' : ''}>{charCount}</span>/1000 characters
                  </div>
                </div>

                {/* Related License */}
                <Select
                  label={
                    <span>
                      <i className="ri-award-line mr-2 text-primary"></i>
                      Related License (Optional)
                    </span>
                  }
                  id="relatedLicense"
                  name="relatedLicense"
                  value={formData.relatedLicense}
                  onChange={handleInputChange}
                >
                  <option value="">Select related license</option>
                  <option value="NSL-2025-001">NSL-2025-001 - Internet Service Provider License</option>
                  <option value="RBL-2025-002">RBL-2025-002 - Radio Broadcasting License</option>
                  <option value="TVL-2025-003">TVL-2025-003 - Television Broadcasting License</option>
                  <option value="MNL-2023-001">MNL-2023-001 - Mobile Network License</option>
                  <option value="SCL-2025-004">SCL-2025-004 - Satellite Communications License</option>
                  <option value="PSL-2025-005">PSL-2025-005 - Postal Services License</option>
                </Select>

                {/* File Attachments */}
                <div>
                  <label htmlFor="fileInput" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    <i className="ri-attachment-line mr-2 text-primary"></i>
                    Attachments (Optional)
                  </label>
                  <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center hover:border-primary transition-colors">
                    <input 
                      type="file" 
                      id="fileInput" 
                      multiple 
                      accept=".pdf,.doc,.docx,.jpg,.jpeg,.png" 
                      onChange={handleFileUpload}
                      aria-label="Upload attachment files"
                      className="hidden"
                    />
                    <div className="space-y-2">
                      <i className="ri-upload-cloud-line text-4xl text-gray-400"></i>
                      <div>
                        <button 
                          type="button" 
                          onClick={() => document.getElementById('fileInput')?.click()}
                          className="text-primary hover:text-primary font-medium"
                        >
                          Click to upload files
                        </button>
                        <span className="text-gray-500 dark:text-gray-400"> or drag and drop</span>
                      </div>
                      <p className="text-xs text-gray-500 dark:text-gray-400">PDF, DOC, DOCX, JPG, PNG up to 10MB each</p>
                    </div>
                  </div>
                  
                  {/* File List */}
                  {uploadedFiles.length > 0 && (
                    <div className="mt-3 space-y-2">
                      {uploadedFiles.map((file, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                          <div className="flex items-center">
                            <i className="ri-file-line mr-3 text-primary"></i>
                            <div>
                              <div className="text-sm font-medium text-gray-900 dark:text-gray-100">{file.name}</div>
                              <div className="text-xs text-gray-500 dark:text-gray-400">{(file.size / 1024 / 1024).toFixed(2)} MB</div>
                            </div>
                          </div>
                          <button 
                            type="button" 
                            onClick={() => removeFile(file.name)}
                            className="text-red-500 hover:text-red-700"
                            aria-label={`Remove ${file.name}`}
                            title={`Remove ${file.name}`}
                          >
                            <i className="ri-close-line"></i>
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Contact Preference */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                    <i className="ri-phone-line mr-2 text-primary"></i>
                    Preferred Contact Method
                  </label>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                    <label className="flex items-center p-3 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700">
                      <input 
                        type="checkbox" 
                        name="contactMethod" 
                        value="email" 
                        checked={formData.contactMethod.includes('email')}
                        onChange={handleContactMethodChange}
                        className="rounded border-gray-300 text-primary focus:ring-primary"
                      />
                      <span className="ml-3 text-sm text-gray-900 dark:text-gray-100">Email</span>
                    </label>
                    <label className="flex items-center p-3 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700">
                      <input 
                        type="checkbox" 
                        name="contactMethod" 
                        value="phone" 
                        checked={formData.contactMethod.includes('phone')}
                        onChange={handleContactMethodChange}
                        className="rounded border-gray-300 text-primary focus:ring-primary"
                      />
                      <span className="ml-3 text-sm text-gray-900 dark:text-gray-100">Phone</span>
                    </label>
                  </div>
                </div>

                {/* Submit Button */}
                <div className="flex flex-col sm:flex-row gap-3 pt-6">
                  <button 
                    type="submit" 
                    disabled={isSubmitting}
                    className="flex-1 bg-primary text-white px-6 py-3 rounded-lg font-medium hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <i className="ri-send-plane-line mr-2"></i>
                    {isSubmitting ? 'Submitting...' : 'Submit Request'}
                  </button>
                  <button 
                    type="button" 
                    onClick={saveDraft}
                    className="flex-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-6 py-3 rounded-lg font-medium hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all"
                  >
                    <i className="ri-save-line mr-2"></i>
                    Save as Draft
                  </button>
                </div>
              </form>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Quick Actions */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
                <i className="ri-lightning-line mr-2 text-primary"></i>
                Quick Actions
              </h3>
              <div className="space-y-3">
                <Link href="/customer/help" className="flex items-center p-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors">
                  <i className="ri-question-line mr-3 text-primary"></i>
                  View FAQ
                </Link>
                <Link href="/customer/documents" className="flex items-center p-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors">
                  <i className="ri-book-line mr-3 text-primary"></i>
                  Resource Library
                </Link>
                <Link href="/customer/help" className="flex items-center p-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors">
                  <i className="ri-customer-service-line mr-3 text-primary"></i>
                  Help Center
                </Link>
                <Link href="/customer/resources/history" className="flex items-center p-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors">
                  <i className="ri-time-line mr-3 text-primary"></i>
                  Request History
                </Link>
              </div>
            </div>

            {/* Response Times */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
                <i className="ri-time-line mr-2 text-primary"></i>
                Expected Response Times
              </h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100">Low Priority</span>
                  </div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">5-7 days</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-yellow-500 rounded-full mr-3"></div>
                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100">Medium Priority</span>
                  </div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">2-3 days</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-red-500 rounded-full mr-3"></div>
                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100">High Priority</span>
                  </div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">24 hours</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Success Modal */}
        {showSuccessModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white dark:bg-gray-800 rounded-xl p-8 max-w-md mx-4">
              <div className="text-center">
                <div className="w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <i className="ri-check-line text-2xl text-green-600"></i>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">Request Submitted Successfully</h3>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  Your request has been submitted and assigned ticket number <strong>{ticketNumber}</strong>. You will receive updates via email.
                </p>
                <button 
                  onClick={closeSuccessModal}
                  className="w-full bg-primary text-white px-6 py-3 rounded-lg font-medium hover:bg-opacity-90 transition-all"
                >
                  Continue
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </CustomerLayout>
  );
};

export default RequestResourcePage;