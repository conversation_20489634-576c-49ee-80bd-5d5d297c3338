'use client';

import { Suspense } from 'react';
import Image from 'next/image';

function LoginLandingForm() {

  // Service data
  const services = [
    {
      icon: "fas fa-mail-bulk",
      title: "Postal",
      description: "Apply for Courier and Posts licenses for mail services."
    },
    {
      icon: "fas fa-broadcast-tower",
      title: "Telecommunications",
      description: "Get Telecommunications, Spectrum and Radio Dealer licenses."
    },
    {
      icon: "fas fa-file-alt",
      title: "Standards",
      description: "Apply for Type Approval Certificates and Short Codes."
    },
    {
      icon: "fas fa-wifi",
      title: "Converged Licensing Framework",
      description: "Get CLF licenses for telecom infrastructure and services."
    },
    {
      icon: "fas fa-handshake",
      title: "Procurement",
      description: "Submit bids for contracts and track applications."
    },
    {
      icon: "fas fa-users",
      title: "Consumer Affairs",
      description: "Lodge complaints and resolve telecom issues."
    }
  ];



  return (
    <>
      <style jsx>{`
        @keyframes float {
          0%, 100% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-10px);
          }
        }
        
        .floating-header {
          animation: float 3s ease-in-out infinite;
        }
        
        .services-carousel {
          display: flex;
          animation: scroll 30s linear infinite;
        }
        
        @keyframes scroll {
          0% {
            transform: translateX(0);
          }
          100% {
            transform: translateX(-200%);
          }
        }
        
        .carousel-slide {
          flex-shrink: 0;
          width: calc(100% / 3);
          padding: 0 10px;
        }
        

        
        @keyframes pulse {
          0%, 100% {
            transform: scale(1);
          }
          50% {
            transform: scale(1.05);
          }
        }
        
        .service-card:hover {
          animation: pulse 0.6s ease-in-out;
        }
      `}</style>



      <div className="min-h-screen bg-gray-50 font-inter overflow-x-hidden overflow-y-auto flex flex-col">
      {/* Top Header Bar */}
      <div className="bg-white py-4 border-b border-gray-200 shadow-sm">
        <div className="max-w-6xl mx-auto px-5 flex justify-between items-center">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-white rounded-full flex items-center justify-center shadow-md p-1">
              <Image 
                src="/images/macra-logo.png" 
                alt="MACRA Logo" 
                width={40} 
                height={40}
                className="max-w-full max-h-full object-contain" 
              />
            </div>
            <div className="flex flex-col">
              <h1 className="text-2xl font-bold text-red-600 m-0 leading-none">MACRA</h1>
              <p className="text-xs text-red-600 m-0 font-medium">Digital Portal</p>
            </div>
          </div>
          <div className="flex gap-2.5">
            <a 
              href="/customer/auth/login" 
              className="px-4 py-2 bg-transparent text-gray-700 border border-gray-300 rounded-lg text-sm font-semibold cursor-pointer transition-all duration-300 no-underline inline-block text-center hover:bg-gray-50 hover:border-gray-400"
            >
              Log In
            </a>
            <a 
              href="/customer/auth/signup" 
              className="px-4 py-2 bg-primary text-white border-none rounded-lg text-sm font-semibold cursor-pointer transition-all duration-300 no-underline inline-block text-center hover:bg-red-600 hover:-translate-y-0.5 hover:shadow-lg"
            >
              Sign Up
            </a>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex items-center justify-center px-5 py-6">
        <div className="max-w-6xl w-full">
          {/* Services Section - Centered */}
          <div className="text-center text-gray-700">
            <h3 className="floating-header text-3xl font-bold text-red-600 text-center my-4 relative after:content-[''] after:absolute after:-bottom-2 after:left-1/2 after:transform after:-translate-x-1/2 after:w-20 after:h-0.5 after:bg-red-600 after:rounded-sm">
              Services
            </h3>
            <p className="text-base leading-relaxed text-gray-500 max-w-3xl mx-auto mb-6 text-center">
              Access licensing and information services across Postal, Telecommunications, Standards, CLF, Procurement, and Consumer Affairs. Create an account to get started.
            </p>
            
            {/* Service Categories - Infinite 3-Box Carousel */}
            <div className="relative max-w-5xl mx-auto">
              {/* Carousel Container */}
              <div className="services-carousel-container relative h-56 overflow-hidden">
                <div className="services-carousel">
                  {/* Create infinite loop by duplicating services */}
                  {[...services, ...services, ...services].map((service, index) => (
                    <div key={index} className="carousel-slide flex justify-center">
                      <div className="service-card bg-white rounded-lg p-5 text-center shadow-md border border-gray-200 transition-all duration-300 cursor-pointer w-72 min-h-[180px] flex flex-col justify-center hover:-translate-y-1 hover:shadow-lg hover:border-red-600 group">
                        <div className="w-14 h-14 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-3 text-white text-xl group-hover:bg-red-700 group-hover:shadow-lg transition-all duration-300">
                          <i className={service.icon}></i>
                        </div>
                        <h4 className="text-base font-semibold text-red-600 mb-2">{service.title}</h4>
                        <p className="text-xs text-gray-500 m-0 leading-relaxed">{service.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="bg-white border-t border-gray-200 py-4">
        <div className="max-w-6xl mx-auto px-5 text-center">
          <p className="text-gray-500 text-sm">
            © 2024 MACRA Digital Portal. All rights reserved.
          </p>
        </div>
      </div>
    </div>
    </>
  );
}

export default function HomePage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <LoginLandingForm />
    </Suspense>
  );
}