'use client';

import { useEffect, useRef } from 'react';

interface ChartContainerProps {
  title: string;
  children?: React.ReactNode;
  controls?: React.ReactNode;
  height?: string;
  className?: string;
}

const ChartContainer = ({ 
  title, 
  children, 
  controls, 
  height = '250px',
  className = '' 
}: ChartContainerProps) => {
  return (
    <div className={`bg-white rounded-lg shadow overflow-hidden ${className}`}>
      <div className="p-6">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">
            {title}
          </h3>
          {controls && (
            <div className="flex space-x-3">
              {controls}
            </div>
          )}
        </div>
        <div className="mt-4" style={{ height }}>
          {children}
        </div>
      </div>
    </div>
  );
};

// License Status Chart Component
export const LicenseStatusChart = () => {
  const chartRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (typeof window !== 'undefined' && chartRef.current) {
      // This would be where you'd initialize your chart library (e.g., ECharts, Chart.js)
      // For now, we'll show a placeholder
      const chartElement = chartRef.current;
      chartElement.innerHTML = `
        <div class="flex items-center justify-center h-full bg-gray-50 rounded-lg">
          <div class="text-center">
            <div class="text-4xl text-gray-400 mb-2">📊</div>
            <p class="text-gray-500">License Status Chart</p>
            <p class="text-xs text-gray-400">Chart implementation pending</p>
          </div>
        </div>
      `;
    }
  }, []);

  const controls = (
    <>
      <button
        type="button"
        className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-button text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 whitespace-nowrap"
      >
        Daily
      </button>
      <button
        type="button"
        className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-button text-white bg-primary hover:bg-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 whitespace-nowrap"
      >
        Weekly
      </button>
      <button
        type="button"
        className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-button text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 whitespace-nowrap"
      >
        Monthly
      </button>
    </>
  );

  return (
    <ChartContainer title="License Status Overview" controls={controls}>
      <div ref={chartRef} className="w-full h-full"></div>
    </ChartContainer>
  );
};

// Spectrum Usage Chart Component
export const SpectrumUsageChart = () => {
  const chartRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (typeof window !== 'undefined' && chartRef.current) {
      const chartElement = chartRef.current;
      chartElement.innerHTML = `
        <div class="flex items-center justify-center h-full bg-gray-50 rounded-lg">
          <div class="text-center">
            <div class="text-4xl text-gray-400 mb-2">📡</div>
            <p class="text-gray-500">Spectrum Usage Chart</p>
            <p class="text-xs text-gray-400">Chart implementation pending</p>
          </div>
        </div>
      `;
    }
  }, []);

  const controls = (
    <select className="block pl-3 pr-10 py-1.5 text-xs border-gray-300 focus:outline-none focus:ring-red-500 focus:border-red-500 rounded-md">
      <option>All Bands</option>
      <option>VHF</option>
      <option>UHF</option>
      <option>SHF</option>
      <option>EHF</option>
    </select>
  );

  return (
    <ChartContainer title="Spectrum Usage Overview" controls={controls}>
      <div ref={chartRef} className="w-full h-full"></div>
    </ChartContainer>
  );
};

// Revenue Trends Chart Component
export const RevenueTrendsChart = () => {
  const chartRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (typeof window !== 'undefined' && chartRef.current) {
      const chartElement = chartRef.current;
      chartElement.innerHTML = `
        <div class="flex items-center justify-center h-full bg-gray-50 rounded-lg">
          <div class="text-center">
            <div class="text-4xl text-gray-400 mb-2">💰</div>
            <p class="text-gray-500">Revenue Trends Chart</p>
            <p class="text-xs text-gray-400">Chart implementation pending</p>
          </div>
        </div>
      `;
    }
  }, []);

  const controls = (
    <>
      <button
        type="button"
        className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-button text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 whitespace-nowrap"
      >
        Quarter
      </button>
      <button
        type="button"
        className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-button text-white bg-primary hover:bg-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 whitespace-nowrap"
      >
        Year
      </button>
      <button
        type="button"
        className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-button text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 whitespace-nowrap"
      >
        All Time
      </button>
    </>
  );

  return (
    <ChartContainer title="Revenue Trends" controls={controls}>
      <div ref={chartRef} className="w-full h-full"></div>
    </ChartContainer>
  );
};

export default ChartContainer;
