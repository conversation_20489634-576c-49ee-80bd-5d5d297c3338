'use client';

import { ReactNode } from 'react';
import { AuthProvider } from '../contexts/AuthContext';
import { LoadingProvider } from '../contexts/LoadingContext';
import { ThemeProvider } from '../lib/ThemeContext';
import { ToastProvider } from '../contexts/ToastContext';
import Loader from './Loader';
import NoSSR from './NoSSR';

interface ClientWrapperProps {
  children: ReactNode;
}

export default function ClientWrapper({ children }: ClientWrapperProps) {
  return (
    <NoSSR fallback={
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <Loader message="Initializing application..." />
      </div>
    }>
      <ThemeProvider>
        <LoadingProvider>
          <ToastProvider>
            <AuthProvider>
              {children}
            </AuthProvider>
          </ToastProvider>
        </LoadingProvider>
      </ThemeProvider>
    </NoSSR>
  );
}
