'use client';

import Link from 'next/link';

interface ExportCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  recordCount: string;
  lastExport: string;
  bgGradient: string;
  borderColor: string;
  iconBgColor: string;
  badgeColor: string;
  arrowColor: string;
  onClick?: () => void;
  href?: string;
}

const ExportCard = ({
  title,
  description,
  icon,
  recordCount,
  lastExport,
  bgGradient,
  borderColor,
  iconBgColor,
  badgeColor,
  arrowColor,
  onClick,
  href
}: ExportCardProps) => {
  const CardContent = () => (
    <div className={`${bgGradient} rounded-lg p-4 border ${borderColor} hover:shadow-md transition-shadow cursor-pointer`}>
      <div className="flex items-center justify-between mb-3">
        <div className={`w-10 h-10 ${iconBgColor} rounded-lg flex items-center justify-center`}>
          {icon}
        </div>
        <span className={`text-xs font-medium ${badgeColor} px-2 py-1 rounded-full`}>
          {recordCount}
        </span>
      </div>
      <h4 className="text-sm font-medium text-gray-900 mb-1">{title}</h4>
      <p className="text-xs text-gray-600 mb-3">{description}</p>
      <div className="flex items-center justify-between">
        <span className="text-xs text-gray-500">{lastExport}</span>
        <i className={`ri-arrow-right-line ${arrowColor}`}></i>
      </div>
    </div>
  );

  if (href) {
    return (
      <Link href={href}>
        <CardContent />
      </Link>
    );
  }

  return (
    <div onClick={onClick}>
      <CardContent />
    </div>
  );
};

interface ExportCenterProps {
  className?: string;
}

const ExportCenter = ({ className = '' }: ExportCenterProps) => {
  const exportCards = [
    {
      title: 'Financial Transactions',
      description: 'Export payments, invoices, and financial reports',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-6 h-6 text-white">
          <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.086-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
        </svg>
      ),
      recordCount: '4,892 records',
      lastExport: 'Last export: 2 days ago',
      bgGradient: 'bg-gradient-to-br from-green-50 to-green-100',
      borderColor: 'border-green-200',
      iconBgColor: 'bg-green-500',
      badgeColor: 'text-green-700 bg-green-200',
      arrowColor: 'text-green-600',
      href: '/dashboard/financial/export'
    },
    {
      title: 'Spectrum Data',
      description: 'Export frequency allocations and usage data',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-6 h-6 text-white">
          <path strokeLinecap="round" strokeLinejoin="round" d="M9.348 14.652a3.75 3.75 0 0 1 0-5.304m5.304 0a3.75 3.75 0 0 1 0 5.304m-7.425 2.121a6.75 6.75 0 0 1 0-9.546m9.546 0a6.75 6.75 0 0 1 0 9.546M5.106 18.894c-3.808-3.807-3.808-9.98 0-13.788m13.788 0c3.808 3.807 3.808 9.98 0 13.788M12 12h.008v.008H12V12Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z" />
        </svg>
      ),
      recordCount: '1,248 allocations',
      lastExport: 'Last export: 1 week ago',
      bgGradient: 'bg-gradient-to-br from-blue-50 to-blue-100',
      borderColor: 'border-blue-200',
      iconBgColor: 'bg-blue-500',
      badgeColor: 'text-blue-700 bg-blue-200',
      arrowColor: 'text-blue-600',
      href: '/dashboard/spectrum/export'
    },
    {
      title: 'License Data',
      description: 'Export license records and compliance data',
      icon: <i className="ri-key-line text-white text-lg"></i>,
      recordCount: '1,482 licenses',
      lastExport: 'Coming soon',
      bgGradient: 'bg-gradient-to-br from-purple-50 to-purple-100',
      borderColor: 'border-purple-200',
      iconBgColor: 'bg-purple-500',
      badgeColor: 'text-purple-700 bg-purple-200',
      arrowColor: 'text-purple-600',
      onClick: () => alert('License export feature coming soon!')
    }
  ];

  const handleQuickAction = (action: string) => {
    alert(`${action} generation coming soon!`);
  };

  return (
    <div className={`bg-white shadow rounded-lg mb-6 overflow-hidden ${className}`}>
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-medium leading-4 text-gray-900">Export Center</h3>
            <p className="mt-1 text-sm text-gray-500">Generate comprehensive reports and export data for analysis</p>
          </div>
          <div className="flex space-x-3">
            <button 
              type="button" 
              onClick={() => handleQuickAction('Export History')}
              className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-button text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              <i className="ri-history-line mr-1"></i>
              Export History
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
          {exportCards.map((card, index) => (
            <ExportCard key={index} {...card} />
          ))}
        </div>

        {/* Quick Export Actions */}
        <div className="mt-6 pt-6 border-t border-gray-200">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
            <div>
              <h4 className="text-sm font-medium text-gray-900">Quick Actions</h4>
              <p className="text-xs text-gray-500">Generate standard reports and access export tools</p>
            </div>
            <div className="flex space-x-3">
              <button 
                type="button" 
                onClick={() => handleQuickAction('Monthly Report')}
                className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-button text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                <i className="ri-file-excel-line mr-1"></i>
                Monthly Report
              </button>
              <button 
                type="button" 
                onClick={() => handleQuickAction('Compliance Report')}
                className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-button text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                <i className="ri-file-pdf-line mr-1"></i>
                Compliance Report
              </button>
              <Link 
                href="/dashboard/reports"
                className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-button text-white bg-primary hover:bg-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                <i className="ri-bar-chart-line mr-1"></i>
                View All Reports
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExportCenter;
