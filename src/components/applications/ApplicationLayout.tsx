'use client';

import React, { Suspense } from 'react';
import ApplicationProgress from './ApplicationProgress';

interface ApplicationLayoutProps {
  children: React.ReactNode;
  onSubmit?: () => void;
  onSave?: () => void;
  onNext?: () => void;
  onPrevious?: () => void;
  isSubmitting?: boolean;
  isSaving?: boolean;
  showNextButton?: boolean;
  showPreviousButton?: boolean;
  showSaveButton?: boolean;
  showSubmitButton?: boolean;
  nextButtonText?: string;
  previousButtonText?: string;
  saveButtonText?: string;
  submitButtonText?: string;
  nextButtonDisabled?: boolean;
  previousButtonDisabled?: boolean;
  saveButtonDisabled?: boolean;
  submitButtonDisabled?: boolean;
  className?: string;
  showProgress?: boolean; // Allow disabling progress for better performance
  progressFallback?: React.ReactNode; // Custom loading fallback

  // Enhanced props for optimized step configuration
  licenseTypeCode?: string; // For step validation and navigation
  currentStepRoute?: string; // Current step identifier
  stepValidationErrors?: string[]; // Step-specific validation errors
  showStepInfo?: boolean; // Show step information and requirements
}

// Progress Loading Fallback Component
const ProgressLoadingFallback: React.FC = () => (
  <div className="mb-8">
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
      <div className="animate-pulse">
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-4"></div>
        <div className="space-y-2">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="flex items-center p-2">
              <div className="w-6 h-6 bg-gray-200 dark:bg-gray-700 rounded-full mr-3"></div>
              <div className="flex-1">
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3 mb-1"></div>
                <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  </div>
);

const ApplicationLayout: React.FC<ApplicationLayoutProps> = ({
  children,
  onSubmit,
  onSave,
  onNext,
  onPrevious,
  isSubmitting = false,
  isSaving = false,
  showNextButton = true,
  showPreviousButton = true,
  showSaveButton = false,
  showSubmitButton = false,
  nextButtonText = 'Next',
  previousButtonText = 'Previous',
  saveButtonText = 'Save',
  submitButtonText = 'Submit',
  nextButtonDisabled = false,
  previousButtonDisabled = false,
  saveButtonDisabled = false,
  submitButtonDisabled = false,
  className = '',
  showProgress = true,
  progressFallback,
  licenseTypeCode,
  currentStepRoute,
  stepValidationErrors = [],
  showStepInfo = false
}) => {
  return (
    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 ${className}`}>
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Progress Steps - Left Sidebar */}
          {showProgress && (
            <div className="lg:col-span-1">
              <div className="sticky top-8">
                <Suspense fallback={progressFallback || <ProgressLoadingFallback />}>
                  <ApplicationProgress />
                </Suspense>
              </div>
            </div>
          )}

          {/* Main Content Area */}
          <div className={showProgress ? "lg:col-span-3" : "lg:col-span-4"}>
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              {/* Step Information Banner */}
              {showStepInfo && licenseTypeCode && currentStepRoute && (
                <div className="border-b border-gray-200 dark:border-gray-700 p-4 bg-blue-50 dark:bg-blue-900/20">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-sm font-medium text-blue-900 dark:text-blue-100">
                        License Type: {licenseTypeCode.replace(/_/g, ' ').toUpperCase()}
                      </h3>
                      <p className="text-xs text-blue-700 dark:text-blue-300 mt-1">
                        Current Step: {currentStepRoute.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </p>
                    </div>
                    <div className="text-xs text-blue-600 dark:text-blue-400">
                      <i className="ri-information-line mr-1"></i>
                      Optimized Configuration Active
                    </div>
                  </div>
                </div>
              )}

              {/* Validation Errors */}
              {stepValidationErrors.length > 0 && (
                <div className="border-b border-gray-200 dark:border-gray-700 p-4 bg-red-50 dark:bg-red-900/20">
                  <div className="flex items-start">
                    <i className="ri-error-warning-line text-red-500 mr-2 mt-0.5"></i>
                    <div>
                      <h4 className="text-sm font-medium text-red-900 dark:text-red-100 mb-2">
                        Please fix the following issues:
                      </h4>
                      <ul className="text-xs text-red-700 dark:text-red-300 space-y-1">
                        {stepValidationErrors.map((error, index) => (
                          <li key={index}>• {error}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              )}

              {/* Form Content */}
              <div className="p-6">
                {children}
              </div>

              {/* Footer with Action Buttons */}
              <div className="px-6 py-4 bg-gray-50 dark:bg-gray-700/50 border-t border-gray-200 dark:border-gray-600 rounded-b-lg">
                <div className="flex items-center justify-between">
                  {/* Left Side - Previous Button */}
                  <div>
                    {showPreviousButton && onPrevious && (
                      <button
                        type="button"
                        onClick={onPrevious}
                        disabled={previousButtonDisabled}
                        className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <i className="ri-arrow-left-line mr-2"></i>
                        {previousButtonText}
                      </button>
                    )}
                  </div>

                  {/* Right Side - Action Buttons */}
                  <div className="flex items-center space-x-3">
                    {/* Save Button */}
                    {showSaveButton && onSave && (
                      <button
                        type="button"
                        onClick={() => {
                          console.log('🔘 Save button clicked in ApplicationLayout');
                          onSave();
                        }}
                        disabled={saveButtonDisabled || isSaving}
                        className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isSaving ? (
                          <>
                            <i className="ri-loader-4-line animate-spin mr-2"></i>
                            Saving...
                          </>
                        ) : (
                          <>
                            <i className="ri-save-line mr-2"></i>
                            {saveButtonText}
                          </>
                        )}
                      </button>
                    )}

                    {/* Submit Button */}
                    {showSubmitButton && onSubmit && (
                      <button
                        type="button"
                        onClick={onSubmit}
                        disabled={submitButtonDisabled || isSubmitting}
                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isSubmitting ? (
                          <>
                            <i className="ri-loader-4-line animate-spin mr-2"></i>
                            Submitting...
                          </>
                        ) : (
                          <>
                            <i className="ri-send-plane-line mr-2"></i>
                            {submitButtonText}
                          </>
                        )}
                      </button>
                    )}

                    {/* Next Button */}
                    {showNextButton && onNext && (
                      <button
                        type="button"
                        onClick={() => {
                          console.log('🔘 Next button clicked in ApplicationLayout');
                          onNext();
                        }}
                        disabled={nextButtonDisabled}
                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {nextButtonText}
                        <i className="ri-arrow-right-line ml-2"></i>
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ApplicationLayout;
