'use client';

import { useState, useEffect } from 'react';
import { Application } from '../../types/license';
import { applicationService } from '../../services/applicationService';

interface ApplicationViewModalProps {
  isOpen: boolean;
  onClose: () => void;
  applicationId: string | null;
}

interface ApplicationDetails extends Application {
  applicant_details?: any;
  company_profile?: any;
  business_info?: any;
  service_scope?: any;
  business_plan?: any;
  legal_history?: any;
  documents?: any[];
}

export default function ApplicationViewModal({ 
  isOpen, 
  onClose, 
  applicationId 
}: ApplicationViewModalProps) {
  const [application, setApplication] = useState<ApplicationDetails | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    if (isOpen && applicationId) {
      fetchApplicationDetails();
    }
  }, [isOpen, applicationId]);

  const fetchApplicationDetails = async () => {
    if (!applicationId) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const response = await applicationService.getApplicationById(applicationId);
      setApplication(response);
    } catch (err: any) {
      console.error('Error fetching application details:', err);
      setError('Failed to load application details');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setApplication(null);
    setError(null);
    setActiveTab('overview');
    onClose();
  };

  if (!isOpen) return null;

  const tabs = [
    { id: 'overview', label: 'Overview', icon: 'ri-file-text-line' },
    { id: 'applicant', label: 'Applicant Details', icon: 'ri-user-line' },
    { id: 'company', label: 'Company Profile', icon: 'ri-building-line' },
    { id: 'business', label: 'Business Info', icon: 'ri-briefcase-line' },
    { id: 'service', label: 'Service Scope', icon: 'ri-service-line' },
    { id: 'plan', label: 'Business Plan', icon: 'ri-file-chart-line' },
    { id: 'legal', label: 'Legal History', icon: 'ri-scales-line' },
    { id: 'documents', label: 'Documents', icon: 'ri-folder-line' }
  ];

  const getStatusBadge = (status: string) => {
    const statusClasses: Record<string, string> = {
      'draft': 'bg-gray-100 text-gray-800',
      'submitted': 'bg-blue-100 text-blue-800',
      'under_review': 'bg-yellow-100 text-yellow-800',
      'evaluation': 'bg-purple-100 text-purple-800',
      'approved': 'bg-green-100 text-green-800',
      'rejected': 'bg-red-100 text-red-800',
      'withdrawn': 'bg-gray-100 text-gray-800',
    };

    return (
      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusClasses[status] || 'bg-gray-100 text-gray-800'}`}>
        {status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
      </span>
    );
  };

  const renderOverviewTab = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
          <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">Application Information</h4>
          <div className="space-y-2 text-sm">
            <div><span className="font-medium">Application Number:</span> {application?.application_number}</div>
            <div><span className="font-medium">Status:</span> {getStatusBadge(application?.status || '')}</div>
            <div><span className="font-medium">Progress:</span> {application?.progress_percentage}%</div>
            <div><span className="font-medium">Current Step:</span> {application?.current_step}</div>
            <div><span className="font-medium">Submitted:</span> {application?.submitted_at ? new Date(application.submitted_at).toLocaleDateString() : 'Not submitted'}</div>
          </div>
        </div>
        
        <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
          <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">License Information</h4>
          <div className="space-y-2 text-sm">
            <div><span className="font-medium">License Type:</span> {application?.license_category?.license_type?.name || 'N/A'}</div>
            <div><span className="font-medium">License Category:</span> {application?.license_category?.name || 'N/A'}</div>
            <div><span className="font-medium">Description:</span> {application?.license_category?.description || 'N/A'}</div>
          </div>
        </div>
      </div>

      <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">Applicant Information</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div><span className="font-medium">Name:</span> {application?.applicant?.name || 'N/A'}</div>
          <div><span className="font-medium">Email:</span> {application?.applicant?.email || 'N/A'}</div>
          <div><span className="font-medium">Phone:</span> {application?.applicant?.phone || 'N/A'}</div>
          <div><span className="font-medium">Business Registration:</span> {application?.applicant?.business_registration_number || 'N/A'}</div>
          <div><span className="font-medium">TPIN:</span> {application?.applicant?.tpin || 'N/A'}</div>
          <div><span className="font-medium">Address:</span> {application?.applicant?.address || 'N/A'}</div>
        </div>
      </div>
    </div>
  );

  const renderTabContent = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading application details...</p>
          </div>
        </div>
      );
    }

    if (error) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <i className="ri-error-warning-line text-4xl text-red-500 mb-4"></i>
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Error Loading Application</h3>
            <p className="text-gray-500 dark:text-gray-400">{error}</p>
            <button 
              onClick={fetchApplicationDetails}
              className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Try Again
            </button>
          </div>
        </div>
      );
    }

    if (!application) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <i className="ri-file-line text-4xl text-gray-400 mb-4"></i>
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No Application Data</h3>
            <p className="text-gray-500 dark:text-gray-400">Application details could not be found.</p>
          </div>
        </div>
      );
    }

    switch (activeTab) {
      case 'overview':
        return renderOverviewTab();
      case 'applicant':
        return (
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100">Applicant Details</h4>
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              <pre className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                {JSON.stringify(application.applicant_details || application.applicant, null, 2)}
              </pre>
            </div>
          </div>
        );
      case 'company':
        return (
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100">Company Profile</h4>
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              <pre className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                {JSON.stringify(application.company_profile, null, 2)}
              </pre>
            </div>
          </div>
        );
      case 'business':
        return (
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100">Business Information</h4>
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              <pre className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                {JSON.stringify(application.business_info, null, 2)}
              </pre>
            </div>
          </div>
        );
      case 'service':
        return (
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100">Service Scope</h4>
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              <pre className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                {JSON.stringify(application.service_scope, null, 2)}
              </pre>
            </div>
          </div>
        );
      case 'plan':
        return (
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100">Business Plan</h4>
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              <pre className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                {JSON.stringify(application.business_plan, null, 2)}
              </pre>
            </div>
          </div>
        );
      case 'legal':
        return (
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100">Legal History</h4>
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              <pre className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                {JSON.stringify(application.legal_history, null, 2)}
              </pre>
            </div>
          </div>
        );
      case 'documents':
        return (
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100">Documents</h4>
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              {application.documents && application.documents.length > 0 ? (
                <div className="space-y-2">
                  {application.documents.map((doc: any, index: number) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-white dark:bg-gray-600 rounded">
                      <div className="flex items-center">
                        <i className="ri-file-line mr-2"></i>
                        <span className="text-sm">{doc.name || `Document ${index + 1}`}</span>
                      </div>
                      <button className="text-blue-600 hover:text-blue-800 text-sm">
                        <i className="ri-download-line mr-1"></i>
                        Download
                      </button>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 dark:text-gray-400 text-sm">No documents uploaded</p>
              )}
            </div>
          </div>
        );
      default:
        return renderOverviewTab();
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg w-full max-w-6xl max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Application Details
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              {application?.application_number || 'Loading...'}
            </p>
          </div>
          <button
            type="button"
            onClick={handleClose}
            aria-label="Close modal"
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <i className="ri-close-line text-xl"></i>
          </button>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="flex space-x-8 px-6" aria-label="Tabs">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:border-gray-300'
                }`}
              >
                <i className={`${tab.icon} mr-2`}></i>
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {renderTabContent()}
        </div>
      </div>
    </div>
  );
}
