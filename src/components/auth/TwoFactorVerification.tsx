'use client';

import { useState, useEffect, useCallback } from 'react';
import { authService } from '@/services/auth.service';
import { customerApi } from '@/lib/customer-api';
import TextInput from '../forms/TextInput';

// Type for 2FA user data that can handle both staff and customer user formats
type TwoFactorUserData = {
  // Common properties
  email: string;
  roles: string[];
  isAdmin?: boolean;
  two_factor_enabled?: boolean;

  // Staff user properties (from auth.service.ts)
  user_id?: string;
  first_name?: string;
  last_name?: string;
  middle_name?: string;
  phone?: string;
  status?: string;
  profile_image?: string;

  // Customer user properties (from customer-api.ts)
  id?: string;
  firstName?: string;
  lastName?: string;
  organizationName?: string;
  profileImage?: string;
  createdAt?: string;
  lastLogin?: string;
  address?: string;
  city?: string;
  country?: string;
};

interface TwoFactorVerificationProps {
  userId: string;
  email: string;
  isCustomerPortal?: boolean;
  onSuccess: (token: string, user: TwoFactorUserData) => void;
  onCancel: () => void;
  onError: (error: string) => void;
}

export default function TwoFactorVerification({
  userId,
  email,
  isCustomerPortal = false,
  onSuccess,
  onCancel,
  onError
}: TwoFactorVerificationProps) {
  const [code, setCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [resendLoading, setResendLoading] = useState(false);
  const [error, setError] = useState('');
  const [timeLeft, setTimeLeft] = useState(300); // 5 minutes
  const [canResend, setCanResend] = useState(false);

  // Countdown timer
  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
      return () => clearTimeout(timer);
    } else {
      setCanResend(true);
    }
  }, [timeLeft]);

  const generateTwoFactorCode = useCallback(async () => {
    try {
      setResendLoading(true);
      const service = isCustomerPortal ? customerApi : authService;
      const response = await service.generateTwoFactorCode(userId, 'login');
      console.log('2FA code generated:', response);
      setTimeLeft(300); // Reset timer
      setCanResend(false);
      setError('');
    } catch (err: unknown) {
      let errorMessage = 'Failed to generate 2FA code';
      type ErrorWithResponse = {
        response?: {
          data?: {
            message?: string;
          };
        };
        message?: string;
      };
      if (typeof err === 'object' && err !== null) {
        const typedErr = err as ErrorWithResponse;
        if ('response' in typedErr && typeof typedErr.response === 'object' && typedErr.response !== null) {
          errorMessage = typedErr.response?.data?.message || typedErr.message || errorMessage;
        } else if ('message' in typedErr) {
          errorMessage = typedErr.message || errorMessage;
        }
      }
      setError(errorMessage);
      onError(errorMessage);
    } finally {
      setResendLoading(false);
    }
  }, [userId, isCustomerPortal, onError]);

  // Auto-generate 2FA code on component mount
  useEffect(() => {
    generateTwoFactorCode();
  }, [generateTwoFactorCode]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!code.trim()) {
      setError('Please enter the verification code');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const service = isCustomerPortal ? customerApi : authService;
      const response = await service.verify2FA({
        user_id: userId,
        code: code.trim(),
        unique: '' // This will be handled by the backend based on the generated code
      });

      if (response.access_token && response.user) {
        onSuccess(response.access_token, response.user);
      } else {
        throw new Error('Invalid response from server');
      }
    } catch (err: unknown) {
      let errorMessage = 'Invalid verification code';
      if (typeof err === 'object' && err !== null) {
        const typedErr = err as { response?: { data?: { message?: string } }, message?: string };
        errorMessage = typedErr.response?.data?.message || typedErr.message || errorMessage;
      }
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleResend = async () => {
    if (!canResend) return;
    await generateTwoFactorCode();
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="max-w-md mx-auto">
      <div className="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-6">
        <div className="text-center mb-6">
          <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
            <i className="ri-shield-check-line text-2xl text-blue-600 dark:text-blue-400"></i>
          </div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            Two-Factor Authentication
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            We&apos;ve sent a verification code to <strong>{email}</strong>
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <TextInput
              type="text"
              label="Verification Code"
              value={code}
              onChange={(e) => {
                setCode(e.target.value);
                setError('');
              }}
              placeholder="Enter 6-digit code"
              className="text-center text-lg tracking-widest"
              maxLength={6}
              autoComplete="one-time-code"
              disabled={loading}
            />
          </div>

          {error && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 px-4 py-3 rounded-lg text-sm">
              {error}
            </div>
          )}

          <div className="flex flex-col space-y-3">
            <button
              type="submit"
              disabled={loading || !code.trim()}
              className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center"
            >
              {loading ? (
                <>
                  <i className="ri-loader-4-line animate-spin mr-2"></i>
                  Verifying...
                </>
              ) : (
                <>
                  <i className="ri-check-line mr-2"></i>
                  Verify Code
                </>
              )}
            </button>

            <button
              type="button"
              onClick={onCancel}
              className="w-full bg-gray-200 hover:bg-gray-300 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 font-medium py-2 px-4 rounded-lg transition-colors duration-200"
            >
              Cancel
            </button>
          </div>
        </form>

        <div className="mt-6 text-center">
          <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
            {timeLeft > 0 ? (
              <span>Code expires in {formatTime(timeLeft)}</span>
            ) : (
              <span className="text-red-600 dark:text-red-400">Code has expired</span>
            )}
          </div>
          
          <button
            type="button"
            onClick={handleResend}
            disabled={!canResend || resendLoading}
            className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 font-medium text-sm disabled:text-gray-400 disabled:cursor-not-allowed"
          >
            {resendLoading ? (
              <>
                <i className="ri-loader-4-line animate-spin mr-1"></i>
                Sending...
              </>
            ) : (
              <>
                <i className="ri-refresh-line mr-1"></i>
                Resend Code
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
