'use client';

import React, { useState } from 'react';
import Pagination from './Pagination';

// Example component showing how to use the Pagination component
const PaginationExample: React.FC = () => {
  // Example pagination data
  const [paginationData, setPaginationData] = useState({
    itemsPerPage: 10,
    totalItems: 247,
    currentPage: 5,
    totalPages: 25,
    sortBy: [['name', 'ASC']] as [string, string][],
    searchBy: ['name', 'email'],
    search: '',
    filter: {}
  });

  const handlePageChange = (page: number) => {
    console.log('Page changed to:', page);
    setPaginationData(prev => ({
      ...prev,
      currentPage: page
    }));
  };

  const handlePageSizeChange = (pageSize: number) => {
    console.log('Page size changed to:', pageSize);
    const newTotalPages = Math.ceil(paginationData.totalItems / pageSize);
    setPaginationData(prev => ({
      ...prev,
      itemsPerPage: pageSize,
      totalPages: newTotalPages,
      currentPage: 1 // Reset to first page when changing page size
    }));
  };

  return (
    <div className="p-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          Pagination Component Examples
        </h1>

        {/* Example 1: Full featured pagination */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow mb-6">
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Full Featured Pagination
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Shows all features: page info, page size selector, first/last buttons
            </p>
          </div>
          <div className="p-4">
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded mb-4">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                This would be your data table content here...
              </p>
            </div>
            <Pagination
              meta={paginationData}
              onPageChange={handlePageChange}
              onPageSizeChange={handlePageSizeChange}
              showFirstLast={true}
              showPageSizeSelector={true}
              showInfo={true}
              maxVisiblePages={7}
              pageSizeOptions={[10, 25, 50, 100]}
            />
          </div>
        </div>

        {/* Example 2: Minimal pagination */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow mb-6">
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Minimal Pagination
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Just page navigation without extra features
            </p>
          </div>
          <div className="p-4">
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded mb-4">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                This would be your data table content here...
              </p>
            </div>
            <Pagination
              meta={paginationData}
              onPageChange={handlePageChange}
              showFirstLast={false}
              showPageSizeSelector={false}
              showInfo={false}
              maxVisiblePages={5}
            />
          </div>
        </div>

        {/* Example 3: Compact pagination */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow mb-6">
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Compact Pagination
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Shows fewer page numbers for compact display
            </p>
          </div>
          <div className="p-4">
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded mb-4">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                This would be your data table content here...
              </p>
            </div>
            <Pagination
              meta={paginationData}
              onPageChange={handlePageChange}
              onPageSizeChange={handlePageSizeChange}
              showFirstLast={true}
              showPageSizeSelector={true}
              showInfo={true}
              maxVisiblePages={3}
              pageSizeOptions={[5, 10, 20]}
            />
          </div>
        </div>

        {/* Current state display */}
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">
            Current Pagination State
          </h3>
          <pre className="text-sm text-blue-800 dark:text-blue-200 bg-blue-100 dark:bg-blue-900/40 p-3 rounded">
            {JSON.stringify(paginationData, null, 2)}
          </pre>
        </div>
      </div>
    </div>
  );
};

export default PaginationExample;
