import React from 'react';
import Link from 'next/link';

interface PaymentCardProps {
  id: string;
  title: string;
  amount: string;
  dueDate: string;
  status: 'Due' | 'Overdue' | 'Paid';
  description?: string;
}

const PaymentCard: React.FC<PaymentCardProps> = ({
  id,
  title,
  amount,
  dueDate,
  status,
  description
}) => {
  const getStatusStyles = (status: string) => {
    switch (status) {
      case 'Due':
        return {
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-100',
          iconBg: 'bg-yellow-100',
          iconColor: 'text-yellow-600',
          badgeBg: 'bg-yellow-100',
          badgeText: 'text-yellow-800'
        };
      case 'Overdue':
        return {
          bgColor: 'bg-red-50',
          borderColor: 'border-red-100',
          iconBg: 'bg-red-100',
          iconColor: 'text-red-600',
          badgeBg: 'bg-red-100',
          badgeText: 'text-red-800'
        };
      case 'Paid':
        return {
          bgColor: 'bg-green-50',
          borderColor: 'border-green-100',
          iconBg: 'bg-green-100',
          iconColor: 'text-green-600',
          badgeBg: 'bg-green-100',
          badgeText: 'text-green-800'
        };
      default:
        return {
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-100',
          iconBg: 'bg-gray-100',
          iconColor: 'text-gray-600',
          badgeBg: 'bg-gray-100',
          badgeText: 'text-gray-800'
        };
    }
  };

  const statusStyles = getStatusStyles(status);

  return (
    <div className={`${statusStyles.bgColor} border ${statusStyles.borderColor} rounded-lg p-4`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <div className={`w-8 h-8 flex items-center justify-center rounded-full ${statusStyles.iconBg} ${statusStyles.iconColor}`}>
            <i className="ri-calendar-line"></i>
          </div>
          <div className="ml-3">
            <p className="text-sm font-medium text-gray-900">{title}</p>
            <p className="text-xs text-gray-500">{description || dueDate}</p>
          </div>
        </div>
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusStyles.badgeBg} ${statusStyles.badgeText}`}>
          {amount}
        </span>
      </div>
      {status !== 'Paid' && (
        <div className="mt-2 text-right">
          <Link 
            href={`/customer/payments/${id}`} 
            className="text-xs font-medium text-primary hover:text-primary"
          >
            Pay Now
          </Link>
        </div>
      )}
    </div>
  );
};

export default PaymentCard;
