// Application Form Types
export interface ApplicantInfoData {
  applicantName: string;
  postalPoBox: string;
  postalCity: string;
  postalCountry: string;
  physicalStreet: string;
  physicalCity: string;
  physicalCountry: string;
  telephone: string;
  fax: string;
  email: string;
}

export interface ShareholderData {
  name: string;
  nationality: string;
  shares: number;
  percentage: number;
}

export interface DirectorData {
  name: string;
  nationality: string;
  position: string;
  appointmentDate: string;
}

export interface CompanyProfileData {
  shareholders: ShareholderData[];
  directors: DirectorData[];
  foreignOwnership: string;
  businessRegistrationNo: string;
  tpin: string;
  website: string;
  dateOfIncorporation: string;
  placeOfIncorporation: string;
}

export interface ManagementTeamMember {
  name: string;
  position: string;
  qualifications: string;
  experience: string;
}

export interface ManagementData {
  managementTeam: ManagementTeamMember[];
  organizationalStructure: string;
  keyPersonnel: string;
}

export interface ProfessionalServicesData {
  consultants: string;
  serviceProviders: string;
  technicalSupport: string;
  maintenanceArrangements: string;
}

export interface BusinessInfoData {
  businessDescription: string;
  operationalPlan: string;
}

export interface ServiceScopeData {
  servicesOffered: string;
  geographicCoverage: string;
  pricingStrategy: string;
}

export interface BusinessPlanData {
  marketAnalysis: string;
  financialProjections: string;
  competitiveAdvantage: string;
  riskAssessment: string;
  implementationTimeline: string;
}

export interface LegalHistoryData {
  previousViolations: string;
  courtCases: string;
  regulatoryHistory: string;
  complianceRecord: string;
}

export interface ApplicationFormData {
  applicantInfo: ApplicantInfoData;
  companyProfile: CompanyProfileData;
  management: ManagementData;
  professionalServices: ProfessionalServicesData;
  businessInfo: BusinessInfoData;
  serviceScope: ServiceScopeData;
  businessPlan: BusinessPlanData;
  legalHistory: LegalHistoryData;
}

// Component Props Interface
export interface ApplicationFormComponentProps {
  errors?: Record<string, string>;
  disabled?: boolean;
}

// Independent Step Component Props Interface
export interface IndependentStepProps {
  applicationId?: string | null;
  licenseTypeId?: string;
  licenseCategoryId: string;
  isEditMode?: boolean;
  onNext?: () => void;
  onPrevious?: () => void;
  isFirstStep?: boolean;
  isLastStep?: boolean;
  onStepComplete?: (stepId: string, data?: any) => void;
  onStepError?: (stepId: string, errors: any) => void;
  onNavigate?: (direction: 'next' | 'previous') => void;
}

// Legacy Step Component Props Interface (for backward compatibility)
export interface StepComponentProps {
  formData: any;
  onChange: (field: string, value: any) => void;
  onSave: (data: any) => Promise<string>;
  errors: Record<string, string>;
  licenseTypeId?: string;
  licenseCategoryId?: string;
  applicationId?: string;
  isLoading?: boolean;
}

// Step completion status
export interface StepStatus {
  stepId: string;
  completed: boolean;
  hasErrors: boolean;
  lastSaved?: Date;
  data?: any;
}
