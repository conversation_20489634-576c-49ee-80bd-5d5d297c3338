'use client';

export default function AccountSettingsContent() {
  return (
    <>
      <div className="px-6 py-5 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center">
              <i className="ri-settings-line text-red-600 dark:text-red-400"></i>
            </div>
          </div>
          <div className="ml-3">
            <h2 className="text-xl font-medium text-gray-900 dark:text-gray-100">Account Settings Help</h2>
            <p className="text-sm text-gray-500 dark:text-gray-400">Manage your profile, security, and preferences</p>
          </div>
        </div>
      </div>

      <div className="p-6">
        <div className="space-y-8">
          {/* Profile Management */}
          <div className="border-b border-gray-200 dark:border-gray-700 pb-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">Profile Management</h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Keep your profile information up to date to ensure accurate communication and compliance:
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">Personal Information</h4>
                <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                  <li>• Update name and contact details</li>
                  <li>• Change profile picture</li>
                  <li>• Set preferred language</li>
                  <li>• Update time zone settings</li>
                </ul>
              </div>
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">Organization Details</h4>
                <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                  <li>• Company information</li>
                  <li>• Business registration details</li>
                  <li>• Primary contact person</li>
                  <li>• Billing address</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Security Settings */}
          <div className="border-b border-gray-200 dark:border-gray-700 pb-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">Security Settings</h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Protect your account with strong security measures:
            </p>
            <div className="space-y-4">
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <i className="ri-shield-check-line text-red-600 dark:text-red-400"></i>
                  </div>
                  <div className="ml-3">
                    <h4 className="text-sm font-medium text-red-800 dark:text-red-200">Two-Factor Authentication (2FA)</h4>
                    <p className="text-sm text-red-700 dark:text-red-300 mt-1">
                      Enable 2FA for enhanced security. Use authenticator apps like Google Authenticator
                      or receive SMS codes for login verification.
                    </p>
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">Password Requirements</h4>
                  <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                    <li>• Minimum 8 characters</li>
                    <li>• Include uppercase and lowercase</li>
                    <li>• At least one number</li>
                    <li>• Special character required</li>
                  </ul>
                </div>
                <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">Session Management</h4>
                  <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                    <li>• View active sessions</li>
                    <li>• Logout from all devices</li>
                    <li>• Set session timeout</li>
                    <li>• Login history tracking</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Notification Preferences */}
          <div className="border-b border-gray-200 dark:border-gray-700 pb-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">Notification Preferences</h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Configure how and when you receive notifications:
            </p>
            <div className="space-y-4">
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <h4 className="font-medium text-blue-900 dark:text-blue-200 mb-3">Notification Types</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h5 className="text-sm font-medium text-blue-800 dark:text-blue-300 mb-2">License Notifications</h5>
                    <ul className="text-sm text-blue-700 dark:text-blue-400 space-y-1">
                      <li>• Renewal reminders</li>
                      <li>• Application status updates</li>
                      <li>• Compliance alerts</li>
                    </ul>
                  </div>
                  <div>
                    <h5 className="text-sm font-medium text-blue-800 dark:text-blue-300 mb-2">Financial Notifications</h5>
                    <ul className="text-sm text-blue-700 dark:text-blue-400 space-y-1">
                      <li>• Payment confirmations</li>
                      <li>• Invoice generation</li>
                      <li>• Payment due reminders</li>
                    </ul>
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
                  <i className="ri-mail-line text-2xl text-gray-600 dark:text-gray-400 mb-2"></i>
                  <h4 className="font-medium text-gray-900 dark:text-gray-100">Email</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Detailed notifications</p>
                </div>
                <div className="text-center p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
                  <i className="ri-smartphone-line text-2xl text-gray-600 dark:text-gray-400 mb-2"></i>
                  <h4 className="font-medium text-gray-900 dark:text-gray-100">SMS</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Urgent alerts only</p>
                </div>
                <div className="text-center p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
                  <i className="ri-notification-line text-2xl text-gray-600 dark:text-gray-400 mb-2"></i>
                  <h4 className="font-medium text-gray-900 dark:text-gray-100">In-App</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Real-time updates</p>
                </div>
              </div>
            </div>
          </div>

          {/* Data Management */}
          <div className="border-b border-gray-200 dark:border-gray-700 pb-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">Data Management</h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Control your data and privacy settings:
            </p>
            <div className="space-y-4">
              <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">Data Export</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  Download your data including licenses, applications, and transaction history.
                </p>
                <button className="text-sm bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 px-3 py-1 rounded">
                  Request Data Export
                </button>
              </div>
              <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">Account Deletion</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  Permanently delete your account and all associated data. This action cannot be undone.
                </p>
                <button className="text-sm bg-red-100 dark:bg-red-900/20 hover:bg-red-200 dark:hover:bg-red-900/30 text-red-700 dark:text-red-400 px-3 py-1 rounded">
                  Delete Account
                </button>
              </div>
            </div>
          </div>

          {/* Frequently Asked Questions */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Account Settings FAQ</h3>
            <div className="space-y-4">
              <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">How do I change my email address?</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Go to Profile Settings, click "Edit" next to your email address, enter the new email,
                  and verify it through the confirmation email sent to your new address.
                </p>
              </div>
              <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">I forgot my password. How do I reset it?</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Click "Forgot Password" on the login page, enter your email address, and follow
                  the instructions in the password reset email. Make sure to check your spam folder.
                </p>
              </div>
              <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">Can I have multiple users on one account?</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Yes, you can invite team members and assign different roles with specific permissions.
                  Go to User Management to add new users and configure their access levels.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
