'use client';

export default function ReportsAnalyticsContent() {
  return (
    <>
      <div className="px-6 py-5 border-b border-gray-200">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
              <i className="ri-file-chart-line text-red-600"></i>
            </div>
          </div>
          <div className="ml-3">
            <h2 className="text-xl font-medium text-gray-900">Reports & Analytics Help</h2>
            <p className="text-sm text-gray-500">Generate and view comprehensive reports</p>
          </div>
        </div>
      </div>
      
      <div className="p-6">
        <div className="space-y-8">
          <div className="border-b border-gray-200 pb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Available Reports</h3>
            <p className="text-gray-600 mb-4">
              Generate various reports to track your licenses, payments, and compliance status.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="border border-gray-200 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">License Reports</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Active licenses summary</li>
                  <li>• Renewal schedule</li>
                  <li>• Application status tracking</li>
                  <li>• Compliance history</li>
                </ul>
              </div>
              <div className="border border-gray-200 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">Financial Reports</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Payment history</li>
                  <li>• Outstanding invoices</li>
                  <li>• Fee breakdown analysis</li>
                  <li>• Annual spending summary</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="border-b border-gray-200 pb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Generating Reports</h3>
            <div className="space-y-4">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <span className="text-sm font-medium text-green-600">1</span>
                  </div>
                </div>
                <div className="ml-4">
                  <h4 className="font-medium text-gray-900">Select Report Type</h4>
                  <p className="text-sm text-gray-600">
                    Choose from license, financial, spectrum, or compliance reports.
                  </p>
                </div>
              </div>
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <span className="text-sm font-medium text-green-600">2</span>
                  </div>
                </div>
                <div className="ml-4">
                  <h4 className="font-medium text-gray-900">Set Parameters</h4>
                  <p className="text-sm text-gray-600">
                    Define date ranges, filters, and specific criteria for your report.
                  </p>
                </div>
              </div>
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <span className="text-sm font-medium text-green-600">3</span>
                  </div>
                </div>
                <div className="ml-4">
                  <h4 className="font-medium text-gray-900">Generate and Download</h4>
                  <p className="text-sm text-gray-600">
                    Generate the report and download in PDF, Excel, or CSV format.
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-3">Analytics Dashboard</h3>
            <p className="text-gray-600 mb-4">
              Use the analytics dashboard to visualize trends and insights from your data.
            </p>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-2">Key Metrics</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• License utilization rates</li>
                <li>• Payment trends and patterns</li>
                <li>• Compliance score tracking</li>
                <li>• Renewal forecasting</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
