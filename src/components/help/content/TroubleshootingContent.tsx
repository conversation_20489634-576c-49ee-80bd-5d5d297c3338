'use client';

export default function TroubleshootingContent() {
  return (
    <>
      <div className="px-6 py-5 border-b border-gray-200">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
              <i className="ri-tools-line text-red-600"></i>
            </div>
          </div>
          <div className="ml-3">
            <h2 className="text-xl font-medium text-gray-900">Troubleshooting Guide</h2>
            <p className="text-sm text-gray-500">Common issues and solutions</p>
          </div>
        </div>
      </div>
      
      <div className="p-6">
        <div className="space-y-8">
          {/* Login Issues */}
          <div className="border-b border-gray-200 pb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Login Issues</h3>
            <div className="space-y-4">
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <h4 className="font-medium text-red-900 mb-2">Can't log in to your account?</h4>
                <div className="space-y-2 text-sm text-red-800">
                  <p>• Check your email and password for typos</p>
                  <p>• Ensure Caps Lock is off</p>
                  <p>• Clear your browser cache and cookies</p>
                  <p>• Try using an incognito/private browser window</p>
                  <p>• Reset your password if needed</p>
                </div>
              </div>
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <h4 className="font-medium text-yellow-900 mb-2">Two-Factor Authentication Issues</h4>
                <div className="space-y-2 text-sm text-yellow-800">
                  <p>• Ensure your device time is synchronized</p>
                  <p>• Check if you have backup codes</p>
                  <p>• Contact support to disable 2FA temporarily</p>
                </div>
              </div>
            </div>
          </div>

          {/* File Upload Problems */}
          <div className="border-b border-gray-200 pb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">File Upload Problems</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="border border-gray-200 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">Supported File Types</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• PDF documents</li>
                  <li>• Images (JPG, PNG, GIF)</li>
                  <li>• Microsoft Office files</li>
                  <li>• Text documents</li>
                </ul>
              </div>
              <div className="border border-gray-200 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">File Size Limits</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Maximum 10MB per file</li>
                  <li>• Compress large files</li>
                  <li>• Use PDF for documents</li>
                  <li>• Optimize images</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Payment Issues */}
          <div className="border-b border-gray-200 pb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Payment Issues</h3>
            <div className="space-y-4">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="font-medium text-blue-900 mb-2">Payment Failed</h4>
                <div className="space-y-2 text-sm text-blue-800">
                  <p>• Check your card details and expiry date</p>
                  <p>• Ensure sufficient funds are available</p>
                  <p>• Contact your bank about international transactions</p>
                  <p>• Try a different payment method</p>
                </div>
              </div>
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <h4 className="font-medium text-green-900 mb-2">Payment Confirmation</h4>
                <p className="text-sm text-green-800">
                  Payment confirmations are sent via email within 5 minutes. 
                  Check your spam folder if you don't receive it.
                </p>
              </div>
            </div>
          </div>

          {/* Browser Compatibility */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-3">Browser Compatibility</h3>
            <p className="text-gray-600 mb-4">
              For the best experience, use one of these supported browsers:
            </p>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-4 border border-gray-200 rounded-lg">
                <div className="text-2xl mb-2">🌐</div>
                <h4 className="font-medium text-gray-900">Chrome</h4>
                <p className="text-sm text-gray-600">Version 90+</p>
              </div>
              <div className="text-center p-4 border border-gray-200 rounded-lg">
                <div className="text-2xl mb-2">🦊</div>
                <h4 className="font-medium text-gray-900">Firefox</h4>
                <p className="text-sm text-gray-600">Version 88+</p>
              </div>
              <div className="text-center p-4 border border-gray-200 rounded-lg">
                <div className="text-2xl mb-2">🧭</div>
                <h4 className="font-medium text-gray-900">Safari</h4>
                <p className="text-sm text-gray-600">Version 14+</p>
              </div>
              <div className="text-center p-4 border border-gray-200 rounded-lg">
                <div className="text-2xl mb-2">📘</div>
                <h4 className="font-medium text-gray-900">Edge</h4>
                <p className="text-sm text-gray-600">Version 90+</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
