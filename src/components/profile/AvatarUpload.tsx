'use client';

import { useState, useRef } from 'react';
import { User } from '../../services/userService';
import { userService } from '../../services/userService';

interface AvatarUploadProps {
  user: User;
  onUpdate: (user: User) => void;
}

export default function AvatarUpload({ user, onUpdate }: AvatarUploadProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      setError('Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed.');
      return;
    }

    // Validate file size (5MB max)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      setError('File size too large. Maximum size is 10MB.');
      return;
    }

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setPreviewUrl(e.target?.result as string);
    };
    reader.readAsDataURL(file);

    setError(null);
  };

  const handleUpload = async () => {
    const file = fileInputRef.current?.files?.[0];
    if (!file) {
      setError('Please select a file first.');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      console.log('AvatarUpload: Uploading file', file.name);
      const updatedUser = await userService.uploadAvatar(file);
      console.log('AvatarUpload: Upload successful, updated user:', updatedUser);

      onUpdate(updatedUser);
      setSuccess('Profile picture updated successfully!');
      setPreviewUrl(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (err: any) {
      console.error('AvatarUpload: Upload failed', err);
      setError(err.response?.data?.message || 'Failed to upload profile picture');
    } finally {
      setLoading(false);
    }
  };

  const handleRemove = async () => {
    if (!user.profile_image) return;

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const updatedUser = await userService.removeAvatar();
      onUpdate(updatedUser);
      setSuccess('Profile picture removed successfully!');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to remove profile picture');
    } finally {
      setLoading(false);
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  return (
    <div>
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="h-10 w-10 bg-red-100 rounded-lg flex items-center justify-center">
              <i className="ri-image-line text-red-600 text-xl"></i>
            </div>
          </div>
          <div className="ml-4">
            <h3 className="text-xl font-semibold text-gray-900">Profile Picture</h3>
            <p className="text-sm text-gray-500">
              Upload a profile picture to personalize your account.
            </p>
          </div>
        </div>
      </div>

      {/* Alert Messages */}
      {error && (
        <div className="mb-6 rounded-md bg-red-50 p-4 border-l-4 border-red-400">
          <div className="flex">
            <div className="flex-shrink-0">
              <i className="ri-error-warning-line text-red-400 text-lg"></i>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {success && (
        <div className="mb-6 rounded-md bg-green-50 p-4 border-l-4 border-green-400">
          <div className="flex">
            <div className="flex-shrink-0">
              <i className="ri-check-line text-green-400 text-lg"></i>
            </div>
            <div className="ml-3">
              <p className="text-sm text-green-800">{success}</p>
            </div>
          </div>
        </div>
      )}

      {/* Avatar Upload Section */}
      <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
        <div className="flex flex-col lg:flex-row lg:items-start lg:space-x-8 space-y-6 lg:space-y-0">
          {/* Current Avatar Display */}
          <div className="flex-shrink-0">
            <div className="text-center">
              <div className="relative inline-block">
                <div className="h-40 w-40 rounded-full overflow-hidden bg-gray-100 dark:bg-gray-700 border-4 border-white dark:border-gray-600 shadow-lg">
                  {previewUrl ? (
                    <img
                      src={previewUrl}
                      alt="Preview"
                      className="h-full w-full object-cover"
                    />
                  ) : user.profile_image ? (
                    <img
                      src={user.profile_image}
                      alt={`${user.first_name} ${user.last_name}`}
                      className="h-full w-full object-cover"
                    />
                  ) : (
                    <div className="h-full w-full flex items-center justify-center bg-gradient-to-br from-red-500 to-red-600">
                      <span className="text-4xl font-bold text-white">
                        {user.first_name?.charAt(0)}{user.last_name?.charAt(0)}
                      </span>
                    </div>
                  )}
                </div>
                {previewUrl && (
                  <div className="absolute -top-2 -right-2">
                    <div className="bg-blue-500 dark:bg-blue-600 text-white rounded-full p-1">
                      <i className="ri-eye-line text-sm"></i>
                    </div>
                  </div>
                )}
              </div>
              <p className="mt-3 text-sm text-gray-500 dark:text-gray-400">
                {previewUrl ? 'Preview' : 'Current Picture'}
              </p>
            </div>
          </div>

          {/* Upload Controls */}
          <div className="flex-1">
            <input
              ref={fileInputRef}
              type="file"
              accept="image/jpeg,image/png,image/gif,image/webp"
              onChange={handleFileSelect}
              className="hidden"
            />

            {/* Upload Area */}
            <div className="space-y-6">
              <div
                onClick={triggerFileInput}
                className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center hover:border-red-400 dark:hover:border-red-500 transition-colors duration-200 cursor-pointer bg-white dark:bg-gray-800"
              >
                <div className="space-y-2">
                  <div className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500">
                    <i className="ri-upload-cloud-line text-4xl"></i>
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-300">
                    <span className="font-medium text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300">
                      Click to upload
                    </span>
                    {' '}or drag and drop
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    PNG, JPG, GIF, WebP up to 5MB
                  </p>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="space-y-3">
                <button
                  type="button"
                  onClick={triggerFileInput}
                  disabled={loading}
                  className="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-lg text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 transition-colors duration-200"
                >
                  <i className="ri-folder-open-line mr-2"></i>
                  Browse Files
                </button>

                {previewUrl && (
                  <div className="flex space-x-3">
                    <button
                      type="button"
                      onClick={handleUpload}
                      disabled={loading}
                      className="flex-1 inline-flex justify-center items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-lg text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 transition-colors duration-200"
                    >
                      {loading ? (
                        <>
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Uploading...
                        </>
                      ) : (
                        <>
                          <i className="ri-upload-line mr-2"></i>
                          Upload
                        </>
                      )}
                    </button>
                    <button
                      type="button"
                      onClick={() => {
                        setPreviewUrl(null);
                        if (fileInputRef.current) {
                          fileInputRef.current.value = '';
                        }
                      }}
                      className="flex-1 inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-lg text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
                    >
                      <i className="ri-close-line mr-2"></i>
                      Cancel
                    </button>
                  </div>
                )}

                {user.profile_image && !previewUrl && (
                  <button
                    type="button"
                    onClick={handleRemove}
                    disabled={loading}
                    className="w-full inline-flex justify-center items-center px-4 py-2 border border-red-300 dark:border-red-600 shadow-sm text-sm font-medium rounded-lg text-red-700 dark:text-red-400 bg-white dark:bg-gray-800 hover:bg-red-50 dark:hover:bg-red-900/20 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 transition-colors duration-200"
                  >
                    <i className="ri-delete-bin-line mr-2"></i>
                    {loading ? 'Removing...' : 'Remove Picture'}
                  </button>
                )}
              </div>

              {/* File Requirements */}
              <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
                <h4 className="text-sm font-medium text-blue-800 dark:text-blue-300 mb-2 flex items-center">
                  <i className="ri-information-line mr-2"></i>
                  File Requirements
                </h4>
                <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                  <li className="flex items-center">
                    <i className="ri-check-line mr-2 text-blue-500 dark:text-blue-400"></i>
                    Supported formats: JPEG, PNG, GIF, WebP
                  </li>
                  <li className="flex items-center">
                    <i className="ri-check-line mr-2 text-blue-500 dark:text-blue-400"></i>
                    Maximum file size: 10MB
                  </li>
                  <li className="flex items-center">
                    <i className="ri-check-line mr-2 text-blue-500 dark:text-blue-400"></i>
                    Recommended size: 400x400 pixels
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
