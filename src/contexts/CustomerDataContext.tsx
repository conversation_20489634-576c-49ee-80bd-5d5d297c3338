'use client';

import React, { createContext, useContext, useCallback, ReactNode } from 'react';
import { useCustomerCache } from '@/hooks/useCustomerCache';
import { customerApi, License, Application, Payment } from '@/lib/customer-api';

// Define proper types for API options
interface ApiOptions {
  limit?: number;
  offset?: number;
  status?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  [key: string]: unknown;
}

interface CustomerData {
  licenses: License[];
  applications: Application[];
  payments: Payment[];
  stats: {
    activeLicenses: number;
    pendingApplications: number;
    expiringSoon: number;
    paymentsDue: number;
    totalPaymentAmount: number;
  };
}

interface CustomerDataContextType {
  getDashboardData: () => Promise<CustomerData>;
  getLicenses: (options?: ApiOptions) => Promise<License[]>;
  getApplications: (options?: ApiOptions) => Promise<Application[]>;
  getPayments: (options?: ApiOptions) => Promise<Payment[]>;
  invalidateCache: (key?: string) => void;
  isLoading: Record<string, boolean>;
}

const CustomerDataContext = createContext<CustomerDataContextType | undefined>(undefined);

interface CustomerDataProviderProps {
  children: ReactNode;
}

export const CustomerDataProvider: React.FC<CustomerDataProviderProps> = ({ children }) => {
  const dashboardCache = useCustomerCache<CustomerData>();
  const licensesCache = useCustomerCache<License[]>();
  const applicationsCache = useCustomerCache<Application[]>();
  const paymentsCache = useCustomerCache<Payment[]>();

  const getDashboardData = useCallback(async (): Promise<CustomerData> => {
    return dashboardCache.fetchWithCache('dashboard', async () => {
      const [licensesRes, applicationsRes, paymentsRes, statsRes] = await Promise.allSettled([
        customerApi.getLicenses({ limit: 10 }),
        customerApi.getApplications({ limit: 10 }),
        customerApi.getPayments({ limit: 10 }),
        customerApi.getDashboardStats() // Changed from getStats to getDashboardStats
      ]);

      const licenses = licensesRes.status === 'fulfilled' ? (licensesRes.value.data || licensesRes.value || []) : [];
      const applications = applicationsRes.status === 'fulfilled' ? (applicationsRes.value.data || applicationsRes.value || []) : [];
      const payments = paymentsRes.status === 'fulfilled' ? (paymentsRes.value.data || paymentsRes.value || []) : [];
      
      let stats;
      if (statsRes.status === 'fulfilled' && statsRes.value) {
        stats = statsRes.value.data || statsRes.value;
      } else {
        // Calculate stats from fetched data if API call fails
        const activeLicenses = licenses.filter((l: License) => l.status === 'active').length;
        const pendingApplications = applications.filter((a: Application) =>
          ['submitted', 'under_review'].includes(a.status)
        ).length;

        // Check for licenses expiring in next 30 days
        const thirtyDaysFromNow = new Date();
        thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
        const expiringSoon = licenses.filter((l: License) => {
          const expirationDate = new Date(l.expirationDate);
          return l.status === 'active' && expirationDate <= thirtyDaysFromNow;
        }).length;

        const pendingPayments = payments.filter((p: Payment) =>
          ['pending', 'overdue'].includes(p.status)
        );
        const totalPaymentAmount = pendingPayments.reduce((sum: number, p: Payment) => sum + p.amount, 0);

        stats = {
          activeLicenses,
          pendingApplications,
          expiringSoon,
          paymentsDue: pendingPayments.length,
          totalPaymentAmount
        };
      }

      return { licenses, applications, payments, stats };
    }, { ttl: 2 * 60 * 1000 }); // Cache for 2 minutes
  }, [dashboardCache]);

  const getLicenses = useCallback(async (options: ApiOptions = {}): Promise<License[]> => {
    const cacheKey = `licenses-${JSON.stringify(options)}`;
    return licensesCache.fetchWithCache(cacheKey, async () => {
      const result = await customerApi.getLicenses(options);
      return result.data || result || [];
    }, {
      ttl: 5 * 60 * 1000 // Cache for 5 minutes
    });
  }, [licensesCache]);

  const getApplications = useCallback(async (options: ApiOptions = {}): Promise<Application[]> => {
    const cacheKey = `applications-${JSON.stringify(options)}`;
    return applicationsCache.fetchWithCache(cacheKey, async () => {
      const result = await customerApi.getApplications(options);
      return result.data || result || [];
    }, {
      ttl: 3 * 60 * 1000 // Cache for 3 minutes
    });
  }, [applicationsCache]);

  const getPayments = useCallback(async (options: ApiOptions = {}): Promise<Payment[]> => {
    const cacheKey = `payments-${JSON.stringify(options)}`;
    return paymentsCache.fetchWithCache(cacheKey, async () => {
      const result = await customerApi.getPayments(options);
      return result.data || result || [];
    }, {
      ttl: 5 * 60 * 1000 // Cache for 5 minutes
    });
  }, [paymentsCache]);

  const invalidateCache = useCallback((key?: string) => {
    if (key) {
      // Invalidate specific cache based on key prefix
      if (key.startsWith('dashboard')) {
        dashboardCache.invalidate(key);
      } else if (key.startsWith('licenses')) {
        licensesCache.invalidate(key);
      } else if (key.startsWith('applications')) {
        applicationsCache.invalidate(key);
      } else if (key.startsWith('payments')) {
        paymentsCache.invalidate(key);
      }
    } else {
      // Invalidate all caches
      dashboardCache.invalidate();
      licensesCache.invalidate();
      applicationsCache.invalidate();
      paymentsCache.invalidate();
    }
  }, [dashboardCache, licensesCache, applicationsCache, paymentsCache]);

  const value: CustomerDataContextType = {
    getDashboardData,
    getLicenses,
    getApplications,
    getPayments,
    invalidateCache,
    isLoading: {
      ...dashboardCache.isLoading,
      ...licensesCache.isLoading,
      ...applicationsCache.isLoading,
      ...paymentsCache.isLoading
    }
  };

  return (
    <CustomerDataContext.Provider value={value}>
      {children}
    </CustomerDataContext.Provider>
  );
};

export const useCustomerData = (): CustomerDataContextType => {
  const context = useContext(CustomerDataContext);
  if (context === undefined) {
    throw new Error('useCustomerData must be used within a CustomerDataProvider');
  }
  return context;
};

export default CustomerDataProvider;