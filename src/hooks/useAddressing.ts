'use client';

import { customerApi } from '@/lib/customer-api';
import { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { useDebouncedCallback } from 'use-debounce';

export interface CreateAddressData {
  address_type: string;
  entity_type?: string;
  entity_id?: string;
  address_line_1: string;
  address_line_2?: string;
  address_line_3?: string;
  postal_code: string;
  country: string;
  city: string;
}

export interface EditAddressData {
  address_id: string;
  address_type?: string;
  entity_type?: string;
  entity_id?: string;
  address_line_1?: string;
  address_line_2?: string;
  address_line_3?: string;
  postal_code?: string;
  country?: string;
  city?: string;
}

export interface SearchPostcodes {
  region?: string;
  district?: string;
  location?: string;
  postal_code?: string;
}

export interface PostalCodeLookupResult {
  postal_code_id: string;
  region: string;
  district: string;
  location: string;
  postal_code: string;
  created_at: string;
  updated_at: string;
  deleted_at?: string | null;
}

export const initialAddressData: CreateAddressData = {
  address_type: 'business',
  entity_type: 'applicant',
  entity_id: '',
  address_line_1: '',
  address_line_2: '',
  address_line_3: '',
  postal_code: '',
  country: '',
  city: ''
};

export interface Address {
  address_id: string;
  address_type?: string;
  entity_type?: string;
  entity_id?: string;
  address_line_1: string;
  address_line_2?: string;
  address_line_3?: string;
  postal_code: string;
  country: string;
  city: string;
  created_at: string;
  updated_at: string;
}


// Address service using customer API
export const addressService = {
  async createAddress(data: CreateAddressData): Promise<any> {
    const response = await customerApi.createAddress(data);
    return response;
  },

  async getAddress(id: string): Promise<any> {
    const response = await customerApi.getAddress(id);
    return response;
  },

  async editAddress(data: EditAddressData): Promise<any> {
    const response = await customerApi.editAddress(data);
    return response;
  },

  async getAddressesByEntity(entityType: string, entityId: string): Promise<any> {
    const response = await customerApi.getAddressesByEntity(entityType, entityId);
    return response;
  },

  async searchPostcodes(searchParams: SearchPostcodes): Promise<any> {
    const response = await customerApi.searchPostcodes(searchParams);
    return response;
  },

}


// Actual hook
export const useAddresses = (initialSearchParams?: SearchPostcodes) => {
  const [addresses, setAddresses] = useState<Address[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchParams, setSearchParams] = useState<SearchPostcodes>(initialSearchParams || {});

  const [postcodeSuggestions, setPostcodeSuggestions] = useState<PostalCodeLookupResult[]>([]);
  const [searching, setSearching] = useState(false);

  // Fetch address list when searchParams change
  useEffect(() => {
    const fetchAddresses = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await addressService.searchPostcodes(searchParams);
        setAddresses(response.data || []);
      } catch (err: any) {
        console.error('Address fetch error:', err);
        setError(err.message || 'Failed to fetch addresses');
        toast.error('Failed to fetch addresses');
      } finally {
        setLoading(false);
      }
    };

    if (Object.keys(searchParams).length > 0) {
      fetchAddresses();
    }
  }, [searchParams]);

  // Postcode suggestions (live lookup, debounced)
  const debouncedSearchPostcodes = useDebouncedCallback(async (params: SearchPostcodes) => {
    setSearching(true);
    try {
      const response = await customerApi.searchPostcodes(params);
      setPostcodeSuggestions(response.data || []);
    } catch (err) {
      console.error('Postcode search failed:', err);
    } finally {
      setSearching(false);
    }
  }, 500); // debounce for 500ms

  // Manual search trigger to update addresses based on params
  const searchAddresses = (params: SearchPostcodes) => {
    setSearchParams(params);
  };

  // Create new address
  const createAddress = async (data: CreateAddressData) => {
    setLoading(true);
    setError(null);
    try {
      const newAddress = await addressService.createAddress(data);
      setAddresses(prev => [newAddress, ...prev]);
      toast.success('Address created successfully');
      return newAddress;
    } catch (err: any) {
      console.error('Address create error:', err);
      setError(err.message || 'Failed to create address');
      toast.error('Failed to create address');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Edit existing address
  const editAddress = async (data: EditAddressData) => {
    setLoading(true);
    setError(null);
    try {
      const updatedAddress = await addressService.editAddress(data);
      setAddresses(prev =>
        prev.map(addr => (addr.address_id === data.address_id ? updatedAddress : addr))
      );
      toast.success('Address updated successfully');
      return updatedAddress;
    } catch (err: any) {
      console.error('Address edit error:', err);
      setError(err.message || 'Failed to update address');
      toast.error('Failed to update address');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return {
    // State
    addresses,
    postcodeSuggestions,
    searching,
    loading,
    error,
    searchParams,

    // Setters / Triggers
    setSearchParams,
    debouncedSearchPostcodes,
    searchAddresses,

    // CRUD
    createAddress,
    editAddress,

    // Raw service (if needed)
    addressService,
  };
};