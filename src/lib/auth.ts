import axios from 'axios';

// Authentication utility functions

export const getAuthToken = (): string | null => {
  if (typeof window === 'undefined') {
    return null; // Server-side rendering
  }
  
  return localStorage.getItem('auth_token');
};

export const setAuthToken = (token: string): void => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('auth_token', token);
  }
};

export const removeAuthToken = (): void => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('auth_token');
  }
};

export const isAuthenticated = (): boolean => {
  return !!getAuthToken();
};



// Create axios instance with auth
export const createAuthenticatedAxios = (API_BASE_URL: string = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001') => {
  const token = getAuthToken();
  const instance = axios.create({
    baseURL: API_BASE_URL,
    timeout: 10000, // 10 second timeout
    headers: {
      'Authorization': token ? `Bearer ${token}` : '',
      'Content-Type': 'application/json',
    },
  });

  // Add request interceptor for debugging
  instance.interceptors.request.use(
    (config) => {
      console.log(`Making ${config.method?.toUpperCase()} request to: ${config.baseURL}${config.url}`);
      return config;
    },
    (error) => {
      console.error('Request interceptor error:', error);
      return Promise.reject(error);
    }
  );

  // Add response interceptor for better error handling
  instance.interceptors.response.use(
    (response) => {
      return response;
    },
    (error) => {
      console.error('API Error Details:', {
        url: error.config?.url,
        method: error.config?.method,
        status: error.response?.status,
        statusText: error.response?.statusText,
        message: error.message,
        code: error.code,
        data: error.response?.data
      });

      // Handle authentication errors - auto logout on 401
      if (error.response?.status === 401) {
        console.warn('Authentication failed - token invalid or expired. Logging out...');
        removeAuthToken();
        // Redirect to login page
        if (typeof window !== 'undefined') {
          window.location.href = '/auth/login';
        }
      }

      // Handle network errors
      if (error.code === 'ERR_NETWORK' || error.message === 'Network Error') {
        console.error('Network error - backend may not be accessible');
      }

      return Promise.reject(error);
    }
  );

  return instance;
};