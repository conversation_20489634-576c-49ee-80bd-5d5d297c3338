import Cookies from 'js-cookie';
import { getAuthToken, removeAuthToken } from './auth';

/**
 * Utility functions for authentication management
 */

/**
 * Check if a JWT token is expired
 * @param token - JWT token to check
 * @returns true if token is expired, false otherwise
 */
export const isTokenExpired = (token: string): boolean => {
  if (!token) return true;

  try {
    // Decode JWT payload (without verification - just for expiry check)
    const payload = JSON.parse(atob(token.split('.')[1]));
    const currentTime = Math.floor(Date.now() / 1000);
    
    // Check if token has expired
    return payload.exp < currentTime;
  } catch (error) {
    console.error('Error decoding token:', error);
    return true; // Treat invalid tokens as expired
  }
};

/**
 * Validate current authentication state
 * @returns true if user is properly authenticated, false otherwise
 */
export const validateAuthState = (): boolean => {
  const token = getAuthToken();
  const userCookie = Cookies.get('auth_user');

  // Check if token exists and is not expired
  if (!token || isTokenExpired(token)) {
    console.warn('Token is missing or expired');
    return false;
  }

  // Check if user data exists
  if (!userCookie) {
    console.warn('User data is missing');
    return false;
  }

  try {
    JSON.parse(userCookie); // Validate user data format
    return true;
  } catch (error) {
    console.error('Invalid user data format:', error);
    return false;
  }
};

/**
 * Clear all authentication data and redirect to login
 */
export const forceLogout = (): void => {
  console.warn('Forcing logout due to invalid authentication state');
  
  // Clear all auth data
  removeAuthToken();
  Cookies.remove('auth_token');
  Cookies.remove('auth_user');
  
  // Clear localStorage and sessionStorage
  if (typeof window !== 'undefined') {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user_preferences');
    sessionStorage.clear();
    
    // Redirect to appropriate login based on current portal
    const isCustomerPortal = window.location.pathname.startsWith('/customer') || 
                            window.location.hostname.includes('customer');
    
    window.location.href = isCustomerPortal ? '/customer/auth/login' : '/auth/login';
  }
};

/**
 * Periodically check token validity and auto-logout if expired
 * @param intervalMs - Check interval in milliseconds (default: 60 seconds)
 */
export const startTokenValidationTimer = (intervalMs: number = 60000): NodeJS.Timeout => {
  return setInterval(() => {
    if (!validateAuthState()) {
      forceLogout();
    }
  }, intervalMs);
};

/**
 * Periodically check token validity and auto-logout if expired
 * @param intervalMs - Check interval in milliseconds (default: 60 seconds)
 */
export const processApiResponse = (response: any): any =>  {

    // Check if it's a standard datatable success response format
  if (response.data.meta !== undefined && response.data.data) {
    return response.data;
  }

  // Check if it's a standard success response format
  if (response?.data?.data) {
    return response.data.data;
  }

  // Check if it's direct data format
  else if (response.data) {
    return response.data;
  }

  return response.data;
};

/**
 * Check token expiry and warn user before it expires
 * @param warningMinutes - Minutes before expiry to show warning (default: 5)
 */
export const checkTokenExpiry = (warningMinutes: number = 5): void => {
  const token = getAuthToken();
  if (!token) return;

  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    const expiryTime = payload.exp * 1000; // Convert to milliseconds
    const currentTime = Date.now();
    const timeUntilExpiry = expiryTime - currentTime;
    const warningTime = warningMinutes * 60 * 1000; // Convert to milliseconds

    if (timeUntilExpiry <= warningTime && timeUntilExpiry > 0) {
      console.warn(`Token will expire in ${Math.floor(timeUntilExpiry / 60000)} minutes`);
      // You can add a toast notification here if needed
    }
  } catch (error) {
    console.error('Error checking token expiry:', error);
  }
};
