'use client';

import * as echarts from 'echarts';
import { useEffect, useRef } from 'react';

export const useECharts = (options: any, dependencies: any[] = []) => {
  const chartRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (chartRef.current) {
      const chart = echarts.init(chartRef.current);
      chart.setOption(options);

      const resizeHandler = () => chart.resize();
      window.addEventListener('resize', resizeHandler);

      return () => {
        chart.dispose();
        window.removeEventListener('resize', resizeHandler);
      };
    }
  }, dependencies);

  return chartRef;
};