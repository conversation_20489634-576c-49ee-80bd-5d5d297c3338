import { apiClient } from '../lib/apiClient';

// Types
export interface IdentificationType {
  identification_type_id: string;
  name: string;
  created_at: string;
  updated_at: string;
  created_by?: string;
  updated_by?: string;
  creator?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  updater?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  user_identifications?: any[];
}

export interface CreateIdentificationTypeDto {
  name: string;
}

export interface UpdateIdentificationTypeDto {
  name?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  meta: {
    itemsPerPage: number;
    totalItems?: number;
    currentPage?: number;
    totalPages?: number;
    sortBy: [string, string][];
    searchBy: string[];
    search: string;
    select: string[];
    filter?: Record<string, string | string[]>;
  };
  links: {
    first?: string;
    previous?: string;
    current: string;
    next?: string;
    last?: string;
  };
}

export type IdentificationTypesResponse = PaginatedResponse<IdentificationType>;

export interface PaginateQuery {
  page?: number;
  limit?: number;
  sortBy?: string[];
  searchBy?: string[];
  search?: string;
  filter?: Record<string, string | string[]>;
}

export const identificationTypeService = {
  // Get all identification types with pagination
  async getIdentificationTypes(query: PaginateQuery = {}): Promise<IdentificationTypesResponse> {
    const params = new URLSearchParams();

    if (query.page) params.set('page', query.page.toString());
    if (query.limit) params.set('limit', query.limit.toString());
    if (query.search) params.set('search', query.search);
    if (query.sortBy) {
      query.sortBy.forEach(sort => params.append('sortBy', sort));
    }
    if (query.searchBy) {
      query.searchBy.forEach(search => params.append('searchBy', search));
    }
    if (query.filter) {
      Object.entries(query.filter).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          value.forEach(v => params.append(`filter.${key}`, v));
        } else {
          params.set(`filter.${key}`, value);
        }
      });
    }

    const response = await apiClient.get(`/identification-types?${params.toString()}`);
    return response.data;
  },

  // Get identification type by ID
  async getIdentificationType(id: string): Promise<IdentificationType> {
    const response = await apiClient.get(`/identification-types/${id}`);
    return response.data;
  },

  // Get simple list of identification types
  async getSimpleIdentificationTypes(): Promise<IdentificationType[]> {
    const response = await apiClient.get('/identification-types/simple');
    return response.data;
  },

  // Create new identification type
  async createIdentificationType(identificationTypeData: CreateIdentificationTypeDto): Promise<IdentificationType> {
    const response = await apiClient.post('/identification-types', identificationTypeData);
    return response.data;
  },

  // Update identification type
  async updateIdentificationType(id: string, identificationTypeData: UpdateIdentificationTypeDto): Promise<IdentificationType> {
    const response = await apiClient.put(`/identification-types/${id}`, identificationTypeData);
    return response.data;
  },

  // Delete identification type
  async deleteIdentificationType(id: string): Promise<{ message: string }> {
    const response = await apiClient.delete(`/identification-types/${id}`);
    return response.data;
  },

  // Get all identification types (simple list for dropdowns)
  async getAllIdentificationTypes(): Promise<IdentificationType[]> {
    const response = await this.getIdentificationTypes({ limit: 1000 });
    return response.data;
  },
};
