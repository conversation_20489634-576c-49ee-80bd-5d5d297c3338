import { apiClient } from '../lib/apiClient';
import { LicenseType } from './licenseTypeService';
import { cacheService, CACHE_KEYS, CACHE_TTL } from './cacheService';

// Utility functions for category codes
export const generateCategoryCode = (name: string): string => {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
    .substring(0, 50); // Limit length
};

export const addCodesToCategories = (categories: LicenseCategory[]): LicenseCategory[] => {
  return categories.map(category => ({
    ...category,
    code: generateCategoryCode(category.name),
    children: category.children ? addCodesToCategories(category.children) : undefined
  }));
};

export const findCategoryByCode = (categories: LicenseCategory[], code: string): LicenseCategory | null => {
  for (const category of categories) {
    if (category.code === code) {
      return category;
    }
    if (category.children) {
      const found = findCategoryByCode(category.children, code);
      if (found) return found;
    }
  }
  return null;
};

export const findCategoryById = (categories: LicenseCategory[], id: string): LicenseCategory | null => {
  for (const category of categories) {
    if (category.license_category_id === id) {
      return category;
    }
    if (category.children) {
      const found = findCategoryById(category.children, id);
      if (found) return found;
    }
  }
  return null;
};

// Types
export interface LicenseCategory {
  license_category_id: string;
  license_type_id: string;
  parent_id?: string;
  name: string;
  fee: string;
  description: string;
  authorizes: string;
  created_at: string;
  updated_at: string;
  created_by?: string;
  updated_by?: string;
  license_type?: LicenseType;
  parent?: LicenseCategory;
  children?: LicenseCategory[];
  creator?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  updater?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  // Generated code for URL-friendly routing
  code?: string;
}

export interface CreateLicenseCategoryDto {
  license_type_id: string;
  parent_id?: string;
  name: string;
  fee: string;
  description: string;
  authorizes: string;
}

export interface UpdateLicenseCategoryDto {
  license_type_id?: string;
  parent_id?: string;
  name?: string;
  fee?: string;
  description?: string;
  authorizes?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  meta: {
    itemsPerPage: number;
    totalItems?: number;
    currentPage?: number;
    totalPages?: number;
    sortBy: [string, string][];
    searchBy: string[];
    search: string;
    select: string[];
    filter?: Record<string, string | string[]>;
  };
  links: {
    first?: string;
    previous?: string;
    current: string;
    next?: string;
    last?: string;
  };
}

export type LicenseCategoriesResponse = PaginatedResponse<LicenseCategory>;

export interface PaginateQuery {
  page?: number;
  limit?: number;
  sortBy?: string[];
  searchBy?: string[];
  search?: string;
  filter?: Record<string, string | string[]>;
}

export const licenseCategoryService = {
  // Get all license categories with pagination
  async getLicenseCategories(query: PaginateQuery = {}): Promise<LicenseCategoriesResponse> {
    const params = new URLSearchParams();

    if (query.page) params.set('page', query.page.toString());
    if (query.limit) params.set('limit', query.limit.toString());
    if (query.search) params.set('search', query.search);
    if (query.sortBy) {
      query.sortBy.forEach(sort => params.append('sortBy', sort));
    }
    if (query.searchBy) {
      query.searchBy.forEach(search => params.append('searchBy', search));
    }
    if (query.filter) {
      Object.entries(query.filter).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          value.forEach(v => params.append(`filter.${key}`, v));
        } else {
          params.set(`filter.${key}`, value);
        }
      });
    }

    const response = await apiClient.get(`/license-categories?${params.toString()}`);
    return response.data;
  },

  // Get license category by ID
  async getLicenseCategory(id: string): Promise<LicenseCategory> {
    const response = await apiClient.get(`/license-categories/${id}`);
    return response.data;
  },

  // Get license categories by license type
  async getLicenseCategoriesByType(licenseTypeId: string): Promise<LicenseCategory[]> {
    const response = await apiClient.get(`/license-categories/by-license-type/${licenseTypeId}`);
    return response.data;
  },

  // Create new license category
  async createLicenseCategory(licenseCategoryData: CreateLicenseCategoryDto): Promise<LicenseCategory> {
    const response = await apiClient.post('/license-categories', licenseCategoryData);
    return response.data;
  },

  // Update license category
  async updateLicenseCategory(id: string, licenseCategoryData: UpdateLicenseCategoryDto): Promise<LicenseCategory> {
    const response = await apiClient.put(`/license-categories/${id}`, licenseCategoryData);
    return response.data;
  },

  // Delete license category
  async deleteLicenseCategory(id: string): Promise<{ message: string }> {
    const response = await apiClient.delete(`/license-categories/${id}`);
    return response.data;
  },

  // Get all license categories (simple list for dropdowns) with caching
  async getAllLicenseCategories(): Promise<LicenseCategory[]> {
    return cacheService.getOrSet(
      CACHE_KEYS.LICENSE_CATEGORIES,
      async () => {
        console.log('Fetching license categories from API...');
        // Reduce limit to avoid rate limiting
        const response = await this.getLicenseCategories({ limit: 100 });
        return addCodesToCategories(response.data);
      },
      CACHE_TTL.LONG // Cache for 15 minutes
    );
  },

  // Get hierarchical tree of categories for a license type with caching
  async getCategoryTree(licenseTypeId: string): Promise<LicenseCategory[]> {
    return cacheService.getOrSet(
      `category-tree-${licenseTypeId}`,
      async () => {
        console.log(`Fetching category tree for license type: ${licenseTypeId}`);
        const response = await apiClient.get(`/license-categories/license-type/${licenseTypeId}/tree`);
        return addCodesToCategories(response.data);
      },
      CACHE_TTL.MEDIUM // Cache for 5 minutes
    );
  },

  // Get root categories (no parent) for a license type with caching
  async getRootCategories(licenseTypeId: string): Promise<LicenseCategory[]> {
    return cacheService.getOrSet(
      `root-categories-${licenseTypeId}`,
      async () => {
        console.log(`Fetching root categories for license type: ${licenseTypeId}`);
        const response = await apiClient.get(`/license-categories/license-type/${licenseTypeId}/root`);
        return response.data;
      },
      CACHE_TTL.MEDIUM // Cache for 5 minutes
    );
  },

  // Get license categories for parent selection dropdown
  async getCategoriesForParentSelection(licenseTypeId: string, excludeId?: string): Promise<LicenseCategory[]> {
    try {
      const params = excludeId ? { excludeId } : {};
      console.log('🚀 Fetching parent categories for license type:', licenseTypeId, 'with params:', params);

      // Try the new endpoint first
      try {
        const response = await apiClient.get(`/license-categories/license-type/${licenseTypeId}/for-parent-selection`, { params });


        if (response.data && Array.isArray(response.data.data)) {
          console.log('✅ Valid array response with', response.data.data.length, 'items')
          return response.data.data;
        } else {
          console.warn('⚠️ API returned non-array data:', response.data);
          return [];
        }
      } catch (newEndpointError) {
        console.warn('🔄 New endpoint failed, trying fallback:', newEndpointError);

        // Fallback to existing endpoint
        const response = await apiClient.get(`/license-categories/by-license-type/${licenseTypeId}`);
        console.log('🔄 Fallback response:', response.data);

        if (response.data && Array.isArray(response.data)) {
          // Filter out the excluded category if specified
          let categories = response.data;
          if (excludeId) {
            categories = categories.filter(cat => cat.license_category_id !== excludeId);
          }
          console.log('✅ Fallback successful with', categories.length, 'items');
          return categories;
        } else {
          console.warn('⚠️ Fallback also returned non-array data:', response.data);
          return [];
        }
      }
    } catch (error) {

      return [];
    }
  },

  // Get potential parent categories for a license type
  async getPotentialParents(licenseTypeId: string, excludeId?: string): Promise<LicenseCategory[]> {
    const params = excludeId ? { excludeId } : {};
    const response = await apiClient.get(`/license-categories/license-type/${licenseTypeId}/potential-parents`, { params });
    return response.data;
  },
};
