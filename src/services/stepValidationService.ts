/**
 * Step Validation Service
 * Handles step completion validation and navigation rules for license applications
 */

import { getLicenseTypeStepConfig, getStepIndex } from '@/config/licenseTypeStepConfig';
import { applicationProgressService } from './applicationProgressService';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface NavigationValidation {
  canNavigateToStep: boolean;
  reason?: string;
  requiredSteps: string[];
}

class StepValidationService {
  /**
   * Validate if user can navigate to a specific step
   */
  async validateStepNavigation(
    applicationId: string,
    licenseTypeId: string,
    targetStepId: string
  ): Promise<NavigationValidation> {
    const licenseConfig = getLicenseTypeStepConfig(licenseTypeId);
    if (!licenseConfig) {
      return {
        canNavigateToStep: false,
        reason: 'Invalid license type',
        requiredSteps: []
      };
    }

    const targetStepIndex = getStepIndex(licenseTypeId, targetStepId);
    if (targetStepIndex === -1) {
      return {
        canNavigateToStep: false,
        reason: 'Invalid step',
        requiredSteps: []
      };
    }

    // Get completed steps
    const completedStepIds = await applicationProgressService.getCompletedStepIds(applicationId);
    
    // Check if all required previous steps are completed
    const requiredPreviousSteps = licenseConfig.steps
      .slice(0, targetStepIndex)
      .filter(step => step.required)
      .map(step => step.id);

    const missingRequiredSteps = requiredPreviousSteps.filter(
      stepId => !completedStepIds.includes(stepId)
    );

    if (missingRequiredSteps.length > 0) {
      return {
        canNavigateToStep: false,
        reason: 'Required previous steps must be completed first',
        requiredSteps: missingRequiredSteps
      };
    }

    return {
      canNavigateToStep: true,
      requiredSteps: []
    };
  }

  /**
   * Validate if user can proceed to next step
   */
  async validateNextStepNavigation(
    applicationId: string,
    licenseTypeId: string,
    currentStepId: string
  ): Promise<NavigationValidation> {
    const licenseConfig = getLicenseTypeStepConfig(licenseTypeId);
    if (!licenseConfig) {
      return {
        canNavigateToStep: false,
        reason: 'Invalid license type',
        requiredSteps: []
      };
    }

    const currentStepIndex = getStepIndex(licenseTypeId, currentStepId);
    if (currentStepIndex === -1) {
      return {
        canNavigateToStep: false,
        reason: 'Invalid current step',
        requiredSteps: []
      };
    }

    // Check if current step is completed (if required)
    const currentStep = licenseConfig.steps[currentStepIndex];
    if (currentStep.required) {
      const isCompleted = await applicationProgressService.isStepCompleted(applicationId, currentStepId);
      if (!isCompleted) {
        return {
          canNavigateToStep: false,
          reason: 'Current step must be completed before proceeding',
          requiredSteps: [currentStepId]
        };
      }
    }

    // Check if there is a next step
    if (currentStepIndex >= licenseConfig.steps.length - 1) {
      return {
        canNavigateToStep: false,
        reason: 'Already at the last step',
        requiredSteps: []
      };
    }

    return {
      canNavigateToStep: true,
      requiredSteps: []
    };
  }

  /**
   * Validate if user can go back to previous step
   */
  async validatePreviousStepNavigation(
    applicationId: string,
    licenseTypeId: string,
    currentStepId: string
  ): Promise<NavigationValidation> {
    const currentStepIndex = getStepIndex(licenseTypeId, currentStepId);
    
    if (currentStepIndex <= 0) {
      return {
        canNavigateToStep: false,
        reason: 'Already at the first step',
        requiredSteps: []
      };
    }

    return {
      canNavigateToStep: true,
      requiredSteps: []
    };
  }

  /**
   * Get the next available step that the user can navigate to
   */
  async getNextAvailableStep(
    applicationId: string,
    licenseTypeId: string
  ): Promise<string | null> {
    const licenseConfig = getLicenseTypeStepConfig(licenseTypeId);
    if (!licenseConfig) return null;

    const completedStepIds = await applicationProgressService.getCompletedStepIds(applicationId);
    
    // Find the first incomplete required step
    for (const step of licenseConfig.steps) {
      if (step.required && !completedStepIds.includes(step.id)) {
        return step.id;
      }
    }

    // If all required steps are completed, find the first incomplete optional step
    for (const step of licenseConfig.steps) {
      if (!step.required && !completedStepIds.includes(step.id)) {
        return step.id;
      }
    }

    // All steps completed
    return null;
  }

  /**
   * Validate application completion
   */
  async validateApplicationCompletion(
    applicationId: string,
    licenseTypeId: string
  ): Promise<ValidationResult> {
    const licenseConfig = getLicenseTypeStepConfig(licenseTypeId);
    if (!licenseConfig) {
      return {
        isValid: false,
        errors: ['Invalid license type'],
        warnings: []
      };
    }

    const completedStepIds = await applicationProgressService.getCompletedStepIds(applicationId);
    const requiredSteps = licenseConfig.steps.filter(step => step.required);
    const missingRequiredSteps = requiredSteps.filter(step => !completedStepIds.includes(step.id));

    const errors: string[] = [];
    const warnings: string[] = [];

    if (missingRequiredSteps.length > 0) {
      errors.push(`Missing required steps: ${missingRequiredSteps.map(s => s.name).join(', ')}`);
    }

    const optionalSteps = licenseConfig.steps.filter(step => !step.required);
    const missingOptionalSteps = optionalSteps.filter(step => !completedStepIds.includes(step.id));

    if (missingOptionalSteps.length > 0) {
      warnings.push(`Optional steps not completed: ${missingOptionalSteps.map(s => s.name).join(', ')}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Get step completion requirements
   */
  async getStepRequirements(
    licenseTypeId: string,
    stepId: string
  ): Promise<{
    isRequired: boolean;
    dependencies: string[];
    description: string;
    estimatedTime: string;
  } | null> {
    const licenseConfig = getLicenseTypeStepConfig(licenseTypeId);
    if (!licenseConfig) return null;

    const step = licenseConfig.steps.find(s => s.id === stepId);
    if (!step) return null;

    const stepIndex = getStepIndex(licenseTypeId, stepId);
    const dependencies = licenseConfig.steps
      .slice(0, stepIndex)
      .filter(s => s.required)
      .map(s => s.id);

    return {
      isRequired: step.required,
      dependencies,
      description: step.description,
      estimatedTime: step.estimatedTime
    };
  }

  /**
   * Check if application is ready for submission
   */
  async isReadyForSubmission(
    applicationId: string,
    licenseTypeId: string
  ): Promise<boolean> {
    const validation = await this.validateApplicationCompletion(applicationId, licenseTypeId);
    return validation.isValid;
  }
}

// Export singleton instance
export const stepValidationService = new StepValidationService();
