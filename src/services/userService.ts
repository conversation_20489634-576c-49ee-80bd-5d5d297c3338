import axios, { AxiosError } from 'axios';
import { createAuthenticatedAxios } from '../lib/auth';
import { usersApiClient, apiClient } from '../lib/apiClient';
import { processApiResponse } from '@/lib/authUtils';
import { Department } from '@/types/department';
import { Organization } from '@/types/organization';

// Types
export interface User {
  user_id: string;
  email: string;
  first_name: string;
  last_name: string;
  middle_name?: string;
  phone: string;
  department_id?: string;
  organization_id?: string;
  status: 'active' | 'inactive' | 'suspended';
  profile_image?: string;
  created_at: string;
  updated_at: string;
  last_login?: string;
  isAdmin?: boolean;
  roles?: Role[];
  department?: {
    department_id: string;
    name: string;
    code: string;
  };
}

export interface Role {
  role_id: string;
  name: string;
  description?: string;
  created_at: string;
  updated_at: string;
  permissions?: Permission[];
}

export interface Permission {
  permission_id: string;
  name: string;
  description: string;
  category: string;
  created_at: string;
  updated_at: string;
  roles?: Role[];
}

export interface CreateUserDto {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  middle_name?: string;
  phone: string;
  department_id?: string;
  organization_id?: string;
  status?: 'active' | 'inactive' | 'suspended';
  profile_image?: string;
  role_ids?: string[];
}

export interface UpdateUserDto {
  email?: string;
  password?: string;
  first_name?: string;
  last_name?: string;
  middle_name?: string;
  phone?: string;
  department_id?: string;
  organization_id?: string;
  status?: 'active' | 'inactive' | 'suspended';
  profile_image?: string;
  role_ids?: string[];
}

export interface UpdateProfileDto {
  email?: string;
  first_name?: string;
  last_name?: string;
  middle_name?: string;
  phone?: string;
  profile_image?: string;
}

export interface ChangePasswordDto {
  current_password: string;
  new_password: string;
  confirm_password: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  meta: {
    itemsPerPage: number;
    totalItems?: number;
    currentPage?: number;
    totalPages?: number;
    sortBy: [string, string][];
    searchBy: string[];
    search: string;
    select: string[];
    filter?: Record<string, string | string[]>;
  };
  links: {
    first?: string;
    previous?: string;
    current: string;
    next?: string;
    last?: string;
  };
}


export type UsersResponse = PaginatedResponse<User>;

export interface PaginateQuery {
  page?: number;
  limit?: number;
  sortBy?: string[];
  searchBy?: string[];
  search?: string;
  filter?: Record<string, string | string[]>;
}

export interface UserFilters extends Record<string, string | undefined> {
  department_id?: string;
  organization_id?: string;
  role?: string;
  status?: string;
}


export const userService = {
  // Get all users with pagination
  async getUsers(query: PaginateQuery = {}): Promise<UsersResponse> {
    const params = new URLSearchParams();

    if (query.page) params.set('page', query.page.toString());
    if (query.limit) params.set('limit', query.limit.toString());
    if (query.search) params.set('search', query.search);
    if (query.sortBy) {
      query.sortBy.forEach(sort => params.append('sortBy', sort));
    }
    if (query.searchBy) {
      query.searchBy.forEach(search => params.append('searchBy', search));
    }
    if (query.filter) {
      Object.entries(query.filter).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          value.forEach(v => params.append(`filter.${key}`, v));
        } else {
          params.set(`filter.${key}`, value);
        }
      });
    }

    const response = await usersApiClient.get(`?${params.toString()}`);
    return processApiResponse(response);
  },

  // Get user by ID
  async getUser(id: string): Promise<User> {
    const response = await usersApiClient.get(`/${id}`);
    return processApiResponse(response);
  },

  // Get user by ID (alias for consistency)
  async getUserById(id: string): Promise<User> {
    return this.getUser(id);
  },

  // Get current user profile
  async getProfile(): Promise<User> {
    const response = await usersApiClient.get('/profile');
    return processApiResponse(response);
  },

  // Create new user
  async createUser(userData: CreateUserDto): Promise<User> {
    const response = await usersApiClient.post('', userData);
    return processApiResponse(response);
  },

  // Update user
  async updateUser(id: string, userData: UpdateUserDto): Promise<User> {
    const response = await usersApiClient.put(`/${id}`, userData);
    return processApiResponse(response);
  },

  // Update current user profile
  async updateProfile(userData: UpdateProfileDto): Promise<User> {
    const response = await usersApiClient.put('/profile', userData);
    return processApiResponse(response);
  },

  // Change password
  async changePassword(passwordData: ChangePasswordDto): Promise<{ message: string }> {
    const response = await usersApiClient.put('/profile/password', passwordData);
    return processApiResponse(response);
  },

  // Upload avatar
  async uploadAvatar(file: File): Promise<User> {
    console.log('userService: uploadAvatar called', {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type
    });

    const formData = new FormData();
    formData.append('avatar', file);


    try {
      const response = await usersApiClient.post('/profile/avatar', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return processApiResponse(response);
    } catch (error: unknown) {
      const axiosError = error as AxiosError;
      console.error('userService: Upload failed', {
        status: axiosError.response?.status,
        statusText: axiosError.response?.statusText,
        data: axiosError.response?.data,
        message: axiosError.message
      });
      throw error;
    }
  },

  // Remove avatar
  async removeAvatar(): Promise<User> {
    const response = await usersApiClient.delete('/profile/avatar');
    return processApiResponse(response);
  },

  // Delete user
  async deleteUser(id: string): Promise<void> {
    await usersApiClient.delete(`/${id}`);
  },
};
