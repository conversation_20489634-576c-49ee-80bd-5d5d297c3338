// Connectivity utilities for diagnosing backend connection issues

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

export const testBackendConnectivity = async (): Promise<{
  isConnected: boolean;
  status?: number;
  error?: string;
  responseTime?: number;
}> => {
  const startTime = Date.now();
  
  try {
    // Test the health endpoint which should not require authentication
    const response = await fetch(`${API_BASE_URL}/health`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const responseTime = Date.now() - startTime;

    return {
      isConnected: response.ok,
      status: response.status,
      responseTime,
    };
  } catch (error: unknown) {
    const responseTime = Date.now() - startTime;
    
    return {
      isConnected: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      responseTime,
    };
  }
};

export const testUserEndpoint = async (token?: string): Promise<{
  isAccessible: boolean;
  status?: number;
  error?: string;
  requiresAuth?: boolean;
}> => {
  try {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    const response = await fetch(`${API_BASE_URL}/users?limit=1`, {
      method: 'GET',
      headers,
    });

    return {
      isAccessible: response.ok,
      status: response.status,
      requiresAuth: response.status === 401,
    };
  } catch (error: unknown) {
    return {
      isAccessible: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
};

export const diagnoseConnectivityIssue = async (token?: string) => {
  console.log('🔍 Starting backend connectivity diagnosis...');
  console.log(`📡 Testing connection to: ${API_BASE_URL}`);
  
  // Test basic connectivity
  const healthCheck = await testBackendConnectivity();
  console.log('🏥 Health check result:', healthCheck);
  
  if (!healthCheck.isConnected) {
    console.log('❌ Backend is not accessible. Possible issues:');
    console.log('   - Backend server is not running');
    console.log('   - Wrong API URL configuration');
    console.log('   - Network connectivity issues');
    console.log('   - Firewall blocking the connection');
    return;
  }
  
  console.log('✅ Backend is accessible');
  
  // Test users endpoint
  const userEndpointTest = await testUserEndpoint(token);
  console.log('👥 Users endpoint test:', userEndpointTest);
  
  if (!userEndpointTest.isAccessible) {
    if (userEndpointTest.requiresAuth) {
      console.log('🔒 Users endpoint requires authentication');
      console.log('   - Please ensure you are logged in');
      console.log('   - Check if your token is valid');
    } else {
      console.log('❌ Users endpoint is not accessible. Possible issues:');
      console.log('   - Endpoint is disabled or not implemented');
      console.log('   - Server error');
      console.log('   - Permission issues');
    }
  } else {
    console.log('✅ Users endpoint is accessible');
  }
};