// Form safety utilities for error handling and validation

/**
 * Log form errors with context and data for debugging
 */
export const logFormError = (context: string, error: any, formData?: any) => {
  console.error(`Form Error in ${context}:`, {
    error: error.message || error,
    formData,
    timestamp: new Date().toISOString(),
    stack: error.stack
  });
};

/**
 * Validate and normalize license type names for form configuration
 */
export const validateLicenseType = (licenseTypeName?: string): string | null => {
  if (!licenseTypeName) {
    console.warn('No license type name provided');
    return null;
  }
  
  const normalizedName = licenseTypeName.toLowerCase().trim();
  
  // Map license type names to form configurations
  if (normalizedName.includes('postal') || normalizedName.includes('courier') || normalizedName.includes('mail')) {
    return 'postal';
  } else if (normalizedName.includes('telecom') || normalizedName.includes('communication') || normalizedName.includes('internet')) {
    return 'telecommunications';
  } else if (normalizedName.includes('standard') || normalizedName.includes('approval') || normalizedName.includes('certification')) {
    return 'standards';
  } else if (normalizedName.includes('broadcast') || normalizedName.includes('media') || normalizedName.includes('radio') || normalizedName.includes('tv')) {
    return 'broadcasting';
  }
  
  // Default to general if no specific match
  console.warn(`Unknown license type: ${licenseTypeName}, using general form`);
  return 'general';
};

/**
 * Safely get nested object properties without throwing errors
 */
export const safeGet = (obj: any, path: string, defaultValue: any = null): any => {
  try {
    return path.split('.').reduce((current, key) => current?.[key], obj) ?? defaultValue;
  } catch (error) {
    console.warn(`Error accessing path ${path}:`, error);
    return defaultValue;
  }
};

/**
 * Sanitize form data to prevent XSS and other security issues
 */
export const sanitizeFormData = (data: Record<string, any>): Record<string, any> => {
  const sanitized: Record<string, any> = {};
  
  for (const [key, value] of Object.entries(data)) {
    if (typeof value === 'string') {
      // Basic HTML sanitization - remove script tags and dangerous attributes
      sanitized[key] = value
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/javascript:/gi, '')
        .replace(/on\w+\s*=/gi, '')
        .trim();
    } else if (typeof value === 'object' && value !== null) {
      // Recursively sanitize nested objects
      sanitized[key] = sanitizeFormData(value);
    } else {
      sanitized[key] = value;
    }
  }
  
  return sanitized;
};

/**
 * Validate file uploads for security
 */
export const validateFileUpload = (file: File, options: {
  maxSize?: number; // in MB
  allowedTypes?: string[];
} = {}): string | null => {
  const { maxSize = 10, allowedTypes = ['.pdf', '.doc', '.docx', '.jpg', '.jpeg', '.png'] } = options;
  
  // Check file size
  if (file.size > maxSize * 1024 * 1024) {
    return `File size must be less than ${maxSize}MB`;
  }
  
  // Check file type
  const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
  if (!allowedTypes.includes(fileExtension)) {
    return `File type must be: ${allowedTypes.join(', ')}`;
  }
  
  return null;
};

/**
 * Create a safe timeout for async operations
 */
export const withTimeout = <T>(
  promise: Promise<T>, 
  timeoutMs: number = 30000,
  errorMessage: string = 'Operation timed out'
): Promise<T> => {
  return Promise.race([
    promise,
    new Promise<never>((_, reject) => 
      setTimeout(() => reject(new Error(errorMessage)), timeoutMs)
    )
  ]);
};

/**
 * Retry failed operations with exponential backoff
 */
export const retryOperation = async <T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> => {
  let lastError: Error;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;
      
      if (attempt === maxRetries) {
        throw lastError;
      }
      
      // Exponential backoff
      const delay = baseDelay * Math.pow(2, attempt);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError!;
};

/**
 * Debounce function calls to prevent excessive API requests
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

/**
 * Throttle function calls to limit execution frequency
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

/**
 * Format error messages for user display
 */
export const formatErrorMessage = (error: any): string => {
  if (typeof error === 'string') {
    return error;
  }
  
  if (error?.response?.data?.message) {
    return error.response.data.message;
  }
  
  if (error?.message) {
    return error.message;
  }
  
  return 'An unexpected error occurred. Please try again.';
};

/**
 * Check if the current environment is development
 */
export const isDevelopment = (): boolean => {
  return process.env.NODE_ENV === 'development';
};

/**
 * Safe JSON parsing with fallback
 */
export const safeJsonParse = (jsonString: string, fallback: any = null): any => {
  try {
    return JSON.parse(jsonString);
  } catch (error) {
    console.warn('Failed to parse JSON:', error);
    return fallback;
  }
};

/**
 * Generate a unique ID for form elements
 */
export const generateFormId = (prefix: string = 'form'): string => {
  return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};
