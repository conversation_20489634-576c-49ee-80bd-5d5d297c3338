/**
 * Advanced Request Manager for handling rate limiting and throttling
 * Prevents ThrottlerException: Too Many Requests errors
 */

interface QueuedRequest {
  id: string;
  url: string;
  options: RequestInit;
  resolve: (value: Response) => void;
  reject: (reason: any) => void;
  priority: number;
  retryCount: number;
  timestamp: number;
}

interface RateLimitInfo {
  limit: number;
  remaining: number;
  resetTime: number;
  retryAfter?: number;
}

class RequestManager {
  private queue: QueuedRequest[] = [];
  private activeRequests = new Set<string>();
  private rateLimitInfo: Map<string, RateLimitInfo> = new Map();
  private requestCounts: Map<string, number[]> = new Map();
  private isProcessing = false;
  
  // Configuration
  private readonly MAX_CONCURRENT_REQUESTS = 3;
  private readonly MAX_REQUESTS_PER_MINUTE = 30;
  private readonly MAX_REQUESTS_PER_SECOND = 5;
  private readonly RETRY_DELAYS = [1000, 2000, 5000, 10000]; // Progressive delays
  private readonly REQUEST_TIMEOUT = 30000; // 30 seconds

  /**
   * Enhanced fetch with automatic rate limiting and retry logic
   */
  async fetch(url: string, options: RequestInit = {}, priority: number = 1): Promise<Response> {
    return new Promise((resolve, reject) => {
      const requestId = this.generateRequestId();
      const queuedRequest: QueuedRequest = {
        id: requestId,
        url,
        options: {
          ...options,
          signal: options.signal || AbortSignal.timeout(this.REQUEST_TIMEOUT)
        },
        resolve,
        reject,
        priority,
        retryCount: 0,
        timestamp: Date.now()
      };

      this.addToQueue(queuedRequest);
      this.processQueue();
    });
  }

  /**
   * Add request to queue with priority sorting
   */
  private addToQueue(request: QueuedRequest): void {
    this.queue.push(request);
    // Sort by priority (higher number = higher priority)
    this.queue.sort((a, b) => b.priority - a.priority);
  }

  /**
   * Process the request queue with rate limiting
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessing) return;
    this.isProcessing = true;

    while (this.queue.length > 0 && this.activeRequests.size < this.MAX_CONCURRENT_REQUESTS) {
      const request = this.queue.shift();
      if (!request) break;

      // Check if we should delay this request
      const delay = this.calculateDelay(request.url);
      if (delay > 0) {
        // Put request back in queue and wait
        this.queue.unshift(request);
        setTimeout(() => this.processQueue(), delay);
        break;
      }

      this.executeRequest(request);
    }

    this.isProcessing = false;
  }

  /**
   * Calculate delay needed before making request
   */
  private calculateDelay(url: string): number {
    const baseUrl = this.getBaseUrl(url);
    const now = Date.now();

    // Check rate limit info
    const rateLimitInfo = this.rateLimitInfo.get(baseUrl);
    if (rateLimitInfo && rateLimitInfo.resetTime > now) {
      if (rateLimitInfo.remaining <= 0) {
        return rateLimitInfo.resetTime - now;
      }
    }

    // Check request frequency
    const requestTimes = this.requestCounts.get(baseUrl) || [];
    const recentRequests = requestTimes.filter(time => now - time < 60000); // Last minute
    const veryRecentRequests = requestTimes.filter(time => now - time < 1000); // Last second

    if (recentRequests.length >= this.MAX_REQUESTS_PER_MINUTE) {
      return 60000 - (now - recentRequests[0]);
    }

    if (veryRecentRequests.length >= this.MAX_REQUESTS_PER_SECOND) {
      return 1000 - (now - veryRecentRequests[0]);
    }

    return 0;
  }

  /**
   * Execute individual request with error handling
   */
  private async executeRequest(request: QueuedRequest): Promise<void> {
    this.activeRequests.add(request.id);
    
    try {
      // Record request time
      this.recordRequest(request.url);

      const response = await fetch(request.url, request.options);
      
      // Update rate limit info from response headers
      this.updateRateLimitInfo(request.url, response);

      if (response.status === 429) {
        // Rate limited - retry with exponential backoff
        await this.handleRateLimit(request, response);
      } else if (!response.ok && this.shouldRetry(response.status)) {
        // Other retryable errors
        await this.retryRequest(request, new Error(`HTTP ${response.status}`));
      } else {
        request.resolve(response);
      }
    } catch (error) {
      if (this.shouldRetry(0) && request.retryCount < this.RETRY_DELAYS.length) {
        await this.retryRequest(request, error);
      } else {
        request.reject(error);
      }
    } finally {
      this.activeRequests.delete(request.id);
      // Continue processing queue
      setTimeout(() => this.processQueue(), 100);
    }
  }

  /**
   * Handle rate limit response
   */
  private async handleRateLimit(request: QueuedRequest, response: Response): Promise<void> {
    const retryAfter = this.getRetryAfter(response);
    const delay = Math.min(retryAfter * 1000, 60000); // Max 1 minute

    console.warn(`Rate limited. Retrying after ${delay}ms`);

    setTimeout(() => {
      request.retryCount++;
      this.addToQueue(request);
      this.processQueue();
    }, delay);
  }

  /**
   * Retry request with exponential backoff
   */
  private async retryRequest(request: QueuedRequest, error: any): Promise<void> {
    const delay = this.RETRY_DELAYS[request.retryCount] || this.RETRY_DELAYS[this.RETRY_DELAYS.length - 1];
    
    console.warn(`Request failed, retrying in ${delay}ms. Attempt ${request.retryCount + 1}`, error);

    setTimeout(() => {
      request.retryCount++;
      this.addToQueue(request);
      this.processQueue();
    }, delay);
  }

  /**
   * Record request timestamp for rate limiting
   */
  private recordRequest(url: string): void {
    const baseUrl = this.getBaseUrl(url);
    const now = Date.now();
    const requestTimes = this.requestCounts.get(baseUrl) || [];
    
    requestTimes.push(now);
    // Keep only recent requests
    const filtered = requestTimes.filter(time => now - time < 60000);
    this.requestCounts.set(baseUrl, filtered);
  }

  /**
   * Update rate limit information from response headers
   */
  private updateRateLimitInfo(url: string, response: Response): void {
    const baseUrl = this.getBaseUrl(url);
    const limit = parseInt(response.headers.get('X-RateLimit-Limit') || '0');
    const remaining = parseInt(response.headers.get('X-RateLimit-Remaining') || '0');
    const reset = parseInt(response.headers.get('X-RateLimit-Reset') || '0');
    const retryAfter = this.getRetryAfter(response);

    if (limit > 0) {
      this.rateLimitInfo.set(baseUrl, {
        limit,
        remaining,
        resetTime: reset * 1000, // Convert to milliseconds
        retryAfter
      });
    }
  }

  /**
   * Get retry-after value from response
   */
  private getRetryAfter(response: Response): number {
    const retryAfter = response.headers.get('Retry-After');
    if (retryAfter) {
      const seconds = parseInt(retryAfter);
      return isNaN(seconds) ? 60 : Math.min(seconds, 300); // Max 5 minutes
    }
    return 60; // Default 1 minute
  }

  /**
   * Check if error should be retried
   */
  private shouldRetry(status: number): boolean {
    return [408, 429, 500, 502, 503, 504].includes(status);
  }

  /**
   * Extract base URL for rate limiting
   */
  private getBaseUrl(url: string): string {
    try {
      const urlObj = new URL(url);
      return `${urlObj.protocol}//${urlObj.host}`;
    } catch {
      return url.split('/')[0];
    }
  }

  /**
   * Generate unique request ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get current queue status
   */
  getStatus() {
    return {
      queueLength: this.queue.length,
      activeRequests: this.activeRequests.size,
      rateLimitInfo: Object.fromEntries(this.rateLimitInfo),
      requestCounts: Object.fromEntries(
        Array.from(this.requestCounts.entries()).map(([url, times]) => [
          url,
          times.filter(time => Date.now() - time < 60000).length
        ])
      )
    };
  }

  /**
   * Clear all queued requests
   */
  clearQueue(): void {
    this.queue.forEach(request => {
      request.reject(new Error('Request cancelled'));
    });
    this.queue = [];
  }
}

// Global instance
export const requestManager = new RequestManager();

// Enhanced fetch function that uses the request manager
export const managedFetch = (url: string, options?: RequestInit, priority?: number): Promise<Response> => {
  return requestManager.fetch(url, options, priority);
};

export default requestManager;
