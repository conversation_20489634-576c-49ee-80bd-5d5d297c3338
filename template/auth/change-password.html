<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Change Password - Digital Portal Dashboard</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: { primary: "#e02b20", secondary: "#6366f1" },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"
    />
    <style>
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
        font-family: 'Inter', sans-serif;
        background-color: #f9fafb;
      }
      .side-nav{
        overflow: auto;
        -ms-overflow-style: none;
        height: 75vh;
      }
      .side-nav::-webkit-scrollbar {
        display: none;
      }
      .side-nav {
        scrollbar-width: none;
      }
    </style>
  </head>
  <body>
    <div class="flex h-screen overflow-hidden">
      <!-- Sidebar -->
      <aside class="w-64 bg-white shadow-lg flex-shrink-0 hidden md:block">
        <div class="h-16 flex items-center px-6 border-b">
          <div class="flex items-center">
            <img src="../images/macra-logo.png" alt="Logo" class="max-h-12 w-auto">
          </div>
        </div>
        <nav class="mt-6 px-4 side-nav">
          <div class="space-y-1">
            <a
              href="index.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-dashboard-line"></i>
              </div>
              Dashboard
            </a>
            <a
              href="user-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-user-line"></i>
              </div>
              User Management
            </a>
            <a
              href="license-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-key-line"></i>
              </div>
              License Management
            </a>
            <a
              href="spectrum-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z" />
                </svg>
              </div>
              Spectrum Management
            </a>
            <a
              href="transaction-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-exchange-dollar-line"></i>
              </div>
              Transaction Management
            </a>
            <a
              href="reports.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-file-chart-line"></i>
              </div>
              Reports
            </a>
          </div>
          <div class="mt-8">
            <h3
              class="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider"
            >
              Spectrum Tools
            </h3>
            <div class="mt-2 space-y-1">
              <a
                href="frequency-allocation.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-radar-line"></i>
                </div>
                Frequency Allocation
              </a>
              <a
                href="spectrum-monitoring.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-pulse-line"></i>
                </div>
                Spectrum Monitoring
              </a>
              <a
                href="interference-management.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-error-warning-line"></i>
                </div>
                Interference Management
              </a>
              <a
                href="spectrum-license-application.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-file-list-3-line"></i>
                </div>
                License Application
              </a>
            </div>
          </div>
          <div class="mt-8">
            <h3
              class="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider"
            >
              Settings
            </h3>
            <div class="mt-2 space-y-1">
              <a
                href="user-profile.html"
                class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md bg-primary bg-opacity-10 text-primary"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-user-settings-line"></i>
                </div>
                My Profile
              </a>
              <a
                href="system-settings.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-settings-3-line"></i>
                </div>
                Settings
              </a>
            </div>
          </div>
        </nav>
      </aside>

      <!-- Main content -->
      <div class="flex-1 flex flex-col overflow-hidden">
        <!-- Top navigation -->
        <header class="bg-white shadow-sm z-10">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
              <div class="flex">
                <div class="flex-shrink-0 flex items-center md:hidden">
                  <img src="../images/macra-logo.png" alt="Logo" class="h-8 w-auto">
                </div>
                <button
                  type="button"
                  class="md:hidden inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary"
                >
                  <span class="sr-only">Open main menu</span>
                  <div class="w-6 h-6 flex items-center justify-center">
                    <i class="ri-menu-line ri-lg"></i>
                  </div>
                </button>
              </div>
              <div
                class="flex-1 flex items-center justify-center px-2 lg:ml-6 lg:justify-start"
              >
                <div class="max-w-lg w-full">
                  <label for="search" class="sr-only">Search</label>
                  <div class="relative">
                    <div
                      class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                    >
                      <div
                        class="w-5 h-5 flex items-center justify-center text-gray-400"
                      >
                        <i class="ri-search-line"></i>
                      </div>
                    </div>
                    <input
                      id="search"
                      name="search"
                      class="block w-full pl-10 pr-3 py-2 border border-gray-200 rounded-md leading-5 bg-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary text-sm"
                      placeholder="Search..."
                      type="search"
                    />
                  </div>
                </div>
              </div>
              <div class="flex items-center">
                <div class="flex-shrink-0 relative ml-4">
                  <div>
                    <button
                      type="button"
                      class="bg-white rounded-full flex focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                    >
                      <span class="sr-only">View notifications</span>
                      <div
                        class="w-6 h-6 flex items-center justify-center text-gray-400"
                      >
                        <i class="ri-notification-3-line"></i>
                      </div>
                    </button>
                  </div>
                </div>
                <div class="ml-4 relative flex-shrink-0">
                  <div>
                    <button
                      type="button"
                      class="bg-white rounded-full flex text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                    >
                      <span class="sr-only">Open user menu</span>
                      <img
                        class="h-8 w-8 rounded-full"
                        src="https://images.unsplash.com/photo-1550525811-e5869dd03032?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
                        alt=""
                      />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </header>

        <!-- Main content area -->
        <main class="flex-1 overflow-y-auto bg-gray-50 p-4 sm:p-6">
          <div class="max-w-7xl mx-auto">
            <!-- Page header -->
            <div class="mb-6">
              <div class="flex items-center justify-between">
                <h1 class="text-2xl font-semibold text-gray-900">Change Password</h1>
                <div>
                  <a
                    href="user-profile.html"
                    class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    <div class="w-5 h-5 flex items-center justify-center mr-2">
                      <i class="ri-arrow-left-line"></i>
                    </div>
                    Back to Profile
                  </a>
                </div>
              </div>
              <p class="mt-1 text-sm text-gray-500">
                Update your password to keep your account secure.
              </p>
            </div>

            <div class="bg-white shadow overflow-hidden sm:rounded-lg">
              <div class="px-4 py-5 sm:p-6">
                <form action="user-profile.html" method="GET">
                  <div class="space-y-6">
                    <div>
                      <label for="current-password" class="block text-sm font-medium text-gray-700">
                        Current Password
                      </label>
                      <div class="mt-1">
                        <input id="current-password" name="current-password" type="password" autocomplete="current-password" required class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md">
                      </div>
                    </div>

                    <div>
                      <label for="new-password" class="block text-sm font-medium text-gray-700">
                        New Password
                      </label>
                      <div class="mt-1">
                        <input id="new-password" name="new-password" type="password" autocomplete="new-password" required class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md">
                      </div>
                      <p class="mt-2 text-sm text-gray-500">
                        Your password must be at least 8 characters, and include a number, a symbol, and an uppercase letter.
                      </p>
                    </div>

                    <div>
                      <label for="confirm-password" class="block text-sm font-medium text-gray-700">
                        Confirm New Password
                      </label>
                      <div class="mt-1">
                        <input id="confirm-password" name="confirm-password" type="password" autocomplete="new-password" required class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md">
                      </div>
                    </div>
                  </div>

                  <div class="mt-6">
                    <h3 class="text-sm font-medium text-gray-700">Password requirements:</h3>
                    <div class="mt-2">
                      <ul class="pl-4 list-disc text-xs text-gray-500 space-y-2">
                        <li>Minimum 8 characters long - the longer, the better</li>
                        <li>At least one lowercase character</li>
                        <li>At least one uppercase character</li>
                        <li>At least one number</li>
                        <li>At least one special character (e.g. !@#$%^&*)</li>
                      </ul>
                    </div>
                  </div>

                  <div class="mt-6 flex justify-end">
                    <a href="user-profile.html" class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                      Cancel
                    </a>
                    <button type="submit" class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                      Update Password
                    </button>
                  </div>
                </form>
              </div>
            </div>

            <!-- Security tips -->
            <div class="mt-6">
              <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                <div class="px-4 py-5 sm:px-6">
                  <h3 class="text-lg leading-6 font-medium text-gray-900">
                    Password Security Tips
                  </h3>
                  <p class="mt-1 max-w-2xl text-sm text-gray-500">
                    Follow these best practices to keep your account secure.
                  </p>
                </div>
                <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
                  <div class="space-y-4">
                    <div class="flex">
                      <div class="flex-shrink-0">
                        <div class="h-5 w-5 text-green-500">
                          <i class="ri-check-line"></i>
                        </div>
                      </div>
                      <div class="ml-3 text-sm text-gray-700">
                        <p>Use a unique password for each of your important accounts</p>
                      </div>
                    </div>
                    <div class="flex">
                      <div class="flex-shrink-0">
                        <div class="h-5 w-5 text-green-500">
                          <i class="ri-check-line"></i>
                        </div>
                      </div>
                      <div class="ml-3 text-sm text-gray-700">
                        <p>Use a password manager to generate and store strong passwords</p>
                      </div>
                    </div>
                    <div class="flex">
                      <div class="flex-shrink-0">
                        <div class="h-5 w-5 text-green-500">
                          <i class="ri-check-line"></i>
                        </div>
                      </div>
                      <div class="ml-3 text-sm text-gray-700">
                        <p>Enable two-factor authentication for additional security</p>
                      </div>
                    </div>
                    <div class="flex">
                      <div class="flex-shrink-0">
                        <div class="h-5 w-5 text-green-500">
                          <i class="ri-check-line"></i>
                        </div>
                      </div>
                      <div class="ml-3 text-sm text-gray-700">
                        <p>Change your passwords regularly, especially for sensitive accounts</p>
                      </div>
                    </div>
                    <div class="flex">
                      <div class="flex-shrink-0">
                        <div class="h-5 w-5 text-red-500">
                          <i class="ri-close-line"></i>
                        </div>
                      </div>
                      <div class="ml-3 text-sm text-gray-700">
                        <p>Never share your password with anyone, including colleagues</p>
                      </div>
                    </div>
                    <div class="flex">
                      <div class="flex-shrink-0">
                        <div class="h-5 w-5 text-red-500">
                          <i class="ri-close-line"></i>
                        </div>
                      </div>
                      <div class="ml-3 text-sm text-gray-700">
                        <p>Don't use easily guessable information like birthdays or names</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  </body>
</html>
