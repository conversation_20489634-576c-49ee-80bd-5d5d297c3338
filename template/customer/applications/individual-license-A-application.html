<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Individual License Application - Form A - Digital Portal Dashboard</title>
  <script src="https://cdn.tailwindcss.com/3.4.16"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css"
    integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg=="
    crossorigin="anonymous" referrerpolicy="no-referrer" />
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: { primary: "#e02b20", secondary: "#6366f1", "primary-subtle": "#e4463c", "secondary-subtle": "#9FE2BF"},
          borderRadius: {
            none: "0px",
            sm: "4px",
            DEFAULT: "8px",
            md: "12px",
            lg: "16px",
            xl: "20px",
            "2xl": "24px",
            "3xl": "32px",
            full: "9999px",
            button: "8px",
          },
        },
      },
    };
  </script>
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css" />
  <link rel="stylesheet" href="../assets/main.css">
  <style type="text/tailwindcss">
    @layer components {
      .custom-form-label {
        @apply block text-sm font-medium text-gray-700 pb-2;
      }
      .enhanced-input {
        @apply appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm;
      }

      .enhanced-select {
        @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm;
      }

      .enhanced-checkbox {
        @apply h-5 w-5 text-primary focus:ring-primary border border-gray-300 rounded;
      }

      .main-button {
        @apply inline-flex py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-subtle hover:bg-primary focus:ring focus:ring-primary-subtle;
      }

      .secondary-main-button {
        @apply inline-flex py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring focus:ring-secondary-subtle/50;
      }

      .custom-input {
        @apply mt-1 block w-full px-3 py-2 border border-secondary-subtle rounded-md placeholder-gray-400 text-sm focus:shadow-md focus:shadow-secondary-subtle focus:border-secondary-subtle;
      }

      .form-section {
        @apply flex-col flex gap-y-2 lg:border-b divide-solid border-gray-300;
      }

      .inner-form-section {
        @apply mt-4 grid gap-y-6 gap-x-6 sm:grid-cols-2 lg:grid-cols-2;
      }

      .tab-heading {
        @apply flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0 mb-6;
      }

      .form-group {
        @apply mb-6;
      }

      .step-indicator {
        @apply flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium;
      }

      .step-indicator.active {
        @apply bg-primary text-white;
      }

      .step-indicator.completed {
        @apply bg-green-500 text-white;
      }

      .step-indicator.inactive {
        @apply bg-gray-200 text-gray-500;
      }

      .step-content {
        @apply hidden;
      }

      .step-content.active {
        @apply block;
      }

      .progress-bar {
        @apply w-full bg-gray-200 rounded-full h-2;
      }

      .progress-fill {
        @apply bg-primary h-2 rounded-full transition-all duration-300;
      }

      .file-upload-area {
        @apply border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-primary cursor-pointer transition-colors;
      }

      .tracking-status {
        @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
      }

      .status-draft {
        @apply bg-gray-100 text-gray-800;
      }

      .status-submitted {
        @apply bg-blue-100 text-blue-800;
      }

      .status-review {
        @apply bg-yellow-100 text-yellow-800;
      }

      .status-approved {
        @apply bg-green-100 text-green-800;
      }

      .status-rejected {
        @apply bg-red-100 text-red-800;
      }

      .evaluation-table {
        @apply w-full border-collapse border border-gray-300;
      }

      .evaluation-table th,
      .evaluation-table td {
        @apply border border-gray-300 px-4 py-2 text-left;
      }

      .evaluation-table th {
        @apply bg-gray-100 font-medium;
      }
    }

    @layer utilities {
      :root {
        --color-primary: #e02b20;
        --color-secondary: #20d5e0;
        --color-primary-subtle: #e4463c;
        --color-secondary-subtle: #abeff3;
      }
    }

  </style>

  <style>
    :where([class^="ri-"])::before {
      content: "\f3c2";
    }

    body {
      font-family: 'Inter', sans-serif;
      background-color: #f9fafb;
    }

    .dropdown-content {
      display: none;
      position: absolute;
      right: 0;
      top: 100%;
      z-index: 50;
    }

    .dropdown-content.show {
      display: block;
    }

    .side-nav {
      overflow: auto;
      -ms-overflow-style: none;
      height: 75vh;
    }

    .side-nav::-webkit-scrollbar {
      display: none;
    }

    /* Hide scrollbar for Firefox */
    .side-nav {
      scrollbar-width: none;
    }

    /* Mobile sidebar styles */
    @media (max-width: 768px) {
      .mobile-sidebar-open {
        display: block !important;
        position: fixed;
        z-index: 50;
        height: 100vh;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
      }

      .mobile-sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 40;
        display: none;
      }

      .mobile-sidebar-overlay.show {
        display: block;
      }
    }

    .error-message {
      color: #dc2626;
      font-size: 0.875rem;
      margin-top: 0.25rem;
    }

    .input-error {
      border-color: #dc2626 !important;
    }
  </style>
</head>

<body>
  <div class="flex h-screen overflow-hidden">
    <!-- Mobile sidebar overlay -->
    <div id="mobileSidebarOverlay" class="mobile-sidebar-overlay"></div>

    <!-- Sidebar -->
    <aside id="sidebar" class="w-64 bg-white shadow-lg flex-shrink-0 hidden md:block">
      <div class="h-16 flex items-center px-6 border-b">
        <div class="flex items-center">
          <img src="../images/macra-logo.png" alt="Logo" class="max-h-12 w-auto">
        </div>
      </div>
      <nav class="mt-6 px-4 side-nav">
        <div class="space-y-1">
          <a href="../index.html"
            class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md bg-primary bg-opacity-10 text-primary">
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <i class="ri-dashboard-line"></i>
            </div>
            Dashboard
          </a>
          <a href="../new-application.html"
            class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <i class="ri-add-line"></i>
            </div>
            New Application
          </a>
          <a href="../my-licenses.html"
            class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <i class="ri-key-line"></i>
            </div>
            My Licenses
          </a>
          <a href="../invoices.html"
            class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <i class="ri-file-text-line"></i>
            </div>
            Invoices
          </a>
          <a href="../payments.html"
            class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <i class="ri-bank-card-line"></i>
            </div>
            Payments
          </a>
          <a href="../documents.html"
            class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <i class="ri-folder-line"></i>
            </div>
            Documents
          </a>
          <a href="../contact-support.html"
            class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <i class="ri-customer-service-line"></i>
            </div>
            Support
          </a>
        </div>
      </nav>
      <div class="absolute bottom-0 w-64 p-4 border-t">
        <a href="../profile.html" class="flex items-center hover:bg-gray-50 p-2 rounded-md">
          <img class="h-10 w-10 rounded-full"
            src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp" alt="Profile" />
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-900">John Doe</p>
            <p class="text-xs text-gray-500">Customer</p>
          </div>
        </a>
      </div>
    </aside>

    <!-- Main content -->
    <div class="flex-1 flex flex-col overflow-hidden">
      <!-- Top header -->
      <header class="bg-white shadow-sm z-10">
        <div class="flex items-center justify-between h-16 px-4 sm:px-6">
          <button id="mobileMenuBtn" type="button" onclick="toggleMobileSidebar()"
            class="md:hidden text-gray-500 hover:text-gray-700 focus:outline-none">
            <div class="w-6 h-6 flex items-center justify-center">
              <i class="ri-menu-line"></i>
            </div>
          </button>
          <div class="flex-1 flex items-center justify-center px-2 lg:ml-6 lg:justify-start">
            <div class="max-w-lg w-full">
              <label for="search" class="sr-only">Search</label>
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <div class="w-5 h-5 flex items-center justify-center text-gray-400">
                    <i class="ri-search-line"></i>
                  </div>
                </div>
                <input id="search" name="search"
                  class="block w-full pl-10 pr-3 py-2 border border-gray-200 rounded-md leading-5 bg-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary text-sm"
                  placeholder="Search applications, licenses, or documents..." type="search" />
              </div>
            </div>
          </div>
          <div class="flex items-center">
            <button type="button"
              class="flex-shrink-0 p-1 mr-4 text-gray-500 rounded-full hover:text-gray-700 focus:outline-none relative">
              <span class="sr-only">View notifications</span>
              <div class="w-6 h-6 flex items-center justify-center">
                <i class="ri-notification-3-line ri-lg"></i>
              </div>
              <span class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white"></span>
            </button>
            <div class="dropdown relative">
              <button type="button" onclick="toggleDropdown()"
                class="flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                <span class="sr-only">Open user menu</span>
                <img class="h-8 w-8 rounded-full"
                  src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp" alt="Profile" />
              </button>
              <div id="userDropdown"
                class="dropdown-content mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
                <div class="py-1">
                  <a href="profile.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Your Profile</a>
                  <a href="help-center.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Help Center</a>
                  <a href="../auth/login.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Sign out</a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      <!-- Main content area -->
      <main class="flex-1 overflow-y-auto bg-gray-50 p-4 sm:p-6">
        <div class="max-w-7xl mx-auto">
          <!-- Page header-->
          <div class="tab-heading">
            <div>
              <h1 class="text-2xl font-semibold text-gray-900">Individual License Application - Class A</h1>
              <p class="mt-1 text-sm text-gray-600">Complete your class A license application with comprehensive tracking</p>
            </div>
            <div class="relative">
              <a href="../new-application.html" class="secondary-main-button" role="button">
                <div class="w-5 h-5 flex items-center justify-center mr-2">
                  <i class="ri-arrow-left-line"></i>
                </div>
                Back to Applications
              </a>
            </div>
          </div>

          <!-- Application Status & Tracking -->
          <div class="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
            <div class="px-4 py-5 sm:p-6">
              <div class="flex items-center justify-between mb-4">
                <div>
                  <h3 class="text-lg font-medium text-gray-900">Application Status</h3>
                  <p class="text-sm text-gray-500">Application ID: <span id="applicationId" class="font-mono">LIC-A-2024-001</span></p>
                </div>
                <div>
                  <span id="applicationStatus" class="tracking-status status-draft">Draft</span>
                </div>
              </div>

              <!-- Progress Bar -->
              <div class="mb-4">
                <div class="flex justify-between text-sm text-gray-600 mb-2">
                  <span>Progress</span>
                  <span id="progressPercentage">0%</span>
                </div>
                <div class="progress-bar">
                  <div id="progressFill" class="progress-fill" style="width: 0%"></div>
                </div>
              </div>

              <!-- Step Indicators -->
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <div id="step1Indicator" class="step-indicator active">1</div>
                  <div class="w-12 h-0.5 bg-gray-200 mx-2"></div>
                  <div id="step2Indicator" class="step-indicator inactive">2</div>
                  <div class="w-12 h-0.5 bg-gray-200 mx-2"></div>
                  <div id="step3Indicator" class="step-indicator inactive">3</div>
                  <div class="w-12 h-0.5 bg-gray-200 mx-2"></div>
                  <div id="step4Indicator" class="step-indicator inactive">4</div>
                  <div class="w-12 h-0.5 bg-gray-200 mx-2"></div>
                  <div id="step5Indicator" class="step-indicator inactive">5</div>
                  <div class="w-12 h-0.5 bg-gray-200 mx-2"></div>
                  <div id="step6Indicator" class="step-indicator inactive">6</div>
                </div>
              </div>

              <!-- Step Labels -->
              <div class="flex justify-between text-xs text-gray-500 mt-2">
                <span>License Info</span>
                <span>Applicant Details</span>
                <span>Business Plan</span>
                <span>Technical</span>
                <span>Evaluation</span>
                <span>Undertaking</span>
              </div>
            </div>
          </div>

          <!-- Application Form -->
          <div class="bg-white shadow overflow-hidden sm:rounded-lg">
            <div class="px-4 py-5 sm:p-6">
              <form id="licenseApplicationForm" class="space-y-8">

                <!-- Step 1: License Information -->
                <div id="step1" class="step-content active">
                  <div class="form-section">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">License Information</h3>

                    <div class="inner-form-section">
                      <!-- License Category -->
                      <div>
                        <label for="licenceCategory" class="custom-form-label">License Category *</label>
                        <select id="licenceCategory" name="licenceCategory" class="custom-input" required onchange="toggleLicenseType()">
                          <option value="">Select Category</option>
                          <option value="international">International</option>
                          <option value="national">National</option>
                          <option value="regional">Regional</option>
                          <option value="district">District</option>
                        </select>
                      </div>

                      <!-- Type of License -->
                      <div>
                        <label for="licenceType" class="custom-form-label">Type of License *</label>
                        <select id="licenceType" name="licenceType" class="custom-input" required disabled>
                          <option value="">Select License Type</option>
                          <option value="facilities">Facilities Service License</option>
                          <option value="network">Network Service License</option>
                          <option value="application">Application Service License</option>
                          <option value="content">Content Service License</option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Step 2: Details of Applicant -->
                <div id="step2" class="step-content">
                  <div class="form-section">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Details of Applicant</h3>

                    <div class="inner-form-section">
                      <!-- Applicant Name -->
                      <div class="sm:col-span-2">
                        <label for="applicantName" class="custom-form-label">Applicant name *</label>
                        <input type="text" name="applicantName" id="applicantName" class="custom-input" required>
                      </div>

                      <!-- Applicant Profile -->
                      <div class="sm:col-span-2">
                        <label for="applicantProfile" class="custom-form-label">Applicant Profile *</label>
                        <textarea id="applicantProfile" name="applicantProfile" rows="4" class="custom-input" required placeholder="Provide a detailed description of your organization's background, mission, and activities"></textarea>
                      </div>

                      <!-- Applicant Website -->
                      <div class="sm:col-span-2">
                        <label for="applicantWebsite" class="custom-form-label">Applicant website *</label>
                        <input type="url" name="applicantWebsite" id="applicantWebsite" class="custom-input" required placeholder="https://www.company.com">
                      </div>
                    </div>

                    <!-- Contact Details Subsection -->
                    <div class="mt-8">
                      <h4 class="text-md font-medium text-gray-800 mb-4">Applicant Contact Details</h4>

                      <div class="inner-form-section">
                        <!-- Contact Email -->
                        <div>
                          <label for="applicantEmail" class="custom-form-label">Contact email *</label>
                          <input type="email" name="applicantEmail" id="applicantEmail" class="custom-input" required>
                        </div>

                        <!-- Contact Phone -->
                        <div>
                          <label for="applicantPhone" class="custom-form-label">Contact phone *</label>
                          <input type="tel" name="applicantPhone" id="applicantPhone" class="custom-input" required pattern="[0-9]{10}" placeholder="0123456789">
                          <p class="text-xs text-gray-500 mt-1">Enter 10-digit phone number</p>
                        </div>

                        <!-- Fax Number -->
                        <div>
                          <label for="applicantFax" class="custom-form-label">Fax number</label>
                          <input type="tel" name="applicantFax" id="applicantFax" class="custom-input" pattern="[0-9]{10}" placeholder="0123456789">
                          <p class="text-xs text-gray-500 mt-1">Enter 10-digit fax number (optional)</p>
                        </div>

                        <!-- Company Registration Number -->
                        <div>
                          <label for="applicantRegNo" class="custom-form-label">Company Registration Number *</label>
                          <input type="text" name="applicantRegNo" id="applicantRegNo" class="custom-input" required>
                        </div>

                        <!-- TPIN -->
                        <div>
                          <label for="applicantTPIN" class="custom-form-label">Tax Payer Identification Number - TPIN *</label>
                          <input type="text" name="applicantTPIN" id="applicantTPIN" class="custom-input" required pattern="[0-9]+" placeholder="Enter digits only">
                          <p class="text-xs text-gray-500 mt-1">Enter digits only</p>
                        </div>

                        <!-- Postal Address -->
                        <div class="sm:col-span-2">
                          <label for="applicantPostal" class="custom-form-label">Postal Address *</label>
                          <textarea id="applicantPostal" name="applicantPostal" rows="3" class="custom-input" required placeholder="Must include P.O. Box, P/Bag, Private Bag, or PO Box"></textarea>
                          <p class="text-xs text-gray-500 mt-1">Address must contain P.O. Box, P/Bag, Private Bag, or PO Box</p>
                        </div>

                        <!-- Physical Address -->
                        <div class="sm:col-span-2">
                          <label for="applicantPhysical" class="custom-form-label">Physical Address *</label>
                          <textarea id="applicantPhysical" name="applicantPhysical" rows="3" class="custom-input" required placeholder="Must include P.O. Box, P/Bag, Private Bag, or PO Box"></textarea>
                          <p class="text-xs text-gray-500 mt-1">Address must contain P.O. Box, P/Bag, Private Bag, or PO Box</p>
                        </div>
                      </div>
                    </div>

                    <!-- Contact Person Subsection -->
                    <div class="mt-8">
                      <h4 class="text-md font-medium text-gray-800 mb-4">Contact Person</h4>

                      <div class="inner-form-section">
                        <!-- Contact Person Name -->
                        <div>
                          <label for="applicantContact" class="custom-form-label">Contact Person Name *</label>
                          <input type="text" name="applicantContact" id="applicantContact" class="custom-input" required>
                        </div>

                        <!-- Contact Person Designation -->
                        <div>
                          <label for="applicantContactDesignation" class="custom-form-label">Contact Person Designation *</label>
                          <input type="text" name="applicantContactDesignation" id="applicantContactDesignation" class="custom-input" required>
                        </div>

                        <!-- Contact Person Phone -->
                        <div>
                          <label for="applicantContactPhone" class="custom-form-label">Contact Person Phone *</label>
                          <input type="tel" name="applicantContactPhone" id="applicantContactPhone" class="custom-input" required pattern="[0-9]{10}" placeholder="0123456789">
                          <p class="text-xs text-gray-500 mt-1">Enter 10-digit phone number</p>
                        </div>

                        <!-- Contact Person Email -->
                        <div>
                          <label for="applicantContactEmail" class="custom-form-label">Contact Person Email Address *</label>
                          <input type="email" name="applicantContactEmail" id="applicantContactEmail" class="custom-input" required>
                        </div>

                        <!-- Contact Person Website -->
                        <div class="sm:col-span-2">
                          <label for="applicantContactWebsite" class="custom-form-label">Contact Person Website *</label>
                          <input type="url" name="applicantContactWebsite" id="applicantContactWebsite" class="custom-input" required placeholder="https://www.website.com">
                        </div>
                      </div>
                    </div>

                    <!-- Details of Incorporation Subsection -->
                    <div class="mt-8">
                      <h4 class="text-md font-medium text-gray-800 mb-4">Details of Incorporation</h4>

                      <div class="inner-form-section">
                        <!-- Date of Incorporation -->
                        <div>
                          <label for="applicantDateIncorporation" class="custom-form-label">Date of incorporation *</label>
                          <input type="date" name="applicantDateIncorporation" id="applicantDateIncorporation" class="custom-input" required>
                        </div>

                        <!-- Place of Incorporation -->
                        <div>
                          <label for="applicantPlaceIncorporation" class="custom-form-label">Place of incorporation *</label>
                          <input type="text" name="applicantPlaceIncorporation" id="applicantPlaceIncorporation" class="custom-input" required>
                        </div>
                      </div>
                    </div>

                    <!-- Ownership and Control Subsection -->
                    <div class="mt-8">
                      <h4 class="text-md font-medium text-gray-800 mb-4">Ownership and Control</h4>
                      <p class="text-sm text-gray-600 mb-4">Please provide Incorporation and Registration Documents below (Copies should be notarised and documents with company letterhead):</p>

                      <div class="space-y-6">
                        <!-- Certificate of Incorporation -->
                        <div>
                          <label class="custom-form-label">Certified copies of Certificate of Incorporation *</label>
                          <div class="file-upload-area" onclick="document.getElementById('legalConstitution').click()">
                            <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                            <p class="text-sm text-gray-600">Upload Certificate of Incorporation</p>
                            <p class="text-xs text-gray-500">PDF up to 10MB</p>
                          </div>
                          <input type="file" id="legalConstitution" name="legalConstitution" accept=".pdf" class="hidden" required>
                          <div id="legalConstitutionList" class="mt-2"></div>
                        </div>

                        <!-- Memorandum of Association -->
                        <div>
                          <label class="custom-form-label">Memorandum of Association *</label>
                          <div class="file-upload-area" onclick="document.getElementById('legalMemorandum').click()">
                            <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                            <p class="text-sm text-gray-600">Upload Memorandum of Association</p>
                            <p class="text-xs text-gray-500">PDF up to 10MB</p>
                          </div>
                          <input type="file" id="legalMemorandum" name="legalMemorandum" accept=".pdf" class="hidden" required>
                          <div id="legalMemorandumList" class="mt-2"></div>
                        </div>

                        <!-- Shareholding Structure -->
                        <div>
                          <label class="custom-form-label">Shareholding Structure *</label>
                          <div class="file-upload-area" onclick="document.getElementById('legalShareholdingStructure').click()">
                            <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                            <p class="text-sm text-gray-600">Upload Shareholding Structure</p>
                            <p class="text-xs text-gray-500">PDF up to 10MB</p>
                          </div>
                          <input type="file" id="legalShareholdingStructure" name="legalShareholdingStructure" accept=".pdf" class="hidden" required>
                          <div id="legalShareholdingStructureList" class="mt-2"></div>
                        </div>

                        <!-- Partnership Agreement -->
                        <div>
                          <label class="custom-form-label">Partnership Agreement *</label>
                          <div class="file-upload-area" onclick="document.getElementById('legalPartnershipAgreement').click()">
                            <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                            <p class="text-sm text-gray-600">Upload Partnership Agreement</p>
                            <p class="text-xs text-gray-500">PDF up to 10MB</p>
                          </div>
                          <input type="file" id="legalPartnershipAgreement" name="legalPartnershipAgreement" accept=".pdf" class="hidden" required>
                          <div id="legalPartnershipAgreementList" class="mt-2"></div>
                        </div>

                        <!-- Ownership Interest -->
                        <div>
                          <label class="custom-form-label">Brief description of all direct and indirect ownership interest in the applicant including beneficial ownership interest *</label>
                          <div class="file-upload-area" onclick="document.getElementById('legalOwnership').click()">
                            <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                            <p class="text-sm text-gray-600">Upload Ownership Interest Description</p>
                            <p class="text-xs text-gray-500">PDF up to 10MB</p>
                          </div>
                          <input type="file" id="legalOwnership" name="legalOwnership" accept=".pdf" class="hidden" required>
                          <div id="legalOwnershipList" class="mt-2"></div>
                        </div>

                        <!-- Board of Directors -->
                        <div>
                          <label class="custom-form-label">Composition of Board of Directors - If Applicable</label>
                          <div class="file-upload-area" onclick="document.getElementById('legalBoardDirectors').click()">
                            <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                            <p class="text-sm text-gray-600">Upload Board of Directors Composition</p>
                            <p class="text-xs text-gray-500">PDF up to 10MB (Optional)</p>
                          </div>
                          <input type="file" id="legalBoardDirectors" name="legalBoardDirectors" accept=".pdf" class="hidden">
                          <div id="legalBoardDirectorsList" class="mt-2"></div>
                        </div>
                      </div>
                    </div>

                    <!-- Details of Key Management Team -->
                    <div class="mt-8">
                      <h4 class="text-md font-medium text-gray-800 mb-4">Details of Key Management Team</h4>
                      <p class="text-sm text-gray-600 mb-4">Please provide detailed information about your key management personnel (minimum 3, maximum 7 members).</p>

                      <div class="mb-6">
                        <label for="managementTeamCount" class="custom-form-label">Number of management team members (3-7) *</label>
                        <select id="managementTeamCount" name="managementTeamCount" class="custom-input" required onchange="generateManagementTeamForms()">
                          <option value="">Select Number of Members</option>
                          <option value="3">3 Members</option>
                          <option value="4">4 Members</option>
                          <option value="5">5 Members</option>
                          <option value="6">6 Members</option>
                          <option value="7">7 Members</option>
                        </select>
                      </div>

                      <div id="managementTeamFormsContainer" class="space-y-6">
                        <!-- Management team forms will be generated dynamically -->
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Step 3: Business Plan -->
                <div id="step3" class="step-content">
                  <div class="form-section">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Business Plan</h3>
                    <p class="text-sm text-gray-600 mb-6">Please provide the following:</p>

                    <div class="space-y-8">
                      <!-- 1. Capital Expenditure -->
                      <div>
                        <h4 class="text-md font-medium text-gray-800 mb-3">1. Proposed capital expenditure and working capital requirements for the first (5) five years of operation</h4>
                        <div class="file-upload-area" onclick="document.getElementById('businessIncomeExpenditure').click()">
                          <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                          <p class="text-sm text-gray-600">Upload Capital Expenditure and Working Capital Requirements</p>
                          <p class="text-xs text-gray-500">PDF up to 10MB</p>
                        </div>
                        <input type="file" id="businessIncomeExpenditure" name="businessIncomeExpenditure" accept=".pdf" class="hidden" required>
                        <div id="businessIncomeExpenditureList" class="mt-2"></div>
                      </div>

                      <!-- 2. Financial Performance -->
                      <div>
                        <h4 class="text-md font-medium text-gray-800 mb-3">2. Projected financial performance and position</h4>
                        <div class="file-upload-area" onclick="document.getElementById('businessPerfomance').click()">
                          <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                          <p class="text-sm text-gray-600">Upload Projected Financial Performance and Position</p>
                          <p class="text-xs text-gray-500">PDF up to 10MB</p>
                        </div>
                        <input type="file" id="businessPerfomance" name="businessPerfomance" accept=".pdf" class="hidden" required>
                        <div id="businessPerfomanceList" class="mt-2"></div>
                      </div>

                      <!-- 3. Volume of Business -->
                      <div>
                        <h4 class="text-md font-medium text-gray-800 mb-3">3. Projected volume of business, indicative prices for the services and market share for the first five (5) years of operation</h4>
                        <textarea id="businessVolumePrices" name="businessVolumePrices" rows="6" class="custom-input" required placeholder="Provide detailed projections of business volume, pricing strategy, and expected market share for the first 5 years of operation"></textarea>
                      </div>

                      <!-- 4. Investment Appraisal -->
                      <div>
                        <h4 class="text-md font-medium text-gray-800 mb-3">4. Detailed investment appraisal (financial and technical feasibility)</h4>
                        <div class="file-upload-area" onclick="document.getElementById('businessAppraisal').click()">
                          <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                          <p class="text-sm text-gray-600">Upload Investment Appraisal</p>
                          <p class="text-xs text-gray-500">PDF up to 10MB</p>
                        </div>
                        <input type="file" id="businessAppraisal" name="businessAppraisal" accept=".pdf" class="hidden" required>
                        <div id="businessAppraisalList" class="mt-2"></div>
                      </div>

                      <!-- 5. Range of Services -->
                      <div>
                        <h4 class="text-md font-medium text-gray-800 mb-3">5. Range of services to be provided and the components of the services</h4>
                        <textarea id="businessServices" name="businessServices" rows="6" class="custom-input" required placeholder="Describe in detail the range of services you plan to provide and the components of each service"></textarea>
                      </div>

                      <!-- 6. Market Assessment -->
                      <div>
                        <h4 class="text-md font-medium text-gray-800 mb-3">6. Detailed Market Assessment</h4>
                        <div class="file-upload-area" onclick="document.getElementById('businessMarketAssessment').click()">
                          <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                          <p class="text-sm text-gray-600">Upload Market Assessment</p>
                          <p class="text-xs text-gray-500">PDF up to 10MB</p>
                        </div>
                        <input type="file" id="businessMarketAssessment" name="businessMarketAssessment" accept=".pdf" class="hidden" required>
                        <div id="businessMarketAssessmentList" class="mt-2"></div>
                      </div>

                      <!-- 7. Proof of Financial Capacity -->
                      <div>
                        <h4 class="text-md font-medium text-gray-800 mb-3">7. Attach proof of financial capacity</h4>
                        <div class="file-upload-area" onclick="document.getElementById('businessProofFinancial').click()">
                          <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                          <p class="text-sm text-gray-600">Upload Proof of Financial Capacity</p>
                          <p class="text-xs text-gray-500">PDF up to 10MB</p>
                        </div>
                        <input type="file" id="businessProofFinancial" name="businessProofFinancial" accept=".pdf" class="hidden" required>
                        <div id="businessProofFinancialList" class="mt-2"></div>
                      </div>
                    </div>

                    <!-- Past Financial Capacity Section -->
                    <div class="mt-12">
                      <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Past Financial Capacity</h3>

                      <div class="mb-6">
                        <label for="businessYears" class="custom-form-label">Number of years in operation *</label>
                        <select id="businessYears" name="businessYears" class="custom-input" required onchange="toggleBusinessYearsDocuments()">
                          <option value="">Select Years in Operation</option>
                          <option value="0-3">0-3 years</option>
                          <option value="3+">3+ years</option>
                        </select>
                      </div>

                      <!-- For 3+ years -->
                      <div id="businessYears3Plus" style="display: none;">
                        <h4 class="text-md font-medium text-gray-800 mb-3">For established businesses (3+ years):</h4>
                        <div>
                          <label class="custom-form-label">The latest copies of detailed audited accounts for the last three (3) financial years *</label>
                          <div class="file-upload-area" onclick="document.getElementById('businessAudit3Plus').click()">
                            <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                            <p class="text-sm text-gray-600">Upload Audited Accounts (Last 3 Years)</p>
                            <p class="text-xs text-gray-500">PDF up to 10MB</p>
                          </div>
                          <input type="file" id="businessAudit3Plus" name="businessAudit" accept=".pdf" class="hidden">
                          <div id="businessAudit3PlusList" class="mt-2"></div>
                        </div>
                      </div>

                      <!-- For 0-3 years -->
                      <div id="businessYears0to3" style="display: none;">
                        <h4 class="text-md font-medium text-gray-800 mb-3">For newly formed business (0-3 years):</h4>
                        <p class="text-sm text-gray-600 mb-4">Please combine the following into a single PDF (up to 5MB):</p>
                        <ul class="text-sm text-gray-600 mb-4 list-disc list-inside space-y-1">
                          <li>Shareholders'/owners' detailed audited financial accounts (for shareholders that are incorporated)</li>
                          <li>Detailed bank statements (for owners that are not incorporated)</li>
                          <li>Bankers' available credit facility</li>
                          <li>For individual shareholders, documentary evidence of assets and liabilities</li>
                          <li>Authorized and paid-up capital and relevant certificates confirming the same</li>
                        </ul>
                        <div>
                          <label class="custom-form-label">Combined Financial Documents *</label>
                          <div class="file-upload-area" onclick="document.getElementById('businessAudit0to3').click()">
                            <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                            <p class="text-sm text-gray-600">Upload Combined Financial Documents</p>
                            <p class="text-xs text-gray-500">PDF up to 5MB</p>
                          </div>
                          <input type="file" id="businessAudit0to3" name="businessAudit" accept=".pdf" class="hidden">
                          <div id="businessAudit0to3List" class="mt-2"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Step 4: Technical Capacity -->
                <div id="step4" class="step-content">
                  <div class="form-section">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Technical Capacity</h3>

                    <div class="space-y-8">
                      <!-- Company Experience and Expertise -->
                      <div>
                        <label class="custom-form-label">Provide full particulars of the company's experience and expertise, including those of partners, suppliers, contractors and providers of technical support *</label>
                        <p class="text-sm text-gray-600 mb-3">Combine all documents in one PDF</p>
                        <div class="file-upload-area" onclick="document.getElementById('technicalExpertise').click()">
                          <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                          <p class="text-sm text-gray-600">Upload Company Experience and Expertise</p>
                          <p class="text-xs text-gray-500">PDF up to 10MB</p>
                        </div>
                        <input type="file" id="technicalExpertise" name="technicalExpertise" accept=".pdf" class="hidden" required>
                        <div id="technicalExpertiseList" class="mt-2"></div>
                      </div>

                      <!-- Technical and Service Rollout Plan -->
                      <div>
                        <label class="custom-form-label">Technical and service rollout plan for the next five (5) years *</label>
                        <div class="file-upload-area" onclick="document.getElementById('technicalRollout').click()">
                          <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                          <p class="text-sm text-gray-600">Upload Technical and Service Rollout Plan</p>
                          <p class="text-xs text-gray-500">PDF up to 10MB</p>
                        </div>
                        <input type="file" id="technicalRollout" name="technicalRollout" accept=".pdf" class="hidden" required>
                        <div id="technicalRolloutList" class="mt-2"></div>
                      </div>

                      <!-- Technical Personnel -->
                      <div>
                        <label class="custom-form-label">Proposed Technical Personnel and their resumes *</label>
                        <div class="file-upload-area" onclick="document.getElementById('technicalPersonnel').click()">
                          <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                          <p class="text-sm text-gray-600">Upload Technical Personnel Resumes</p>
                          <p class="text-xs text-gray-500">PDF up to 10MB</p>
                        </div>
                        <input type="file" id="technicalPersonnel" name="technicalPersonnel" accept=".pdf" class="hidden" required>
                        <div id="technicalPersonnelList" class="mt-2"></div>
                      </div>

                      <!-- Network Layout -->
                      <div>
                        <label class="custom-form-label">Proposed Network layout i.e. business sites or transmission sites *</label>
                        <div class="file-upload-area" onclick="document.getElementById('technicalNetworkLayout').click()">
                          <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                          <p class="text-sm text-gray-600">Upload Network Layout</p>
                          <p class="text-xs text-gray-500">PDF up to 10MB</p>
                        </div>
                        <input type="file" id="technicalNetworkLayout" name="technicalNetworkLayout" accept=".pdf" class="hidden" required>
                        <div id="technicalNetworkLayoutList" class="mt-2"></div>
                      </div>

                      <!-- Implementation Schedule -->
                      <div>
                        <label class="custom-form-label">Implementation schedule and growth plan *</label>
                        <div class="file-upload-area" onclick="document.getElementById('technicalImplementation').click()">
                          <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                          <p class="text-sm text-gray-600">Upload Implementation Schedule and Growth Plan</p>
                          <p class="text-xs text-gray-500">PDF up to 10MB</p>
                        </div>
                        <input type="file" id="technicalImplementation" name="technicalImplementation" accept=".pdf" class="hidden" required>
                        <div id="technicalImplementationList" class="mt-2"></div>
                      </div>

                      <!-- Network Diagram -->
                      <div>
                        <label class="custom-form-label">Detailed information on network diagram/architecture *</label>
                        <div class="file-upload-area" onclick="document.getElementById('technicalNetwork').click()">
                          <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                          <p class="text-sm text-gray-600">Upload Network Diagram/Architecture</p>
                          <p class="text-xs text-gray-500">PDF up to 10MB</p>
                        </div>
                        <input type="file" id="technicalNetwork" name="technicalNetwork" accept=".pdf" class="hidden" required>
                        <div id="technicalNetworkList" class="mt-2"></div>
                      </div>

                      <!-- Disaster Recovery Plan -->
                      <div>
                        <label class="custom-form-label">Disaster recovery plan *</label>
                        <div class="file-upload-area" onclick="document.getElementById('technicalRecovery').click()">
                          <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                          <p class="text-sm text-gray-600">Upload Disaster Recovery Plan</p>
                          <p class="text-xs text-gray-500">PDF up to 10MB</p>
                        </div>
                        <input type="file" id="technicalRecovery" name="technicalRecovery" accept=".pdf" class="hidden" required>
                        <div id="technicalRecoveryList" class="mt-2"></div>
                      </div>

                      <!-- Resource Requirements -->
                      <div>
                        <label class="custom-form-label">Resource requirements (numbering, spectrum) *</label>
                        <div class="file-upload-area" onclick="document.getElementById('technicalResources').click()">
                          <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                          <p class="text-sm text-gray-600">Upload Resource Requirements</p>
                          <p class="text-xs text-gray-500">PDF up to 10MB</p>
                        </div>
                        <input type="file" id="technicalResources" name="technicalResources" accept=".pdf" class="hidden" required>
                        <div id="technicalResourcesList" class="mt-2"></div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Step 5: Evaluation Criteria -->
                <div id="step5" class="step-content">
                  <div class="form-section">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Evaluation Criteria</h3>

                    <div class="mb-8">
                      <h4 class="text-md font-medium text-gray-800 mb-4">License Application Evaluation Criteria</h4>

                      <div class="overflow-x-auto">
                        <table class="evaluation-table">
                          <thead>
                            <tr>
                              <th class="text-left">Evaluation Category</th>
                              <th class="text-center">Weight (%)</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr>
                              <td>Financial Capacity</td>
                              <td class="text-center">20%</td>
                            </tr>
                            <tr>
                              <td>Business Plan</td>
                              <td class="text-center">20%</td>
                            </tr>
                            <tr>
                              <td>Technical and Operational Capacity</td>
                              <td class="text-center">40%</td>
                            </tr>
                            <tr>
                              <td>Organization Set Up</td>
                              <td class="text-center">10%</td>
                            </tr>
                            <tr>
                              <td>Socio-economic Impact</td>
                              <td class="text-center">10%</td>
                            </tr>
                            <tr class="bg-gray-50 font-medium">
                              <td>Total</td>
                              <td class="text-center">100%</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>

                    <div class="space-y-4 mb-8">
                      <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                        <p class="text-sm text-gray-800">
                          <strong>Minimum Score:</strong> The minimum score for consideration for licence award shall be 70%
                        </p>
                      </div>

                      <div class="bg-red-50 border border-red-200 rounded-md p-4">
                        <p class="text-sm text-gray-800">
                          <strong>Note:</strong> Under organisation set up criteria, the Authority will not grant a licence where the Applicant fails to meet the shareholding requirements in the Communications Act, irrespective of how the Applicant scores in the other assessment categories.
                        </p>
                      </div>
                    </div>

                    <div class="border border-gray-200 rounded-md p-4">
                      <div class="flex items-start">
                        <input type="checkbox" id="agreeEvaluationCriteria" name="agreeEvaluationCriteria" class="enhanced-checkbox mt-1" required>
                        <label for="agreeEvaluationCriteria" class="ml-3 text-sm text-gray-700">
                          I have read and agree to the evaluation criteria outlined above. I understand that my application will be assessed based on these criteria and that the minimum score for consideration is 70%. *
                        </label>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Step 6: Undertaking -->
                <div id="step6" class="step-content">
                  <div class="form-section">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Undertaking</h3>

                    <div class="space-y-6">
                      <!-- I/We Selection -->
                      <div>
                        <label for="iWe" class="custom-form-label">Declaration Type *</label>
                        <select id="iWe" name="iWe" class="custom-input" required onchange="updateUndertakingText()">
                          <option value="">Select Declaration Type</option>
                          <option value="I">I (Individual)</option>
                          <option value="We">We (Organization)</option>
                        </select>
                      </div>

                      <!-- Dynamic Undertaking Text -->
                      <div id="undertakingTextContainer" class="bg-gray-50 border border-gray-200 rounded-md p-6" style="display: none;">
                        <p id="undertakingText" class="text-sm text-gray-800 leading-relaxed mb-4">
                          <!-- Text will be dynamically updated based on I/We selection -->
                        </p>

                        <div class="border-t border-gray-200 pt-4 mt-4">
                          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <label class="text-sm font-medium text-gray-700">Signed:</label>
                              <input type="text" id="signedName" name="signedName" class="custom-input mt-1" readonly>
                            </div>
                            <div>
                              <label class="text-sm font-medium text-gray-700">Date:</label>
                              <input type="text" id="currentDate" name="currentDate" class="custom-input mt-1" readonly>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- Company Stamp Upload -->
                      <div>
                        <label class="custom-form-label">Company Stamp *</label>
                        <p class="text-sm text-gray-600 mb-3">Upload your company's official rubber stamp</p>
                        <div class="file-upload-area" onclick="document.getElementById('applicantStamp').click()">
                          <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                          <p class="text-sm text-gray-600">Upload Company Stamp</p>
                          <p class="text-xs text-gray-500">JPG, JPEG, PNG up to 5MB</p>
                        </div>
                        <input type="file" id="applicantStamp" name="applicantStamp" accept=".jpg,.jpeg,.png" class="hidden" required>
                        <div id="applicantStampList" class="mt-2"></div>

                        <!-- Stamp Preview -->
                        <div id="stampPreview" class="mt-4" style="display: none;">
                          <h5 class="text-sm font-medium text-gray-700 mb-2">Company Stamp Preview:</h5>
                          <img id="stampImage" src="" alt="Company Stamp" class="max-w-xs max-h-32 border border-gray-300 rounded">
                        </div>
                      </div>

                      <!-- Important Notes -->
                      <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                        <h4 class="text-md font-medium text-blue-900 mb-2">Important Notes:</h4>
                        <ul class="text-sm text-blue-800 space-y-1 list-disc list-inside">
                          <li>All photocopies must be duly certified as true copies of the original.</li>
                          <li>Any attachment to the application Form A shall be initialised by the signatory.</li>
                          <li>Applicant company rubber stamp must be affixed on the last page of the application form.</li>
                        </ul>
                      </div>

                      <!-- Final Compliance Confirmation -->
                      <div class="border border-gray-200 rounded-md p-4">
                        <div class="flex items-start">
                          <input type="checkbox" id="confirmCompliance" name="confirmCompliance" class="enhanced-checkbox mt-1" required>
                          <label for="confirmCompliance" class="ml-3 text-sm text-gray-700">
                            I confirm that I have read and will comply with all the requirements stated above, including proper certification of documents, initialization of attachments, and affixing of company stamp. *
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Navigation Buttons -->
                <div class="flex justify-between pt-8 border-t border-gray-200">
                  <button type="button" id="prevBtn" onclick="changeStep(-1)" class="secondary-main-button" style="display: none;">
                    <i class="ri-arrow-left-line mr-2"></i>
                    Previous
                  </button>
                  <div class="flex space-x-4">
                    <button type="button" onclick="saveDraft()" class="secondary-main-button">
                      <i class="ri-save-line mr-2"></i>
                      Save Draft
                    </button>
                    <button type="button" id="nextBtn" onclick="changeStep(1)" class="main-button">
                      Next
                      <i class="ri-arrow-right-line ml-2"></i>
                    </button>
                    <button type="submit" id="submitBtn" class="main-button" style="display: none;">
                      <i class="ri-send-plane-line mr-2"></i>
                      Submit Application
                    </button>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>

  <script>
    let currentStep = 1;
    const totalSteps = 6;

    // Initialize the form
    document.addEventListener('DOMContentLoaded', function() {
      updateProgressBar();
      updateStepIndicators();
      updateNavigationButtons();
      setCurrentDate();
      loadDraftData();
    });

    // Step navigation functions
    function changeStep(direction) {
      if (direction === 1 && !validateCurrentStep()) {
        return;
      }

      const currentStepElement = document.getElementById(`step${currentStep}`);
      currentStepElement.classList.remove('active');

      currentStep += direction;

      if (currentStep < 1) currentStep = 1;
      if (currentStep > totalSteps) currentStep = totalSteps;

      const newStepElement = document.getElementById(`step${currentStep}`);
      newStepElement.classList.add('active');

      updateProgressBar();
      updateStepIndicators();
      updateNavigationButtons();
      saveDraftData();
    }

    function updateProgressBar() {
      const progress = ((currentStep - 1) / (totalSteps - 1)) * 100;
      document.getElementById('progressFill').style.width = `${progress}%`;
      document.getElementById('progressPercentage').textContent = `${Math.round(progress)}%`;

      // Update status
      const statusElement = document.getElementById('applicationStatus');
      if (progress === 0) {
        statusElement.textContent = 'Draft';
        statusElement.className = 'tracking-status status-draft';
      } else if (progress < 50) {
        statusElement.textContent = 'In Progress';
        statusElement.className = 'tracking-status status-submitted';
      } else if (progress < 100) {
        statusElement.textContent = 'Nearly Complete';
        statusElement.className = 'tracking-status status-review';
      } else {
        statusElement.textContent = 'Ready for Review';
        statusElement.className = 'tracking-status status-approved';
      }
    }

    function updateStepIndicators() {
      for (let i = 1; i <= totalSteps; i++) {
        const indicator = document.getElementById(`step${i}Indicator`);
        if (i < currentStep) {
          indicator.className = 'step-indicator completed';
        } else if (i === currentStep) {
          indicator.className = 'step-indicator active';
        } else {
          indicator.className = 'step-indicator inactive';
        }
      }
    }

    function updateNavigationButtons() {
      const prevBtn = document.getElementById('prevBtn');
      const nextBtn = document.getElementById('nextBtn');
      const submitBtn = document.getElementById('submitBtn');

      prevBtn.style.display = currentStep === 1 ? 'none' : 'inline-flex';

      if (currentStep === totalSteps) {
        nextBtn.style.display = 'none';
        submitBtn.style.display = 'inline-flex';
      } else {
        nextBtn.style.display = 'inline-flex';
        submitBtn.style.display = 'none';
      }
    }

    // Validation functions
    function validateCurrentStep() {
      const currentStepElement = document.getElementById(`step${currentStep}`);
      const requiredFields = currentStepElement.querySelectorAll('[required]');
      let isValid = true;

      requiredFields.forEach(field => {
        if (field.type === 'checkbox') {
          if (!field.checked) {
            field.classList.add('input-error');
            isValid = false;
          } else {
            field.classList.remove('input-error');
          }
        } else if (!field.value.trim()) {
          field.classList.add('input-error');
          isValid = false;
        } else {
          field.classList.remove('input-error');

          // Special validations
          if (field.name === 'applicantPostal' || field.name === 'applicantPhysical') {
            if (!validatePostalAddress(field.value)) {
              field.classList.add('input-error');
              isValid = false;
            }
          }

          if (field.name === 'applicantTPIN') {
            if (!/^\d+$/.test(field.value)) {
              field.classList.add('input-error');
              isValid = false;
            }
          }
        }
      });

      if (!isValid) {
        alert('Please fill in all required fields correctly before proceeding.');
      }

      return isValid;
    }

    function validatePostalAddress(address) {
      const patterns = ['P.O. Box', 'P/Bag', 'Private Bag', 'PO Box'];
      return patterns.some(pattern => address.toLowerCase().includes(pattern.toLowerCase()));
    }

    // License type toggle
    function toggleLicenseType() {
      const licenceCategory = document.getElementById('licenceCategory').value;
      const licenceType = document.getElementById('licenceType');

      if (licenceCategory) {
        licenceType.disabled = false;
        licenceType.required = true;
      } else {
        licenceType.disabled = true;
        licenceType.required = false;
        licenceType.value = '';
      }
    }

    // Business years toggle
    function toggleBusinessYearsDocuments() {
      const businessYears = document.getElementById('businessYears').value;
      const years3Plus = document.getElementById('businessYears3Plus');
      const years0to3 = document.getElementById('businessYears0to3');
      const audit3Plus = document.getElementById('businessAudit3Plus');
      const audit0to3 = document.getElementById('businessAudit0to3');

      if (businessYears === '3+') {
        years3Plus.style.display = 'block';
        years0to3.style.display = 'none';
        audit3Plus.required = true;
        audit0to3.required = false;
      } else if (businessYears === '0-3') {
        years3Plus.style.display = 'none';
        years0to3.style.display = 'block';
        audit3Plus.required = false;
        audit0to3.required = true;
      } else {
        years3Plus.style.display = 'none';
        years0to3.style.display = 'none';
        audit3Plus.required = false;
        audit0to3.required = false;
      }
    }

    // Generate management team forms dynamically
    function generateManagementTeamForms() {
      const count = parseInt(document.getElementById('managementTeamCount').value) || 0;
      const container = document.getElementById('managementTeamFormsContainer');

      container.innerHTML = '';

      for (let i = 1; i <= count; i++) {
        const managementForm = document.createElement('div');
        managementForm.className = 'border border-gray-200 p-4 rounded-lg';
        managementForm.innerHTML = `
          <h5 class="text-md font-medium text-gray-900 mb-3">Management Team Member ${i}</h5>
          <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <!-- Full Name -->
            <div class="sm:col-span-2">
              <label for="mgtName${i}" class="custom-form-label">Full name *</label>
              <input type="text" name="mgtName${i}" id="mgtName${i}" class="custom-input" required>
            </div>

            <!-- Contact Details Section -->
            <div class="sm:col-span-2">
              <h6 class="text-sm font-medium text-gray-800 mb-2">Contact Details</h6>
            </div>

            <!-- Email Address -->
            <div>
              <label for="mgtEmail${i}" class="custom-form-label">Email Address *</label>
              <input type="email" name="mgtEmail${i}" id="mgtEmail${i}" class="custom-input" required>
            </div>

            <!-- Cell Phone -->
            <div>
              <label for="mgtPhone${i}" class="custom-form-label">Cell Phone *</label>
              <input type="tel" name="mgtPhone${i}" id="mgtPhone${i}" class="custom-input" required pattern="[0-9]{10}" placeholder="0123456789">
              <p class="text-xs text-gray-500 mt-1">Enter 10-digit phone number</p>
            </div>

            <!-- Physical Address -->
            <div class="sm:col-span-2">
              <label for="mgtAddress${i}" class="custom-form-label">Physical Address *</label>
              <textarea name="mgtAddress${i}" id="mgtAddress${i}" rows="3" class="custom-input" required placeholder="Provide complete physical address"></textarea>
            </div>
          </div>
        `;
        container.appendChild(managementForm);
      }
    }

    // Undertaking text update
    function updateUndertakingText() {
      const iWe = document.getElementById('iWe').value;
      const applicantName = document.getElementById('applicantName').value || '[Applicant Name]';
      const container = document.getElementById('undertakingTextContainer');
      const textElement = document.getElementById('undertakingText');
      const signedName = document.getElementById('signedName');

      if (iWe) {
        const pronouns = iWe === 'I' ? { subject: 'I', possessive: 'my', object: 'me' } : { subject: 'We', possessive: 'our', object: 'us' };

        textElement.innerHTML = `${pronouns.subject}, ${applicantName} hereby declare that the information supplied above is true and correct to the best of ${pronouns.possessive} knowledge and belief. ${pronouns.subject} undertake that upon grant of the license, ${pronouns.subject} shall abide by the Terms and the Conditions upon which the license is granted. ${pronouns.subject} accept that the license may be revoked if it is established that the license was granted based on information found to be false or untrue or misleading.`;

        signedName.value = applicantName;
        container.style.display = 'block';
      } else {
        container.style.display = 'none';
      }
    }

    // Set current date
    function setCurrentDate() {
      const today = new Date();
      const formattedDate = today.toLocaleDateString('en-GB');
      document.getElementById('currentDate').value = formattedDate;
    }

    // File upload handling
    function handleFileUpload(input) {
      const file = input.files[0];
      if (file) {
        const listContainer = document.getElementById(input.id + 'List');
        listContainer.innerHTML = `
          <div class="flex items-center justify-between bg-gray-50 p-2 rounded">
            <span class="text-sm text-gray-700">${file.name}</span>
            <span class="text-xs text-gray-500">${(file.size / 1024 / 1024).toFixed(2)} MB</span>
          </div>
        `;

        // Handle stamp preview
        if (input.id === 'applicantStamp') {
          const reader = new FileReader();
          reader.onload = function(e) {
            document.getElementById('stampImage').src = e.target.result;
            document.getElementById('stampPreview').style.display = 'block';
          };
          reader.readAsDataURL(file);
        }
      }
    }

    // Add event listeners for file inputs
    document.addEventListener('DOMContentLoaded', function() {
      const fileInputs = document.querySelectorAll('input[type="file"]');
      fileInputs.forEach(input => {
        input.addEventListener('change', function() {
          handleFileUpload(this);
        });
      });
    });

    // Auto-update undertaking text when applicant name changes
    document.addEventListener('DOMContentLoaded', function() {
      const applicantNameInput = document.getElementById('applicantName');
      if (applicantNameInput) {
        applicantNameInput.addEventListener('input', updateUndertakingText);
      }
    });

    // Draft saving and loading
    function saveDraftData() {
      const formData = new FormData(document.getElementById('licenseApplicationForm'));
      const data = {};
      for (let [key, value] of formData.entries()) {
        data[key] = value;
      }
      localStorage.setItem('licenseApplicationDraft', JSON.stringify(data));
    }

    function loadDraftData() {
      const savedData = localStorage.getItem('licenseApplicationDraft');
      if (savedData) {
        const data = JSON.parse(savedData);
        Object.keys(data).forEach(key => {
          const element = document.getElementById(key) || document.querySelector(`[name="${key}"]`);
          if (element && element.type !== 'file') {
            element.value = data[key];
          }
        });
      }
    }

    function saveDraft() {
      saveDraftData();
      alert('Draft saved successfully!');
    }

    // Mobile sidebar toggle
    function toggleMobileSidebar() {
      const sidebar = document.getElementById('sidebar');
      const overlay = document.getElementById('mobileSidebarOverlay');

      sidebar.classList.toggle('mobile-sidebar-open');
      overlay.classList.toggle('show');
    }

    // Dropdown toggle
    function toggleDropdown() {
      const dropdown = document.getElementById('userDropdown');
      dropdown.classList.toggle('show');
    }

    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
      const dropdown = document.getElementById('userDropdown');
      const button = event.target.closest('[onclick="toggleDropdown()"]');

      if (!button && !dropdown.contains(event.target)) {
        dropdown.classList.remove('show');
      }
    });

    // Close mobile sidebar when clicking overlay
    document.getElementById('mobileSidebarOverlay').addEventListener('click', function() {
      toggleMobileSidebar();
    });

    // Form submission
    document.getElementById('licenseApplicationForm').addEventListener('submit', function(e) {
      e.preventDefault();

      if (validateCurrentStep()) {
        // Here you would normally submit the form to your server
        alert('Application submitted successfully! You will receive a confirmation email shortly.');

        // Clear draft data
        localStorage.removeItem('licenseApplicationDraft');

        // Redirect to applications page
        window.location.href = 'my-licenses.html';
      }
    });
  </script>

</body>
</html>