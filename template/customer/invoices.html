<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>New License Application - Customer Portal</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: { primary: "#e02b20", secondary: "#6366f1" },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"
    />
    <style>
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
        font-family: 'Inter', sans-serif;
        background-color: #f9fafb;
      }

      /* Enhanced form styles */
      .enhanced-input {
        @apply appearance-none block w-full px-4 py-3 border-2 border-gray-300 rounded-md shadow-sm
        placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary
        sm:text-sm bg-gray-50 hover:bg-white transition-colors;
      }

      .enhanced-select {
        @apply block w-full px-4 py-3 border-2 border-gray-300 rounded-md shadow-sm
        focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary
        sm:text-sm bg-gray-50 hover:bg-white transition-colors;
      }

      .enhanced-checkbox {
        @apply h-5 w-5 text-primary focus:ring-2 focus:ring-primary border-2 border-gray-300 rounded;
      }

      .enhanced-button {
        @apply flex justify-center py-3 px-4 border-2 border-transparent rounded-md shadow-md
        text-sm font-medium text-white bg-primary hover:bg-opacity-90 focus:outline-none
        focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all;
      }

      .dropdown-content {
        display: none;
        position: absolute;
        right: 0;
        top: 100%;
        z-index: 50;
      }
      .dropdown-content.show {
        display: block;
      }

      .side-nav{
        overflow: auto;
        -ms-overflow-style: none;
        height: 75vh;
      }
      .side-nav::-webkit-scrollbar {
        display: none;
      }
      /* Hide scrollbar for Firefox */
      .side-nav {
        scrollbar-width: none;
      }

      /* Mobile sidebar styles */
      @media (max-width: 768px) {
        .mobile-sidebar-open {
          display: block !important;
          position: fixed;
          z-index: 50;
          height: 100vh;
          box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
        }

        .mobile-sidebar-overlay {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.5);
          z-index: 40;
          display: none;
        }

        .mobile-sidebar-overlay.show {
          display: block;
        }
      }
    </style>
  </head>
  <body>
    <div class="flex h-screen overflow-hidden">
      <!-- Mobile sidebar overlay -->
      <div id="mobileSidebarOverlay" class="mobile-sidebar-overlay"></div>

      <!-- Sidebar -->
      <aside id="sidebar" class="w-64 bg-white shadow-lg flex-shrink-0 hidden md:block">
        <div class="h-16 flex items-center px-6 border-b">
          <div class="flex items-center">
            <img src="../images/macra-logo.png" alt="Logo" class="max-h-12 w-auto">
          </div>
        </div>
        <nav class="mt-6 px-4 side-nav">
          <div class="space-y-1">
            <a
              href="index.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-dashboard-line"></i>
              </div>
              Dashboard
            </a>
            <a
              href="my-licenses.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-key-line"></i>
              </div>
              My Licenses
            </a>
            <a
              href="new-application.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-file-list-3-line"></i>
              </div>
              New Application
            </a>
            <a
              href="payments.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md bg-primary bg-opacity-10 text-primary"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z" />
                </svg>
              </div>
              Payments
            </a>
            <a
              href="documents.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-file-text-line"></i>
              </div>
              Documents
            </a>
          </div>

          <div class="mt-8">
            <h3
              class="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider"
            >
              Support
            </h3>
            <div class="mt-2 space-y-1">
              <a
                href="help-center.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-question-line"></i>
                </div>
                Help Center
              </a>
              <a
                href="contact-support.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-customer-service-2-line"></i>
                </div>
                Contact Support
              </a>
            </div>
          </div>
        </nav>
        <div class="absolute bottom-0 w-64 p-4 border-t">
          <a href="profile.html" class="flex items-center hover:bg-gray-50 p-2 rounded-md">
            <img
              class="h-10 w-10 rounded-full"
              src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp"
              alt="Profile"
            />
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-900">John Smith</p>
              <p class="text-xs text-gray-500">Acme Corporation</p>
            </div>
          </a>
        </div>
      </aside>
      <!-- Main content -->
      <div class="flex-1 flex flex-col overflow-hidden">
        <!-- Top header -->
        <header class="bg-white shadow-sm z-10">
          <div class="flex items-center justify-between h-16 px-4 sm:px-6">
            <button
              id="mobileMenuBtn"
              type="button"
              onclick="toggleMobileSidebar()"
              class="md:hidden text-gray-500 hover:text-gray-700 focus:outline-none"
            >
              <div class="w-6 h-6 flex items-center justify-center">
                <i class="ri-menu-line ri-lg"></i>
              </div>
            </button>
            <div class="flex-1 flex items-center justify-between">
              <div class="flex-1 flex justify-center px-2 lg:ml-6 lg:justify-start">
                <h1 class="text-xl font-medium text-gray-900">New License Application</h1>
              </div>
              <div class="flex items-center">
                <button
                  type="button"
                  class="flex-shrink-0 p-1 mr-4 text-gray-500 rounded-full hover:text-gray-700 focus:outline-none relative"
                >
                  <span class="sr-only">View notifications</span>
                  <div class="w-6 h-6 flex items-center justify-center">
                    <i class="ri-notification-3-line ri-lg"></i>
                  </div>
                  <span
                    class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white"
                  ></span>
                </button>
                <div class="dropdown relative">
                  <button
                    type="button"
                    onclick="toggleDropdown()"
                    class="flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    <span class="sr-only">Open user menu</span>
                    <img
                      class="h-8 w-8 rounded-full"
                      src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp"
                      alt="Profile"
                    />
                  </button>
                  <div
                    id="userDropdown"
                    class="dropdown-content mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5"
                  >
                    <div class="py-1">
                      <a
                        href="profile.html"
                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        >Your Profile</a
                      >
                      <a
                        href="account-settings.html"
                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        >Settings</a
                      >
                      <a
                        href="../auth/login.html"
                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        >Sign out</a
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          
                    <!-- Secondary navigation -->
          <div class="border-t border-gray-200 px-4 sm:px-6">
            <div class="py-3 flex space-x-8">
                  <button id="invoices-tab" onclick="showTab('invoices')" class="tab-button active text-sm px-1 py-2">
                    Invoices
                  </button>
                  <button id="payments-tab" onclick="showTab('payments')" class="tab-button text-sm px-1 py-2 text-gray-500"
                  >
                   Receive Payments
                  </button>

            </div>
          </div>
        </header>



              <!-- Main content area -->
        <main class="flex-1 overflow-y-auto bg-gray-50 p-4 sm:p-6">





            <!-- Invoices Tab Content -->
          <div id="invoices-content" class="tab-content">
            <div class="mb-6">
              <div class="flex items-center justify-between">
                <h1 class="text-2xl font-semibold text-gray-900">Invoices</h1>
                <div>
                  <div class="flex space-x-2">
                    <button
                      type="button"
                      onclick="openAddUserModal()"
                      class="inline-flex items-center px-4 py-3 border-2 border-transparent shadow-md text-sm font-medium rounded-md text-white bg-primary hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all"
                    >
                      <div class="w-5 h-5 flex items-center justify-center mr-2">
                        <i class="ri-file-list-line"></i>
                      </div>
                     Create Invoice

                    </button>
                    <a
                      href="invoice-list.html"
                      class="inline-flex items-center px-4 py-3 border-2 border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all"
                    >
                      <div class="w-5 h-5 flex items-center justify-center mr-2">
                        <i class="ri-upload-2-line"></i>
                      </div>
                      Export Invoice
                    </a>

                  </div>
                </div>
              </div>
              <p class="mt-1 text-sm text-gray-500">
                Manage invoices, payments, and financial transactions.
              </p>
            </div>

              <!-- Filters -->
              <div class="bg-white shadow overflow-hidden sm:rounded-md mb-6">
                <div class="px-4 py-5 sm:p-6">
                  <div class="grid grid-cols-1 gap-y-4 sm:grid-cols-4 sm:gap-x-6">
                    <div>
                      <label for="invoice-status" class="block text-sm font-medium text-gray-700">Status</label>
                      <select id="invoice-status" name="invoice-status" class="enhanced-select mt-1">
                        <option value="">All Statuses</option>
                        <option value="paid">Paid</option>
                        <option value="pending">Pending</option>
                        <option value="overdue">Overdue</option>
                      </select>
                    </div>
                    <div>
                      <label for="invoice-date" class="block text-sm font-medium text-gray-700">Date Range</label>
                      <select id="invoice-date" name="invoice-date" class="enhanced-select mt-1">
                        <option value="">All Time</option>
                        <option value="last-30">Last 30 Days</option>
                        <option value="last-90">Last 90 Days</option>
                        <option value="last-year">Last Year</option>
                      </select>
                    </div>
                    <div>
                      <label for="invoice-amount" class="block text-sm font-medium text-gray-700">Amount</label>
                      <select id="invoice-amount" name="invoice-amount" class="enhanced-select mt-1">
                        <option value="">All Amounts</option>
                        <option value="0-1000">MK0 - MK500,000</option>
                        <option value="1000-5000">MK70,000 - MK955,000</option>
                        <option value="50000+">MK75,000+</option>
                      </select>
                    </div>
                    <div class="flex items-end">
                      <button type="button" class="inline-flex items-center px-4 py-3 border-2 border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all">
                        Apply Filters
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Invoices Table -->
              <div class="bg-white shadow overflow-hidden sm:rounded-md">
                <div class="px-4 py-5 sm:p-6">
                  <div class="flex flex-col">
                    <div class="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                      <div class="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
                        <div class="overflow-hidden border border-gray-200 sm:rounded-lg">
                          <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                              <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Invoice #
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Resource
                                    </th>

                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Issue Date
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Due Date
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Amount
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Status
                                </th>
                                <th scope="col" class="relative px-6 py-3">
                                  <span class="sr-only">Actions</span>
                                </th>
                              </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                              <!-- Sample invoice rows -->
                              <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm font-medium text-gray-900">
                                    INV-2023-001
                                  </div>
                                </td>
           
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm text-gray-900">Radio License</div>
                                </td>
                                      <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm text-gray-900">2023-11-15</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm text-gray-900">2023-10-31</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm text-gray-900">MK2,500.00</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                    Paid
                                  </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                  <a href="#" class="text-primary hover:text-primary mr-3">View</a>
                                  <a href="#" class="text-primary hover:text-primary">Download</a>
                                </td>
                              </tr>
                              <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm font-medium text-gray-900">
                                    INV-2023-002
                                  </div>
                                </td>
    
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm text-gray-900">TV License</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm text-gray-900">2023-11-15</div>
                                </td>
                                      <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm text-gray-900">2023-11-15</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm text-gray-900">MK3,750.00</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                    Pending
                                  </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                  <a href="#" class="text-primary hover:text-primary mr-3">View</a>
                                  <a href="#" class="text-primary hover:text-primary">Download</a>
                                </td>
                              </tr>
                    
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- Pagination -->
                <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                  <div class="flex-1 flex justify-between sm:hidden">
                    <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                      Previous
                    </a>
                    <a href="#" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                      Next
                    </a>
                  </div>
                  <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                      <p class="text-sm text-gray-700">
                        Showing <span class="font-medium">1</span> to <span class="font-medium">3</span> of <span class="font-medium">12</span> invoices
                      </p>
                    </div>
                    <div>
                      <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                          <span class="sr-only">Previous</span>
                          <i class="ri-arrow-left-s-line"></i>
                        </a>
                        <a href="#" aria-current="page" class="z-10 bg-primary bg-opacity-10 border-primary text-primary relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                          1
                        </a>
                        <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                          2
                        </a>
                        <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                          3
                        </a>
                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                          <span class="sr-only">Next</span>
                          <i class="ri-arrow-right-s-line"></i>
                        </a>
                      </nav>
                    </div>
                  </div>
                </div>
              </div>
            </div>










            <!-- Payments Tab Content -->
            <div id="payments-content" class="tab-content hidden">
              <div class="mb-6">
              <div class="flex items-center justify-between">
                <h1 class="text-2xl font-semibold text-gray-900">Payments</h1>
                <div>
                  <div class="flex space-x-2">
                    <button
                      type="button"
                      onclick="openAddUserModal()"
                      class="inline-flex items-center px-4 py-3 border-2 border-transparent shadow-md text-sm font-medium rounded-md text-white bg-primary hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all"
                    >
                      <div class="w-5 h-5 flex items-center justify-center mr-2">
                        <i class="ri-money-dollar-circle-line"></i>
                      </div>
                  Receive Payment

                    </button>
                    <a
                      href="payment-history.html"
                      class="inline-flex items-center px-4 py-3 border-2 border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all"
                    >
                      <div class="w-5 h-5 flex items-center justify-center mr-2">
                        <i class="ri-upload-2-line"></i>
                      </div>
                      Export
                    </a>

                  </div>
                </div>
              </div>
              <p class="mt-1 text-sm text-gray-500">
                Record and manage incoming payments from clients.
              </p>
              </div>

              <!-- Filters -->
              <div class="bg-white shadow overflow-hidden sm:rounded-md mb-6">
                <div class="px-4 py-5 sm:p-6">
                  <div class="grid grid-cols-1 gap-y-4 sm:grid-cols-4 sm:gap-x-6">
                    <div>
                      <label for="invoice-status" class="block text-sm font-medium text-gray-700">Status</label>
                      <select id="invoice-status" name="invoice-status" class="enhanced-select mt-1">
                        <option value="">All Statuses</option>
                        <option value="paid">Paid</option>
                        <option value="pending">Pending</option>
                        <option value="overdue">Overdue</option>
                      </select>
                    </div>
                    <div>
                      <label for="invoice-date" class="block text-sm font-medium text-gray-700">Date Range</label>
                      <select id="invoice-date" name="invoice-date" class="enhanced-select mt-1">
                        <option value="">All Time</option>
                        <option value="last-30">Last 30 Days</option>
                        <option value="last-90">Last 90 Days</option>
                        <option value="last-year">Last Year</option>
                      </select>
                    </div>
                    <div>
                      <label for="invoice-amount" class="block text-sm font-medium text-gray-700">Amount</label>
                      <select id="invoice-amount" name="invoice-amount" class="enhanced-select mt-1">
                        <option value="">All Amounts</option>
                        <option value="0-1000">MK0 - MK500,000</option>
                        <option value="1000-5000">MK70,000 - MK955,000</option>
                        <option value="50000+">MK75,000+</option>
                      </select>
                    </div>
                    <div class="flex items-end">
                      <button type="button" class="inline-flex items-center px-4 py-3 border-2 border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all">
                        Apply Filters
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Invoices Table -->
              <div class="bg-white shadow overflow-hidden sm:rounded-md">
                <div class="px-4 py-5 sm:p-6">
                  <div class="flex flex-col">
                    <div class="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                      <div class="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
                        <div class="overflow-hidden border border-gray-200 sm:rounded-lg">
                          <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                              <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Invoice #
                                </th>

                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Issue Date
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Issue Date
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Amount
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Status
                                </th>
                                <th scope="col" class="relative px-6 py-3">
                                  <span class="sr-only">Actions</span>
                                </th>
                              </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                              <!-- Sample invoice rows -->
                              <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm font-medium text-gray-900">
                                    INV-2023-001
                                  </div>
                                </td>

                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm text-gray-900">2023-10-01</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm text-gray-900">2023-10-31</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm text-gray-900">MK2,500.00</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                    Paid
                                  </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                  <a href="#" class="text-primary hover:text-primary mr-3">View</a>
                                  <a href="#" class="text-primary hover:text-primary">Download</a>
                                </td>
                              </tr>
                              <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm font-medium text-gray-900">
                                    INV-2023-002
                                  </div>
                                </td>
 
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm text-gray-900">2023-10-15</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm text-gray-900">2023-11-15</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm text-gray-900">MK3,750.00</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                    Pending
                                  </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                  <a href="#" class="text-primary hover:text-primary mr-3">View</a>
                                  <a href="#" class="text-primary hover:text-primary">Download</a>
                                </td>
                              </tr>

                            </tbody>
                          </table>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- Pagination -->
                <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                  <div class="flex-1 flex justify-between sm:hidden">
                    <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                      Previous
                    </a>
                    <a href="#" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                      Next
                    </a>
                  </div>
                  <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                      <p class="text-sm text-gray-700">
                        Showing <span class="font-medium">1</span> to <span class="font-medium">3</span> of <span class="font-medium">12</span> invoices
                      </p>
                    </div>
                    <div>
                      <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                          <span class="sr-only">Previous</span>
                          <i class="ri-arrow-left-s-line"></i>
                        </a>
                        <a href="#" aria-current="page" class="z-10 bg-primary bg-opacity-10 border-primary text-primary relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                          1
                        </a>
                        <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                          2
                        </a>
                        <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                          3
                        </a>
                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                          <span class="sr-only">Next</span>
                          <i class="ri-arrow-right-s-line"></i>
                        </a>
                      </nav>
                    </div>
                  </div>
                </div>
              </div>
            </div>






            <!-- Financial Reports Tab Content -->
            <div id="reports-content" class="tab-content hidden">
              <div class="mb-6">
              <div class="flex items-center justify-between">
                <h1 class="text-2xl font-semibold text-gray-900">Financial Report</h1>
                <div>
                  <div class="flex space-x-2">
                    <button
                      type="button"
                      onclick="openAddUserModal()"
                      class="inline-flex items-center px-4 py-3 border-2 border-transparent shadow-md text-sm font-medium rounded-md text-white bg-primary hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all"
                    >
                      <div class="w-5 h-5 flex items-center justify-center mr-2">
                        <i class="ri-file-chart-line"></i>
                      </div>
                      Generate Report

                    </button>
                    <a
                      href="saved-reports.html"
                      class="inline-flex items-center px-4 py-3 border-2 border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all"
                    >
                      <div class="w-5 h-5 flex items-center justify-center mr-2">
                        <i class="ri-folder-chart-line"></i>
                      </div>
                      Saved Reports
                    </a>

                  </div>
                </div>
              </div>
              <p class="mt-1 text-sm text-gray-500">
                Generate and view financial reports and analytics.
              </p>
            </div>

                          <!-- Filters -->
              <div class="bg-white shadow overflow-hidden sm:rounded-md mb-6">
                <div class="px-4 py-5 sm:p-6">
                  <div class="grid grid-cols-1 gap-y-4 sm:grid-cols-4 sm:gap-x-6">
                    <div>
                      <label for="invoice-status" class="block text-sm font-medium text-gray-700">Status</label>
                      <select id="invoice-status" name="invoice-status" class="enhanced-select mt-1">
                        <option value="">All Statuses</option>
                        <option value="paid">Paid</option>
                        <option value="pending">Pending</option>
                        <option value="overdue">Overdue</option>
                      </select>
                    </div>
                    <div>
                      <label for="invoice-date" class="block text-sm font-medium text-gray-700">Date Range</label>
                      <select id="invoice-date" name="invoice-date" class="enhanced-select mt-1">
                        <option value="">All Time</option>
                        <option value="last-30">Last 30 Days</option>
                        <option value="last-90">Last 90 Days</option>
                        <option value="last-year">Last Year</option>
                      </select>
                    </div>
                    <div>
                      <label for="invoice-amount" class="block text-sm font-medium text-gray-700">Amount</label>
                      <select id="invoice-amount" name="invoice-amount" class="enhanced-select mt-1">
                        <option value="">All Amounts</option>
                        <option value="0-1000">$0 - $1,000</option>
                        <option value="1000-5000">$1,000 - $5,000</option>
                        <option value="5000+">$5,000+</option>
                      </select>
                    </div>
                    <div class="flex items-end">
                      <button type="button" class="inline-flex items-center px-4 py-3 border-2 border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all">
                        Apply Filters
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Invoices Table -->
              <div class="bg-white shadow overflow-hidden sm:rounded-md">
                <div class="px-4 py-5 sm:p-6">
                  <div class="flex flex-col">
                    <div class="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                      <div class="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
                        <div class="overflow-hidden border border-gray-200 sm:rounded-lg">
                          <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                              <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Invoice #
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Client
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Issue Date
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Due Date
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Amount
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Status
                                </th>
                                <th scope="col" class="relative px-6 py-3">
                                  <span class="sr-only">Actions</span>
                                </th>
                              </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                              <!-- Sample invoice rows -->
                              <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm font-medium text-gray-900">
                                    INV-2023-001
                                  </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                    </div>
                                    <div class="ml-4">
                                      <div class="text-sm font-medium text-gray-900">
                                        Acme Corporation
                                      </div>
                                      <div class="text-sm text-gray-500">
                                        <EMAIL>
                                      </div>
                                    </div>
                                  </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm text-gray-900">2023-10-01</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm text-gray-900">2023-10-31</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm text-gray-900">$2,500.00</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                    Paid
                                  </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                  <a href="#" class="text-primary hover:text-primary mr-3">View</a>
                                  <a href="#" class="text-primary hover:text-primary">Download</a>
                                </td>
                              </tr>
                              <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm font-medium text-gray-900">
                                    INV-2023-002
                                  </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm font-medium text-gray-900">
                                  Globex Industries
                                  </div>
                                  <div class="text-sm text-gray-500">
                                  <EMAIL>
                                  </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm text-gray-900">2023-10-15</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm text-gray-900">2023-11-15</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm text-gray-900">$3,750.00</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                    Pending
                                  </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                  <a href="#" class="text-primary hover:text-primary mr-3">View</a>
                                  <a href="#" class="text-primary hover:text-primary">Download</a>
                                </td>
                              </tr>
                              <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm font-medium text-gray-900">
                                    INV-2023-003
                                  </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                      <img class="h-10 w-10 rounded-full" src="https://images.unsplash.com/photo-1520813792240-56fc4a3765a7?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="">
                                    </div>
                                    <div class="ml-4">
                                      <div class="text-sm font-medium text-gray-900">
                                        Oceanic Airlines
                                      </div>
                                      <div class="text-sm text-gray-500">
                                        <EMAIL>
                                      </div>
                                    </div>
                                  </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm text-gray-900">2023-09-15</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm text-gray-900">2023-10-15</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm text-gray-900">$1,850.00</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                    Overdue
                                  </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                  <a href="#" class="text-primary hover:text-primary mr-3">View</a>
                                  <a href="#" class="text-primary hover:text-primary">Download</a>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- Pagination -->
                <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                  <div class="flex-1 flex justify-between sm:hidden">
                    <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                      Previous
                    </a>
                    <a href="#" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                      Next
                    </a>
                  </div>
                  <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                      <p class="text-sm text-gray-700">
                        Showing <span class="font-medium">1</span> to <span class="font-medium">3</span> of <span class="font-medium">12</span> invoices
                      </p>
                    </div>
                    <div>
                      <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                          <span class="sr-only">Previous</span>
                          <i class="ri-arrow-left-s-line"></i>
                        </a>
                        <a href="#" aria-current="page" class="z-10 bg-primary bg-opacity-10 border-primary text-primary relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                          1
                        </a>
                        <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                          2
                        </a>
                        <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                          3
                        </a>
                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                          <span class="sr-only">Next</span>
                          <i class="ri-arrow-right-s-line"></i>
                        </a>
                      </nav>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
    <script>
      // Tab switching function
      function showTab(tabId) {
        // Hide all tab contents
        document.querySelectorAll('.tab-content').forEach(content => {
          content.classList.add('hidden');
          content.classList.remove('active');
        });

        // Show the selected tab content
        const selectedContent = document.getElementById(`${tabId}-content`);
        if (selectedContent) {
          selectedContent.classList.remove('hidden');
          selectedContent.classList.add('active');
        }

        // Update tab buttons
        document.querySelectorAll('.tab-button').forEach(button => {
          button.classList.remove('active');
          button.classList.add('text-gray-500');
        });

        const activeTab = document.getElementById(`${tabId}-tab`);
        if (activeTab) {
          activeTab.classList.add('active');
          activeTab.classList.remove('text-gray-500');
        }
      }


      const actionButton = document.getElementById('action-button');
        const iconContainer = actionButton.querySelector('div');
        if (tabId === 'transactions') {
          iconContainer.innerHTML = '<i class="ri-money-dollar-circle-line"></i>';
          actionButton.lastChild.nodeValue = 'Receive Payment';
        } else if (tabId === 'accounts') {
          iconContainer.innerHTML = '<i class="ri-bank-line"></i>';
          actionButton.lastChild.nodeValue = 'Manage Accounts';
        } else if (tabId === 'reports') {
          iconContainer.innerHTML = '<i class="ri-file-chart-line"></i>';
          actionButton.lastChild.nodeValue = 'Generate Report';
        }


      // Toggle dropdown menu
      function toggleDropdown() {
        const dropdown = document.getElementById('userDropdown');
        dropdown.classList.toggle('show');

        // Prevent the click from propagating to the window event
        event.stopPropagation();
      }

      // Close dropdown when clicking outside
      window.addEventListener('click', function(event) {
        if (!event.target.matches('.dropdown button') && !event.target.closest('.dropdown button img')) {
          const dropdowns = document.getElementsByClassName('dropdown-content');
          for (let i = 0; i < dropdowns.length; i++) {
            const openDropdown = dropdowns[i];
            if (openDropdown.classList.contains('show')) {
              openDropdown.classList.remove('show');
            }
          }
        }
      });

      // Make functions globally available
      window.showTab = showTab;
      window.toggleDropdown = toggleDropdown;

      // Toggle mobile sidebar
      function toggleMobileSidebar() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('mobileSidebarOverlay');

        sidebar.classList.toggle('mobile-sidebar-open');
        overlay.classList.toggle('show');

        // Close sidebar when clicking on overlay
        overlay.onclick = function() {
          sidebar.classList.remove('mobile-sidebar-open');
          overlay.classList.remove('show');
        };
      }

      // Make toggleMobileSidebar function globally available
      window.toggleMobileSidebar = toggleMobileSidebar;
    </script>
  </body>
</html>



