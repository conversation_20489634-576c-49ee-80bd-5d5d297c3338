<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Network License Evaluation - MACRA Digital Portal</title>
  <script src="https://cdn.tailwindcss.com/3.4.16"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" 
    integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" 
    crossorigin="anonymous" referrerpolicy="no-referrer" />
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: { primary: "#e02b20", secondary: "#6366f1" },
          borderRadius: {
            none: "0px",
            sm: "4px",
            DEFAULT: "8px",
            md: "12px",
            lg: "16px",
            xl: "20px",
            "2xl": "24px",
            "3xl": "32px",
            full: "9999px",
            button: "8px",
          },
        },
      },
    };
  </script>
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css" />
  <style>
    :where([class^="ri-"])::before { content: "\f3c2"; }
    body {
      font-family: 'Inter', sans-serif;
      background-color: #f9fafb;
    }

    .evaluation-card {
      transition: all 0.3s ease;
      border-left: 4px solid transparent;
    }

    .evaluation-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
      border-left-color: #e02b20;
    }

    .score-input {
      @apply w-20 px-3 py-2 border-2 border-gray-300 rounded-md text-center font-semibold
      focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary;
    }

    .score-excellent { @apply bg-green-50 border-green-300 text-green-800; }
    .score-good { @apply bg-blue-50 border-blue-300 text-blue-800; }
    .score-fair { @apply bg-yellow-50 border-yellow-300 text-yellow-800; }
    .score-poor { @apply bg-red-50 border-red-300 text-red-800; }

    .progress-bar {
      transition: width 0.5s ease-in-out;
    }

    .criteria-section {
      border-left: 4px solid #e5e7eb;
      transition: border-color 0.3s ease;
    }

    .criteria-section.completed {
      border-left-color: #10b981;
    }

    .criteria-section.in-progress {
      border-left-color: #f59e0b;
    }

    .dropdown-content {
      display: none;
      position: absolute;
      right: 0;
      top: 100%;
      z-index: 50;
    }

    .dropdown-content.show {
      display: block;
    }

    .side-nav {
      overflow: auto;
      -ms-overflow-style: none;
      height: 75vh;
    }

    .side-nav::-webkit-scrollbar {
      display: none;
    }

    .side-nav {
      scrollbar-width: none;
    }

    @media (max-width: 768px) {
      .mobile-sidebar-open {
        display: block !important;
        position: fixed;
        z-index: 50;
        height: 100vh;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
      }

      .mobile-sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 40;
        display: none;
      }

      .mobile-sidebar-overlay.show {
        display: block;
      }
    }
  </style>
</head>

<body>
  <div class="flex h-screen overflow-hidden">
    <!-- Mobile sidebar overlay -->
    <div id="mobileSidebarOverlay" class="mobile-sidebar-overlay"></div>

    <!-- Sidebar -->
    <aside id="sidebar" class="w-64 bg-white shadow-lg flex-shrink-0 hidden md:block">
      <div class="h-16 flex items-center px-6 border-b">
        <div class="flex items-center">
          <img src="../images/macra-logo.png" alt="Logo" class="max-h-12 w-auto">
        </div>
      </div>
      <nav class="mt-6 px-4 side-nav">
        <div class="space-y-1">
          <a href="index.html" class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <i class="ri-dashboard-line"></i>
            </div>
            Dashboard
          </a>
          <a href="evaluations.html" class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md bg-primary bg-opacity-10 text-primary">
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <i class="ri-file-list-3-line"></i>
            </div>
            Evaluations
          </a>
          <a href="applications.html" class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <i class="ri-file-text-line"></i>
            </div>
            Applications
          </a>
          <a href="licenses.html" class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <i class="ri-key-line"></i>
            </div>
            Licenses
          </a>
          <a href="reports.html" class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <i class="ri-bar-chart-line"></i>
            </div>
            Reports
          </a>
        </div>

        <div class="mt-8">
          <h3 class="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider">
            Tools
          </h3>
          <div class="mt-2 space-y-1">
            <a href="document-viewer.html" class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-file-search-line"></i>
              </div>
              Document Viewer
            </a>
            <a href="compliance-check.html" class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-shield-check-line"></i>
              </div>
              Compliance Check
            </a>
          </div>
        </div>
      </nav>
      <div class="absolute bottom-0 w-64 p-4 border-t">
        <a href="profile.html" class="flex items-center hover:bg-gray-50 p-2 rounded-md">
          <img class="h-10 w-10 rounded-full" src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp" alt="Profile" />
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-900">Sarah Johnson</p>
            <p class="text-xs text-gray-500">Senior Evaluator</p>
          </div>
        </a>
      </div>
    </aside>

    <!-- Main content -->
    <div class="flex-1 flex flex-col overflow-hidden">
      <!-- Top header -->
      <header class="bg-white shadow-sm z-10">
        <div class="flex items-center justify-between h-16 px-4 sm:px-6">
          <button id="mobileMenuBtn" type="button" onclick="toggleMobileSidebar()" class="md:hidden text-gray-500 hover:text-gray-700 focus:outline-none">
            <div class="w-6 h-6 flex items-center justify-center">
              <i class="ri-menu-line ri-lg"></i>
            </div>
          </button>
          <div class="flex-1 flex items-center justify-center px-2 lg:ml-6 lg:justify-start">
            <div class="max-w-lg w-full">
              <label for="search" class="sr-only">Search</label>
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <div class="w-5 h-5 flex items-center justify-center text-gray-400">
                    <i class="ri-search-line"></i>
                  </div>
                </div>
                <input id="search" name="search" class="block w-full pl-10 pr-3 py-3 border-2 border-gray-300 rounded-md leading-5 bg-gray-50 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary text-sm hover:bg-white transition-colors" placeholder="Search applications..." type="search" />
              </div>
            </div>
          </div>
          <div class="flex items-center">
            <button type="button" class="flex-shrink-0 p-1 mr-4 text-gray-500 rounded-full hover:text-gray-700 focus:outline-none relative">
              <span class="sr-only">View notifications</span>
              <div class="w-6 h-6 flex items-center justify-center">
                <i class="ri-notification-3-line ri-lg"></i>
              </div>
              <span class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white"></span>
            </button>
            <div class="dropdown relative">
              <button type="button" onclick="toggleDropdown()" class="flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                <span class="sr-only">Open user menu</span>
                <img class="h-8 w-8 rounded-full" src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp" alt="Profile" />
              </button>
              <div id="userDropdown" class="dropdown-content mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
                <div class="py-1">
                  <a href="profile.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Your Profile</a>
                  <a href="settings.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Settings</a>
                  <a href="../auth/login.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Sign out</a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      <!-- Main content area -->
      <main class="flex-1 overflow-y-auto bg-gray-50 p-4 sm:p-6">
        <div class="max-w-7xl mx-auto">
          <!-- Page header -->
          <div class="mb-6">
            <div class="flex items-center justify-between">
              <div>
                <h1 class="text-2xl font-semibold text-gray-900">Network License Evaluation</h1>
                <p class="mt-1 text-sm text-gray-500">
                  Evaluate mobile network license application - APP-2025-MNL-001
                </p>
              </div>
              <div class="flex space-x-3">
                <button onclick="saveDraft()" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                  <i class="ri-save-line mr-2"></i>
                  Save Draft
                </button>
                <button onclick="submitEvaluation()" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                  <i class="ri-check-line mr-2"></i>
                  Submit Evaluation
                </button>
              </div>
            </div>
          </div>

          <!-- Application Overview -->
          <div class="bg-white shadow rounded-lg p-6 mb-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Application Overview</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <dt class="text-sm font-medium text-gray-500">Applicant</dt>
                <dd class="mt-1 text-sm text-gray-900">Acme Corporation</dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">License Type</dt>
                <dd class="mt-1 text-sm text-gray-900">Mobile Network License</dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Application Date</dt>
                <dd class="mt-1 text-sm text-gray-900">15 December 2024</dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Evaluation Deadline</dt>
                <dd class="mt-1 text-sm text-gray-900">15 February 2025</dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Assigned Evaluator</dt>
                <dd class="mt-1 text-sm text-gray-900">Sarah Johnson</dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Status</dt>
                <dd class="mt-1">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    In Progress
                  </span>
                </dd>
              </div>
            </div>
          </div>

          <!-- Evaluation Progress -->
          <div class="bg-white shadow rounded-lg p-6 mb-6">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-medium text-gray-900">Evaluation Progress</h3>
              <span id="overallProgress" class="text-sm font-medium text-gray-600">0% Complete</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div id="progressBar" class="progress-bar bg-primary h-2 rounded-full" style="width: 0%"></div>
            </div>
            <div class="mt-4 grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
              <div class="text-center">
                <div class="font-medium text-gray-900">Technical Capacity</div>
                <div id="tech-progress" class="text-gray-500">0/5 criteria</div>
              </div>
              <div class="text-center">
                <div class="font-medium text-gray-900">Financial Capacity</div>
                <div id="financial-progress" class="text-gray-500">0/4 criteria</div>
              </div>
              <div class="text-center">
                <div class="font-medium text-gray-900">Legal Compliance</div>
                <div id="legal-progress" class="text-gray-500">0/3 criteria</div>
              </div>
              <div class="text-center">
                <div class="font-medium text-gray-900">Market Analysis</div>
                <div id="market-progress" class="text-gray-500">0/3 criteria</div>
              </div>
            </div>
          </div>

          <!-- Evaluation Criteria -->
          <div class="space-y-6">
            <!-- Technical Capacity -->
            <div class="criteria-section bg-white shadow rounded-lg p-6" id="technical-section">
              <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                  <i class="ri-settings-3-line mr-2 text-primary"></i>
                  Technical Capacity (40 points)
                </h3>
                <span id="technical-score" class="text-lg font-semibold text-gray-600">0/40</span>
              </div>

              <div class="space-y-6">
                <!-- Network Infrastructure -->
                <div class="evaluation-card bg-gray-50 rounded-lg p-4">
                  <div class="flex items-start justify-between">
                    <div class="flex-1">
                      <h4 class="text-md font-medium text-gray-900 mb-2">Network Infrastructure Plan</h4>
                      <p class="text-sm text-gray-600 mb-3">Assessment of proposed network architecture, coverage plans, and infrastructure deployment strategy.</p>
                      <div class="flex items-center space-x-4">
                        <label class="text-sm font-medium text-gray-700">Score (0-10):</label>
                        <input type="number" min="0" max="10" class="score-input" data-category="technical" data-max="10" onchange="updateScore(this)">
                        <button onclick="viewDocument('network-plan')" class="text-sm text-primary hover:underline">
                          <i class="ri-file-text-line mr-1"></i>View Document
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Technical Personnel -->
                <div class="evaluation-card bg-gray-50 rounded-lg p-4">
                  <div class="flex items-start justify-between">
                    <div class="flex-1">
                      <h4 class="text-md font-medium text-gray-900 mb-2">Technical Personnel Qualifications</h4>
                      <p class="text-sm text-gray-600 mb-3">Evaluation of technical team qualifications, experience, and organizational structure.</p>
                      <div class="flex items-center space-x-4">
                        <label class="text-sm font-medium text-gray-700">Score (0-10):</label>
                        <input type="number" min="0" max="10" class="score-input" data-category="technical" data-max="10" onchange="updateScore(this)">
                        <button onclick="viewDocument('personnel-cv')" class="text-sm text-primary hover:underline">
                          <i class="ri-file-text-line mr-1"></i>View CVs
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Spectrum Management -->
                <div class="evaluation-card bg-gray-50 rounded-lg p-4">
                  <div class="flex items-start justify-between">
                    <div class="flex-1">
                      <h4 class="text-md font-medium text-gray-900 mb-2">Spectrum Management Plan</h4>
                      <p class="text-sm text-gray-600 mb-3">Assessment of spectrum utilization strategy and interference management.</p>
                      <div class="flex items-center space-x-4">
                        <label class="text-sm font-medium text-gray-700">Score (0-8):</label>
                        <input type="number" min="0" max="8" class="score-input" data-category="technical" data-max="8" onchange="updateScore(this)">
                        <button onclick="viewDocument('spectrum-plan')" class="text-sm text-primary hover:underline">
                          <i class="ri-file-text-line mr-1"></i>View Plan
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Quality of Service -->
                <div class="evaluation-card bg-gray-50 rounded-lg p-4">
                  <div class="flex items-start justify-between">
                    <div class="flex-1">
                      <h4 class="text-md font-medium text-gray-900 mb-2">Quality of Service Standards</h4>
                      <p class="text-sm text-gray-600 mb-3">Evaluation of proposed QoS metrics and service level commitments.</p>
                      <div class="flex items-center space-x-4">
                        <label class="text-sm font-medium text-gray-700">Score (0-6):</label>
                        <input type="number" min="0" max="6" class="score-input" data-category="technical" data-max="6" onchange="updateScore(this)">
                        <button onclick="viewDocument('qos-plan')" class="text-sm text-primary hover:underline">
                          <i class="ri-file-text-line mr-1"></i>View Standards
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Security Measures -->
                <div class="evaluation-card bg-gray-50 rounded-lg p-4">
                  <div class="flex items-start justify-between">
                    <div class="flex-1">
                      <h4 class="text-md font-medium text-gray-900 mb-2">Network Security Measures</h4>
                      <p class="text-sm text-gray-600 mb-3">Assessment of cybersecurity framework and data protection measures.</p>
                      <div class="flex items-center space-x-4">
                        <label class="text-sm font-medium text-gray-700">Score (0-6):</label>
                        <input type="number" min="0" max="6" class="score-input" data-category="technical" data-max="6" onchange="updateScore(this)">
                        <button onclick="viewDocument('security-plan')" class="text-sm text-primary hover:underline">
                          <i class="ri-file-text-line mr-1"></i>View Security Plan
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Financial Capacity -->
            <div class="criteria-section bg-white shadow rounded-lg p-6" id="financial-section">
              <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                  <i class="ri-money-dollar-circle-line mr-2 text-primary"></i>
                  Financial Capacity (30 points)
                </h3>
                <span id="financial-score" class="text-lg font-semibold text-gray-600">0/30</span>
              </div>

              <div class="space-y-6">
                <!-- Financial Statements -->
                <div class="evaluation-card bg-gray-50 rounded-lg p-4">
                  <div class="flex items-start justify-between">
                    <div class="flex-1">
                      <h4 class="text-md font-medium text-gray-900 mb-2">Financial Statements & Audits</h4>
                      <p class="text-sm text-gray-600 mb-3">Review of audited financial statements and financial health indicators.</p>
                      <div class="flex items-center space-x-4">
                        <label class="text-sm font-medium text-gray-700">Score (0-10):</label>
                        <input type="number" min="0" max="10" class="score-input" data-category="financial" data-max="10" onchange="updateScore(this)">
                        <button onclick="viewDocument('financial-statements')" class="text-sm text-primary hover:underline">
                          <i class="ri-file-text-line mr-1"></i>View Statements
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Business Plan -->
                <div class="evaluation-card bg-gray-50 rounded-lg p-4">
                  <div class="flex items-start justify-between">
                    <div class="flex-1">
                      <h4 class="text-md font-medium text-gray-900 mb-2">Business Plan & Revenue Projections</h4>
                      <p class="text-sm text-gray-600 mb-3">Assessment of business model viability and revenue forecasts.</p>
                      <div class="flex items-center space-x-4">
                        <label class="text-sm font-medium text-gray-700">Score (0-8):</label>
                        <input type="number" min="0" max="8" class="score-input" data-category="financial" data-max="8" onchange="updateScore(this)">
                        <button onclick="viewDocument('business-plan')" class="text-sm text-primary hover:underline">
                          <i class="ri-file-text-line mr-1"></i>View Business Plan
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Funding Sources -->
                <div class="evaluation-card bg-gray-50 rounded-lg p-4">
                  <div class="flex items-start justify-between">
                    <div class="flex-1">
                      <h4 class="text-md font-medium text-gray-900 mb-2">Funding Sources & Capital Adequacy</h4>
                      <p class="text-sm text-gray-600 mb-3">Evaluation of funding sources and capital adequacy for network deployment.</p>
                      <div class="flex items-center space-x-4">
                        <label class="text-sm font-medium text-gray-700">Score (0-7):</label>
                        <input type="number" min="0" max="7" class="score-input" data-category="financial" data-max="7" onchange="updateScore(this)">
                        <button onclick="viewDocument('funding-proof')" class="text-sm text-primary hover:underline">
                          <i class="ri-file-text-line mr-1"></i>View Funding Proof
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Financial Projections -->
                <div class="evaluation-card bg-gray-50 rounded-lg p-4">
                  <div class="flex items-start justify-between">
                    <div class="flex-1">
                      <h4 class="text-md font-medium text-gray-900 mb-2">Financial Projections & Sustainability</h4>
                      <p class="text-sm text-gray-600 mb-3">Assessment of long-term financial sustainability and cash flow projections.</p>
                      <div class="flex items-center space-x-4">
                        <label class="text-sm font-medium text-gray-700">Score (0-5):</label>
                        <input type="number" min="0" max="5" class="score-input" data-category="financial" data-max="5" onchange="updateScore(this)">
                        <button onclick="viewDocument('projections')" class="text-sm text-primary hover:underline">
                          <i class="ri-file-text-line mr-1"></i>View Projections
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Legal Compliance -->
            <div class="criteria-section bg-white shadow rounded-lg p-6" id="legal-section">
              <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                  <i class="ri-scales-3-line mr-2 text-primary"></i>
                  Legal Compliance (20 points)
                </h3>
                <span id="legal-score" class="text-lg font-semibold text-gray-600">0/20</span>
              </div>

              <div class="space-y-6">
                <!-- Corporate Documentation -->
                <div class="evaluation-card bg-gray-50 rounded-lg p-4">
                  <div class="flex items-start justify-between">
                    <div class="flex-1">
                      <h4 class="text-md font-medium text-gray-900 mb-2">Corporate Documentation & Registration</h4>
                      <p class="text-sm text-gray-600 mb-3">Verification of company registration, articles of incorporation, and corporate structure.</p>
                      <div class="flex items-center space-x-4">
                        <label class="text-sm font-medium text-gray-700">Score (0-8):</label>
                        <input type="number" min="0" max="8" class="score-input" data-category="legal" data-max="8" onchange="updateScore(this)">
                        <button onclick="viewDocument('corporate-docs')" class="text-sm text-primary hover:underline">
                          <i class="ri-file-text-line mr-1"></i>View Documents
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Regulatory Compliance -->
                <div class="evaluation-card bg-gray-50 rounded-lg p-4">
                  <div class="flex items-start justify-between">
                    <div class="flex-1">
                      <h4 class="text-md font-medium text-gray-900 mb-2">Regulatory Compliance History</h4>
                      <p class="text-sm text-gray-600 mb-3">Assessment of compliance with telecommunications regulations and any violations.</p>
                      <div class="flex items-center space-x-4">
                        <label class="text-sm font-medium text-gray-700">Score (0-7):</label>
                        <input type="number" min="0" max="7" class="score-input" data-category="legal" data-max="7" onchange="updateScore(this)">
                        <button onclick="viewDocument('compliance-history')" class="text-sm text-primary hover:underline">
                          <i class="ri-file-text-line mr-1"></i>View History
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Ownership Structure -->
                <div class="evaluation-card bg-gray-50 rounded-lg p-4">
                  <div class="flex items-start justify-between">
                    <div class="flex-1">
                      <h4 class="text-md font-medium text-gray-900 mb-2">Ownership Structure & Local Content</h4>
                      <p class="text-sm text-gray-600 mb-3">Verification of Malawian ownership requirements and shareholding structure.</p>
                      <div class="flex items-center space-x-4">
                        <label class="text-sm font-medium text-gray-700">Score (0-5):</label>
                        <input type="number" min="0" max="5" class="score-input" data-category="legal" data-max="5" onchange="updateScore(this)">
                        <button onclick="viewDocument('ownership-structure')" class="text-sm text-primary hover:underline">
                          <i class="ri-file-text-line mr-1"></i>View Structure
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Market Analysis -->
            <div class="criteria-section bg-white shadow rounded-lg p-6" id="market-section">
              <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                  <i class="ri-line-chart-line mr-2 text-primary"></i>
                  Market Analysis (10 points)
                </h3>
                <span id="market-score" class="text-lg font-semibold text-gray-600">0/10</span>
              </div>

              <div class="space-y-6">
                <!-- Market Research -->
                <div class="evaluation-card bg-gray-50 rounded-lg p-4">
                  <div class="flex items-start justify-between">
                    <div class="flex-1">
                      <h4 class="text-md font-medium text-gray-900 mb-2">Market Research & Analysis</h4>
                      <p class="text-sm text-gray-600 mb-3">Assessment of market research quality and understanding of telecommunications market.</p>
                      <div class="flex items-center space-x-4">
                        <label class="text-sm font-medium text-gray-700">Score (0-4):</label>
                        <input type="number" min="0" max="4" class="score-input" data-category="market" data-max="4" onchange="updateScore(this)">
                        <button onclick="viewDocument('market-research')" class="text-sm text-primary hover:underline">
                          <i class="ri-file-text-line mr-1"></i>View Research
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Competition Analysis -->
                <div class="evaluation-card bg-gray-50 rounded-lg p-4">
                  <div class="flex items-start justify-between">
                    <div class="flex-1">
                      <h4 class="text-md font-medium text-gray-900 mb-2">Competition Analysis & Strategy</h4>
                      <p class="text-sm text-gray-600 mb-3">Evaluation of competitive landscape analysis and differentiation strategy.</p>
                      <div class="flex items-center space-x-4">
                        <label class="text-sm font-medium text-gray-700">Score (0-3):</label>
                        <input type="number" min="0" max="3" class="score-input" data-category="market" data-max="3" onchange="updateScore(this)">
                        <button onclick="viewDocument('competition-analysis')" class="text-sm text-primary hover:underline">
                          <i class="ri-file-text-line mr-1"></i>View Analysis
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Service Innovation -->
                <div class="evaluation-card bg-gray-50 rounded-lg p-4">
                  <div class="flex items-start justify-between">
                    <div class="flex-1">
                      <h4 class="text-md font-medium text-gray-900 mb-2">Service Innovation & Value Proposition</h4>
                      <p class="text-sm text-gray-600 mb-3">Assessment of innovative services and value proposition for Malawian market.</p>
                      <div class="flex items-center space-x-4">
                        <label class="text-sm font-medium text-gray-700">Score (0-3):</label>
                        <input type="number" min="0" max="3" class="score-input" data-category="market" data-max="3" onchange="updateScore(this)">
                        <button onclick="viewDocument('innovation-plan')" class="text-sm text-primary hover:underline">
                          <i class="ri-file-text-line mr-1"></i>View Innovation Plan
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Evaluation Summary -->
          <div class="bg-white shadow rounded-lg p-6 mt-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Evaluation Summary</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 class="text-md font-medium text-gray-900 mb-3">Score Breakdown</h4>
                <div class="space-y-2">
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600">Technical Capacity:</span>
                    <span id="summary-technical" class="text-sm font-medium">0/40</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600">Financial Capacity:</span>
                    <span id="summary-financial" class="text-sm font-medium">0/30</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600">Legal Compliance:</span>
                    <span id="summary-legal" class="text-sm font-medium">0/20</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600">Market Analysis:</span>
                    <span id="summary-market" class="text-sm font-medium">0/10</span>
                  </div>
                  <div class="border-t pt-2 mt-2">
                    <div class="flex justify-between">
                      <span class="text-md font-semibold text-gray-900">Total Score:</span>
                      <span id="summary-total" class="text-md font-semibold text-primary">0/100</span>
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <h4 class="text-md font-medium text-gray-900 mb-3">Recommendation</h4>
                <div id="recommendation" class="p-4 rounded-lg bg-gray-50">
                  <p class="text-sm text-gray-600">Complete the evaluation to see recommendation.</p>
                </div>
                <div class="mt-4">
                  <label for="evaluator-comments" class="block text-sm font-medium text-gray-700 mb-2">
                    Additional Comments
                  </label>
                  <textarea id="evaluator-comments" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary" placeholder="Enter any additional comments or observations..."></textarea>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>

  <script>
    // Evaluation data structure
    const evaluationData = {
      technical: { score: 0, max: 40, criteria: 5 },
      financial: { score: 0, max: 30, criteria: 4 },
      legal: { score: 0, max: 20, criteria: 3 },
      market: { score: 0, max: 10, criteria: 3 }
    };

    // Mobile sidebar functionality
    function toggleMobileSidebar() {
      const sidebar = document.getElementById('sidebar');
      const overlay = document.getElementById('mobileSidebarOverlay');

      sidebar.classList.toggle('mobile-sidebar-open');
      overlay.classList.toggle('show');
    }

    // Close mobile sidebar when clicking overlay
    document.getElementById('mobileSidebarOverlay').addEventListener('click', function() {
      toggleMobileSidebar();
    });

    // User dropdown functionality
    function toggleDropdown() {
      const dropdown = document.getElementById('userDropdown');
      dropdown.classList.toggle('show');
    }

    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
      const dropdown = document.getElementById('userDropdown');
      const button = event.target.closest('.dropdown button');

      if (!button && dropdown.classList.contains('show')) {
        dropdown.classList.remove('show');
      }
    });

    // Update score functionality
    function updateScore(input) {
      const category = input.dataset.category;
      const maxScore = parseInt(input.dataset.max);
      const value = parseInt(input.value) || 0;

      // Validate input
      if (value > maxScore) {
        input.value = maxScore;
        return;
      }

      // Update score styling based on value
      input.className = 'score-input';
      if (value >= maxScore * 0.8) {
        input.classList.add('score-excellent');
      } else if (value >= maxScore * 0.6) {
        input.classList.add('score-good');
      } else if (value >= maxScore * 0.4) {
        input.classList.add('score-fair');
      } else if (value > 0) {
        input.classList.add('score-poor');
      }

      // Calculate category totals
      calculateCategoryScore(category);
      updateProgress();
      updateSummary();
      updateRecommendation();
    }

    // Calculate category score
    function calculateCategoryScore(category) {
      const inputs = document.querySelectorAll(`input[data-category="${category}"]`);
      let total = 0;
      let completed = 0;

      inputs.forEach(input => {
        const value = parseInt(input.value) || 0;
        total += value;
        if (value > 0) completed++;
      });

      evaluationData[category].score = total;

      // Update category score display
      document.getElementById(`${category}-score`).textContent = `${total}/${evaluationData[category].max}`;

      // Update progress indicator
      document.getElementById(`${category}-progress`).textContent = `${completed}/${evaluationData[category].criteria} criteria`;

      // Update section styling
      const section = document.getElementById(`${category}-section`);
      section.classList.remove('completed', 'in-progress');

      if (completed === evaluationData[category].criteria) {
        section.classList.add('completed');
      } else if (completed > 0) {
        section.classList.add('in-progress');
      }
    }

    // Update overall progress
    function updateProgress() {
      const totalScore = Object.values(evaluationData).reduce((sum, cat) => sum + cat.score, 0);
      const maxScore = Object.values(evaluationData).reduce((sum, cat) => sum + cat.max, 0);
      const totalCriteria = Object.values(evaluationData).reduce((sum, cat) => sum + cat.criteria, 0);

      // Count completed criteria
      let completedCriteria = 0;
      Object.keys(evaluationData).forEach(category => {
        const inputs = document.querySelectorAll(`input[data-category="${category}"]`);
        inputs.forEach(input => {
          if (parseInt(input.value) > 0) completedCriteria++;
        });
      });

      const progressPercentage = (completedCriteria / totalCriteria) * 100;

      // Update progress bar
      document.getElementById('progressBar').style.width = `${progressPercentage}%`;
      document.getElementById('overallProgress').textContent = `${Math.round(progressPercentage)}% Complete`;
    }

    // Update summary section
    function updateSummary() {
      Object.keys(evaluationData).forEach(category => {
        const score = evaluationData[category].score;
        const max = evaluationData[category].max;
        document.getElementById(`summary-${category}`).textContent = `${score}/${max}`;
      });

      const totalScore = Object.values(evaluationData).reduce((sum, cat) => sum + cat.score, 0);
      document.getElementById('summary-total').textContent = `${totalScore}/100`;
    }

    // Update recommendation
    function updateRecommendation() {
      const totalScore = Object.values(evaluationData).reduce((sum, cat) => sum + cat.score, 0);
      const recommendationDiv = document.getElementById('recommendation');

      let recommendation = '';
      let bgClass = 'bg-gray-50';
      let textClass = 'text-gray-600';

      if (totalScore >= 80) {
        recommendation = 'APPROVE: Excellent application meeting all requirements. Recommend immediate license approval.';
        bgClass = 'bg-green-50';
        textClass = 'text-green-800';
      } else if (totalScore >= 70) {
        recommendation = 'APPROVE WITH CONDITIONS: Good application with minor areas for improvement. Recommend approval with specific conditions.';
        bgClass = 'bg-blue-50';
        textClass = 'text-blue-800';
      } else if (totalScore >= 60) {
        recommendation = 'CONDITIONAL APPROVAL: Application shows potential but requires significant improvements before approval.';
        bgClass = 'bg-yellow-50';
        textClass = 'text-yellow-800';
      } else if (totalScore >= 40) {
        recommendation = 'DEFER: Application has substantial deficiencies. Recommend deferral pending major improvements.';
        bgClass = 'bg-orange-50';
        textClass = 'text-orange-800';
      } else if (totalScore > 0) {
        recommendation = 'REJECT: Application does not meet minimum requirements. Recommend rejection.';
        bgClass = 'bg-red-50';
        textClass = 'text-red-800';
      } else {
        recommendation = 'Complete the evaluation to see recommendation.';
        bgClass = 'bg-gray-50';
        textClass = 'text-gray-600';
      }

      recommendationDiv.className = `p-4 rounded-lg ${bgClass}`;
      recommendationDiv.innerHTML = `<p class="text-sm font-medium ${textClass}">${recommendation}</p>`;
    }

    // Document viewer functionality
    function viewDocument(docType) {
      // In a real application, this would open a document viewer
      alert(`Opening ${docType} document viewer...`);
    }

    // Save draft functionality
    function saveDraft() {
      const evaluationState = {
        scores: {},
        comments: document.getElementById('evaluator-comments').value,
        timestamp: new Date().toISOString()
      };

      // Collect all scores
      document.querySelectorAll('.score-input').forEach(input => {
        const category = input.dataset.category;
        const max = input.dataset.max;
        const value = input.value;

        if (!evaluationState.scores[category]) {
          evaluationState.scores[category] = [];
        }
        evaluationState.scores[category].push({ max, value });
      });

      // Save to localStorage (in real app, would save to server)
      localStorage.setItem('evaluation-draft', JSON.stringify(evaluationState));

      // Show success message
      showNotification('Draft saved successfully!', 'success');
    }

    // Submit evaluation functionality
    function submitEvaluation() {
      const totalScore = Object.values(evaluationData).reduce((sum, cat) => sum + cat.score, 0);

      if (totalScore === 0) {
        showNotification('Please complete at least some evaluation criteria before submitting.', 'error');
        return;
      }

      // In a real application, this would submit to the server
      const confirmation = confirm(`Submit evaluation with total score ${totalScore}/100?`);

      if (confirmation) {
        showNotification('Evaluation submitted successfully!', 'success');
        // Redirect or update UI as needed
      }
    }

    // Show notification
    function showNotification(message, type) {
      // Create notification element
      const notification = document.createElement('div');
      notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg ${
        type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
      }`;
      notification.textContent = message;

      document.body.appendChild(notification);

      // Remove after 3 seconds
      setTimeout(() => {
        notification.remove();
      }, 3000);
    }

    // Load draft on page load
    document.addEventListener('DOMContentLoaded', function() {
      const draft = localStorage.getItem('evaluation-draft');
      if (draft) {
        try {
          const evaluationState = JSON.parse(draft);

          // Restore comments
          if (evaluationState.comments) {
            document.getElementById('evaluator-comments').value = evaluationState.comments;
          }

          // Restore scores
          Object.keys(evaluationState.scores).forEach(category => {
            const inputs = document.querySelectorAll(`input[data-category="${category}"]`);
            evaluationState.scores[category].forEach((score, index) => {
              if (inputs[index] && score.value) {
                inputs[index].value = score.value;
                updateScore(inputs[index]);
              }
            });
          });

          console.log('Draft loaded successfully');
        } catch (error) {
          console.error('Error loading draft:', error);
        }
      }
    });
  </script>
</body>
</html>
