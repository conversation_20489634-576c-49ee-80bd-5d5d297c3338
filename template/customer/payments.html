<!DOCTYPE html>
<html lang="en">

<head>
  <!-- Existing head content remains unchanged -->
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Accounts & Finance - Digital Portal Dashboard</title>
  <script src="https://cdn.tailwindcss.com/3.4.16"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css"
    integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg=="
    crossorigin="anonymous" referrerpolicy="no-referrer" />
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: { primary: "#e02b20", secondary: "#6366f1" },
          borderRadius: {
            none: "0px",
            sm: "4px",
            DEFAULT: "8px",
            md: "12px",
            lg: "16px",
            xl: "20px",
            "2xl": "24px",
            "3xl": "32px",
            full: "9999px",
            button: "8px",
          },
        },
      },
    };
  </script>
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css" />
  <style>
    /* Existing styles remain unchanged */
    :where([class^="ri-"])::before {
      content: "\f3c2";
    }

    body {
      font-family: 'Inter', sans-serif;
      background-color: #f9fafb;
    }

    .enhanced-input {
      @apply appearance-none block w-full px-4 py-2 border-2 border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary sm:text-sm bg-gray-50 hover:bg-white transition-colors;
    }

    .enhanced-select {
      @apply block w-full px-4 py-2 border-2 border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary sm:text-sm bg-gray-50 hover:bg-white transition-colors;
    }

    .enhanced-checkbox {
      @apply h-5 w-5 text-primary focus:ring-2 focus:ring-primary border-2 border-gray-300 rounded;
    }

    .enhanced-button {
      @apply flex justify-center py-3 px-4 border-2 border-transparent rounded-md shadow-md text-sm font-medium text-white bg-primary hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all;
    }

    .dropdown-content {
      display: none;
      position: absolute;
      right: 0;
      top: 100%;
      z-index: 50;
    }

    .dropdown-content.show {
      display: block;
    }

    .side-nav {
      overflow: auto;
      -ms-overflow-style: none;
      height: 75vh;
    }

    .side-nav::-webkit-scrollbar {
      display: none;
    }

    .side-nav {
      scrollbar-width: none;
    }

    .tab-button {
      position: relative;
      z-index: 1;
    }

    .tab-button.active {
      color: #e02b20;
      font-weight: 500;
    }

    .tab-button.active::after {
      content: '';
      position: absolute;
      bottom: -6px;
      left: 0;
      width: 100%;
      height: 2px;
      background-color: #e02b20;
    }

    .tab-content {
      display: block;
    }

    .tab-content.hidden {
      display: none;
    }

    .modal {
      display: none;
      position: fixed;
      z-index: 100;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      overflow: auto;
      background-color: rgba(0, 0, 0, 0.5);
    }

    .modal.show {
      display: block;
    }

    .modal-content {
      background-color: #fff;
      margin: 5% auto;
      padding: 20px;
      border-radius: 8px;
      width: 80%;
      max-width: 800px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .modal-close {
      color: #aaa;
      float: right;
      font-size: 28px;
      font-weight: bold;
      cursor: pointer;
    }

    .modal-close:hover,
    .modal-close:focus {
      color: #000;
      text-decoration: none;
      cursor: pointer;
    }

    @media (max-width: 768px) {
      .mobile-sidebar-open {
        display: block !important;
        position: fixed;
        z-index: 50;
        height: 100vh;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
      }

      .mobile-sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 40;
        display: none;
      }

      .mobile-sidebar-overlay.show {
        display: block;
      }
    }
  </style>
</head>

<body>
  <div class="flex h-screen overflow-hidden">
    <!-- Mobile sidebar overlay -->
    <div id="mobileSidebarOverlay" class="mobile-sidebar-overlay"></div>

      <!-- Sidebar -->
      <aside id="sidebar" class="w-64 bg-white shadow-lg flex-shrink-0 hidden md:block">
        <div class="h-16 flex items-center px-6 border-b">
          <div class="flex items-center">
            <img src="../images/macra-logo.png" alt="Logo" class="max-h-12 w-auto">
          </div>
        </div>
        <nav class="mt-6 px-4 side-nav">
          <div class="space-y-1">
            <a
              href="index.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-dashboard-line"></i>
              </div>
              Dashboard
            </a>
            <a
              href="my-licenses.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-key-line"></i>
              </div>
              My Licenses
            </a>
            <a
              href="new-application.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-file-list-3-line"></i>
              </div>
              New Application
            </a>
            <a
              href="payments.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md bg-primary bg-opacity-10 text-primary"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z" />
                </svg>
              </div>
              Payments
            </a>
            <a
              href="documents.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-file-text-line"></i>
              </div>
              Documents
            </a>
            <a
              href="request-resource.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-hand-heart-line"></i>
              </div>
              Request Resource
            </a>
          </div>

          <div class="mt-8">
            <h3
              class="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider"
            >
              Support
            </h3>
            <div class="mt-2 space-y-1">
              <a
                href="help-center.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-question-line"></i>
                </div>
                Help Center
              </a>
              <a
                href="contact-support.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-customer-service-2-line"></i>
                </div>
                Contact Support
              </a>
            </div>
          </div>
        </nav>
        <div class="absolute bottom-0 w-64 p-4 border-t">
          <a href="profile.html" class="flex items-center hover:bg-gray-50 p-2 rounded-md">
            <img
              class="h-10 w-10 rounded-full"
              src="https://banner2.cleanpng.com/********/sac/9128e319522f66aba9edf95180a86e7e.webp"
              alt="Profile"
            />
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-900">John Smith</p>
              <p class="text-xs text-gray-500">Acme Corporation</p>
            </div>
          </a>
        </div>
      </aside>
      <!-- Main content -->
    <div class="flex-1 flex flex-col overflow-hidden">
      <!-- Top header -->
      <header class="bg-white shadow-sm z-10">
        <!-- Existing header content remains unchanged -->
        <div class="flex items-center justify-between h-16 px-4 sm:px-6">
          <button id="mobileMenuBtn" type="button" onclick="toggleMobileSidebar()"
            class="md:hidden text-gray-500 hover:text-gray-700 focus:outline-none">
            <div class="w-6 h-6 flex items-center justify-center">
              <i class="ri-menu-line ri-lg"></i>
            </div>
          </button>
          <div class="flex-1 flex items-center justify-center px-2 lg:ml-6 lg:justify-start">
            <div class="max-w-lg w-full">
              <label for="search" class="sr-only">Search</label>
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <div class="w-5 h-5 flex items-center justify-center text-gray-400">
                    <i class="ri-search-line"></i>
                  </div>
                </div>
                <input id="search" name="search"
                  class="block w-full pl-10 pr-3 py-3 border-2 border-gray-300 rounded-md leading-5 bg-gray-50 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary text-sm hover:bg-white transition-colors"
                  placeholder="Search for invoices, or payments..." type="search" />
              </div>
            </div>
          </div>
          <div class="flex items-center">
            <button type="button"
              class="flex-shrink-0 p-1 mr-4 text-gray-500 rounded-full hover:text-gray-700 focus:outline-none relative">
              <span class="sr-only">View notifications</span>
              <div class="w-6 h-6 flex items-center justify-center">
                <i class="ri-notification-3-line ri-lg"></i>
              </div>
              <span class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white"></span>
            </button>
            <div class="dropdown relative">
              <button type="button" onclick="toggleDropdown()"
                class="flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                <span class="sr-only">Open user menu</span>
                <img class="h-8 w-8 rounded-full"
                  src="https://banner2.cleanpng.com/********/sac/9128e319522f66aba9edf95180a86e7e.webp" alt="Profile" />
              </button>
              <div id="userDropdown"
                class="dropdown-content mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
                <div class="py-1">
                  <a href="../user-management/user-profile.html"
                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Your Profile</a>
                  <a href="../account-settings.html"
                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Settings</a>
                  <a href="../auth/login.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Sign
                    out</a>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- Secondary navigation -->
        <div class="border-t border-gray-200 px-4 sm:px-6">
          <div class="py-3 flex space-x-8">
            <button id="invoices-tab" onclick="showTab('invoices')" class="tab-button active text-sm px-1 py-2">
              Invoices
            </button>
            <button id="payments-tab" onclick="showTab('payments')" class="tab-button text-sm px-1 py-2 text-gray-500">
              Payments
            </button>
          </div>
        </div>
      </header>
      <!-- Main content area -->
      <main class="flex-1 overflow-y-auto bg-gray-50 p-4 sm:p-6">
        <!-- Invoices Tab Content -->
        <div id="invoices-content" class="tab-content">
          <!-- Existing invoices tab content remains unchanged -->
          <div class="mb-6">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
              <div>
                <h1 class="text-2xl font-semibold text-gray-900">Invoices</h1>
                <p class="text-sm text-gray-500">Manage invoices, payments, and financial transactions.</p>
              </div>

              <div>
                <div class="flex justify-start sm:justify-end space-x-2">
                  <div class="relative">
                    <button type="button" onclick="openViewInvoicesModal()"
                      class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                      <div class="w-5 h-5 flex items-center justify-center mr-2">
                        <i class="ri-eye-line"></i>
                      </div>
                      View Invoices
                    </button>
                  </div>

                  <div class="relative">
                    <button type="button" onclick="showExportModal()"
                      class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                      <div class="w-5 h-5 flex items-center justify-center mr-2">
                        <i class="ri-download-line"></i>
                      </div>
                      Export
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- Filters -->
          <div class="bg-white shadow overflow-hidden sm:rounded-md mb-6">
            <div class="px-4 py-5 sm:p-6">
              <div class="grid grid-cols-1 gap-y-4 sm:grid-cols-4 sm:gap-x-6">
                <div>
                  <label for="invoice-status" class="block text-sm font-medium text-gray-700">Status</label>
                  <select id="invoice-status" name="invoice-status"
                    class="mt-1 block w-full rounded-md border-gray-300 p-2 shadow-sm focus:ring-primary focus:border-primary sm:text-sm">
                    <option value="">All Statuses</option>
                    <option value="paid">Paid</option>
                    <option value="pending">Pending</option>
                    <option value="overdue">Overdue</option>
                  </select>
                </div>
                <div>
                  <label for="invoice-date" class="block text-sm font-medium text-gray-700">Date Range</label>
                  <select id="invoice-date" name="invoice-date"
                    class="mt-1 block w-full rounded-md border-gray-300 p-2 shadow-sm focus:ring-primary focus:border-primary sm:text-sm">
                    <option value="">All Time</option>
                    <option value="last-30">Last 30 Days</option>
                    <option value="last-90">Last 90 Days</option>
                    <option value="last-year">Last Year</option>
                  </select>
                </div>
                <div>
                  <label for="invoice-amount" class="block text-sm font-medium text-gray-700">Amount (<strong>MWK</strong>)</label>
                  <select id="invoice-amount" name="invoice-amount"
                    class="mt-1 block w-full rounded-md border-gray-300 p-2 shadow-sm focus:ring-primary focus:border-primary sm:text-sm">
                    <option value="">All Amounts</option>
                    <option value="0-1000">0 - 500,000</option>
                    <option value="1000-5000">70,000 - 955,000</option>
                    <option value="50000+">75,000+</option>
                  </select>
                </div>
                <div class="flex items-end">
                  <button type="button"
                    class="inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 hover:text-white bg-white border border-primary rounded-full hover:bg-primary transition transform hover:translate-x-1 after:content-['_↗']">
                    Apply Filters
                  </button>
                </div>
              </div>
            </div>
          </div>
          <!-- Invoices Table -->
          <div class="bg-white shadow overflow-hidden sm:rounded-md">
            <div class="px-4 py-5 sm:p-6">
              <div class="flex flex-col">
                <div class="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                  <div class="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
                    <div class="overflow-hidden border border-gray-200 sm:rounded-lg">
                      <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                          <tr>
                            <th scope="col"
                              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Invoice #
                            </th>
                            <th scope="col"
                              class="px-ashi6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Invoice Date
                            </th>
                            <th scope="col"
                              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Due Date
                            </th>
                            <th scope="col"
                              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Client Name
                            </th>
                            <th scope="col"
                              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Resource
                            </th>
                            <th scope="col"
                              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Price (<strong>MWK</strong>)
                            </th>
                            <th scope="col"
                              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Total Amount (<strong>MWK</strong>)
                            </th>
                            <th scope="col"
                              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Payment Status
                            </th>
                            <th scope="col"
                              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Notes
                            </th>
                            <th scope="col" class="relative px-6 py-3">
                              <span class="sr-only">Actions</span>
                            </th>
                          </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                          <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <div class="text-sm font-medium text-gray-900">INV-2025-001</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <div class="text-sm text-gray-900">2025-03-11</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <div class="text-sm text-gray-900">2025-04-11</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <div class="text-sm font-medium text-gray-900">Airtel</div>
                              <div class="text-sm text-gray-500"><EMAIL></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <div class="text-sm text-gray-900">Internet service provider license</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <div class="text-sm text-gray-900">20,000,000</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <div class="text-sm text-gray-900">20,000,000</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <span
                                class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Paid</span>
                            </td>
                            <td class="px-6 py-4">
                              <div class="text-sm text-gray-900">Internet service provider license for 5 years</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <a href="#" class="inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 hover:text-white bg-white border border-primary rounded-full hover:bg-primary transition transform hover:translate-x-1 after:content-['_↗']">View</a>
                              <a href="#" class="inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 hover:text-white bg-white border border-primary rounded-full hover:bg-primary transition transform hover:translate-x-1 after:content-['_↗']">Download</a>
                            </td>
                          </tr>
                          <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <div class="text-sm font-medium text-gray-900">INV-2025-002</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <div class="text-sm text-gray-900">2024-12-25</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <div class="text-sm text-gray-900">2025-01-25</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <div class="text-sm font-medium text-gray-900">Dawn FM</div>
                              <div class="text-sm text-gray-500"><EMAIL></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <div class="text-sm text-gray-900">Radioincorporate Radio broadcasting license</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <div class="text-sm text-gray-900">5,000,000</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <div class="text-sm text-gray-900">3,000,000</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <span
                                class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Pending</span>
                            </td>
                            <td class="px-6 py-4">
                              <div class="text-sm text-gray-900">Radio broadcasting license for 3 years</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <a href="#" class="inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 hover:text-white bg-white border border-primary rounded-full hover:bg-primary transition transform hover:translate-x-1 after:content-['_↗']">View</a>
                              <a href="#" class="inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 hover:text-white bg-white border border-primary rounded-full hover:bg-primary transition transform hover:translate-x-1 after:content-['_↗']">Download</a>
                            </td>
                          </tr>
                          <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <div class="text-sm font-medium text-gray-900">INV-2025-003</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <div class="text-sm text-gray-900">2025-01-01</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <div class="text-sm text-gray-900">2025-02-01</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <div class="text-sm font-medium text-gray-900">Cruncyroll TV</div>
                              <div class="text-sm text-gray-500"><EMAIL></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <div class="text-sm text-gray-900">Tv broadcasting license</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <div class="text-sm text-gray-900">50,000,000</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <div class="text-sm text-gray-900">0</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <span
                                class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Overdue</span>
                            </td>
                            <td class="px-6 py-4">
                              <div class="text-sm text-gray-900">TV broadcasting license for 10 years</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <a href="#" class="inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 hover:text-white bg-white border border-primary rounded-full hover:bg-primary transition transform hover:translate-x-1 after:content-['_↗']">View</a>
                              <a href="#" class="inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 hover:text-white bg-white border border-primary rounded-full hover:bg-primary transition transform hover:translate-x-1 after:content-['_↗']">Download</a>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
              <!-- Pagination -->
              <div class="bg-white px-4 py-2 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <div class="flex-1 flex justify-between sm:hidden">
                  <a href="#"
                    class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Previous
                  </a>
                  <a href="#"
                    class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Next
                  </a>
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                  <div>
                    <p class="text-sm text-gray-700">
                      Showing <span class="font-medium">1</span> to <span class="font-medium">3</span> of <span
                        class="font-medium">3</span> invoices
                    </p>
                  </div>
                  <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                      <a href="#"
                        class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <span class="sr-only">Previous</span>
                        <i class="ri-arrow-left-s-line"></i>
                      </a>
                      <a href="#" aria-current="page"
                        class="z-10 bg-primary bg-opacity-10 border-primary text-primary relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                        1
                      </a>
                      <a href="#"
                        class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                        2
                      </a>
                      <a href="#"
                        class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                        3
                      </a>
                      <a href="#"
                        class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <span class="sr-only">Next</span>
                        <i class="ri-arrow-right-s-line"></i>
                      </a>
                    </nav>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- Payments Tab Content -->
        <div id="payments-content" class="tab-content hidden">
          <div class="mb-6">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
              <div>
                <h1 class="text-2xl font-semibold text-gray-900">Payments</h1>
                <p class="text-sm text-gray-500">View your payment history and transaction records.</p>
              </div>
            </div>
          </div>
          <!-- Filters -->
          <div class="bg-white shadow overflow-hidden sm:rounded-md mb-6">
            <div class="px-4 py-5 sm:p-6">
              <div class="grid grid-cols-1 gap-y-4 sm:grid-cols-4 sm:gap-x-6">
                <div>
                  <label for="invoice-status" class="block text-sm font-medium text-gray-700">Status</label>
                  <select id="invoice-status" name="invoice-status" class="mt-1 block w-full rounded-md border-gray-300 p-2 shadow-sm focus:ring-primary focus:border-primary sm:text-sm">
                    <option value="">All Statuses</option>
                    <option value="paid">Paid</option>
                    <option value="pending">Pending</option>
                    <option value="overdue">Overdue</option>
                  </select>
                </div>
                <div>
                  <label for="invoice-date" class="block text-sm font-medium text-gray-700">Date Range</label>
                  <select id="invoice-date" name="invoice-date" class="mt-1 block w-full rounded-md border-gray-300 p-2 shadow-sm focus:ring-primary focus:border-primary sm:text-sm">
                    <option value="">All Time</option>
                    <option value="last-30">Last 30 Days</option>
                    <option value="last-90">Last 90 Days</option>
                    <option value="last-year">Last Year</option>
                  </select>
                </div>
                <div>
                  <label for="invoice-amount" class="block text-sm font-medium text-gray-700">Amount (<strong>MWK</strong>)</label>
                  <select id="invoice-amount" name="invoice-amount" class="mt-1 block w-full rounded-md border-gray-300 p-2 shadow-sm focus:ring-primary focus:border-primary sm:text-sm">
                    <option value="">All Amounts</option>
                    <option value="0-1000">0 - 500,000</option>
                    <option value="1000-5000">70,000 - 955,000</option>
                    <option value="50000+">75,000+</option>
                  </select>
                </div>
                <div class="flex items-end">
                  <button type="button"
                    class="inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 hover:text-white bg-white border border-primary rounded-full hover:bg-primary transition transform hover:translate-x-1 after:content-['_↗']">
                    Apply Filters
                  </button>
                </div>
              </div>
            </div>
          </div>
          <!-- Payments Table -->
          <div class="bg-white shadow overflow-hidden sm:rounded-md">
            <div class="px-4 py-5 sm:p-6">
              <div class="flex flex-col">
                <div class="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                  <div class="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
                    <div class="overflow-hidden border border-gray-200 sm:rounded-lg">
                      <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                          <tr>
                            <th scope="col"
                              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Invoice #
                            </th>
                            <th scope="col"
                              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Client
                            </th>
                            <th scope="col"
                              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              License Category
                            </th>
                            <th scope="col"
                              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Issue Date
                            </th>
                            <th scope="col"
                              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Due Date
                            </th>
                            <th scope="col"
                              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Amount (<strong>MWK</strong>)
                            </th>
                            <th scope="col"
                              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Status
                            </th>
                            <th scope="col" class="relative px-6 py-3">
                              <span class="sr-only">Actions</span>
                            </th>
                          </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                          <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <div class="text-sm font-medium text-gray-900">INV-2025-001</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <div class="flex items-center">
                                <div>
                                  <div class="text-sm font-medium text-gray-900">Acme Corporation</div>
                                  <div class="text-sm text-gray-500"><EMAIL></div>
                                </div>
                              </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <div class="text-sm text-gray-900">Internet Service Provider License</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <div class="text-sm text-gray-900">2025-03-11</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <div class="text-sm text-gray-900">2025-04-11</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <div class="text-sm text-gray-900">20,000,000.00</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <span
                                class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Paid</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <a href="#" class="inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 hover:text-white bg-white border border-primary rounded-full hover:bg-primary transition transform hover:translate-x-1 after:content-['_↗']">View</a>
                              <a href="#" class="inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 hover:text-white bg-white border border-primary rounded-full hover:bg-primary transition transform hover:translate-x-1 after:content-['_↗']">Download</a>
                            </td>
                          </tr>
                          <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <div class="text-sm font-medium text-gray-900">INV-2025-002</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <div class="flex items-center">
                                <div>
                                  <div class="text-sm font-medium text-gray-900">Acme Corporation</div>
                                  <div class="text-sm text-gray-500"><EMAIL></div>
                                </div>
                              </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <div class="text-sm text-gray-900">Radio Broadcasting License</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <div class="text-sm text-gray-900">2024-12-25</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <div class="text-sm text-gray-900">2025-01-25</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <div class="text-sm text-gray-900">6,565,000.00</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <span
                                class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Pending</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <a href="#" class="inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 hover:text-white bg-white border border-primary rounded-full hover:bg-primary transition transform hover:translate-x-1 after:content-['_↗']">View</a>
                              <a href="#" class="inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 hover:text-white bg-white border border-primary rounded-full hover:bg-primary transition transform hover:translate-x-1 after:content-['_↗']">Download</a>
                            </td>
                          </tr>
                          <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <div class="text-sm font-medium text-gray-900">INV-2025-003</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <div class="flex items-center">
                                <div>
                                  <div class="text-sm font-medium text-gray-900">Acme Corporation</div>
                                  <div class="text-sm text-gray-500"><EMAIL></div>
                                </div>
                              </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <div class="text-sm text-gray-900">TV Broadcasting License</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <div class="text-sm text-gray-900">2025-01-01</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <div class="text-sm text-gray-900">2025-02-01</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <div class="text-sm text-gray-900">50,000,000.00</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <span
                                class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Overdue</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <a href="#" class="inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 hover:text-white bg-white border border-primary rounded-full hover:bg-primary transition transform hover:translate-x-1 after:content-['_↗']">View</a>
                              <a href="#" class="inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 hover:text-white bg-white border border-primary rounded-full hover:bg-primary transition transform hover:translate-x-1 after:content-['_↗']">Download</a>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
              <!-- Pagination -->
              <div class="bg-white px-4 py-2 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <div class="flex-1 flex justify-between sm:hidden">
                  <a href="#"
                    class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Previous
                  </a>
                  <a href="#"
                    class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Next
                  </a>
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                  <div>
                    <p class="text-sm text-gray-700">
                      Showing <span class="font-medium">1</span> to <span class="font-medium">3</span> of <span
                        class="font-medium"> 3 </span> payments
                    </p>
                  </div>
                  <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                      <a href="#"
                        class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <span class="sr-only">Previous</span>
                        <i class="ri-arrow-left-s-line"></i>
                      </a>
                      <a href="#" aria-current="page"
                        class="z-10 bg-primary bg-opacity-10 border-primary text-primary relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                        1
                      </a>
                      <a href="#"
                        class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                        2
                      </a>
                      <a href="#"
                        class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                        3
                      </a>
                      <a href="#"
                        class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <span class="sr-only">Next</span>
                        <i class="ri-arrow-right-s-line"></i>
                      </a>
                    </nav>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- Invoice Modal -->
        <div id="invoiceModal" class="modal">
          <div class="modal-content">
            <span class="modal-close" onclick="closeInvoiceModal()">×</span>
            <h2 class="text-xl font-medium text-gray-900 border-b pb-2 mb-4">Create New Invoice</h2>
            <div class="bg-white shadow overflow-hidden sm:rounded-md">
              <div class="px-4 py-5 sm:p-6">
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 gap-y-6 sm:grid-cols-2 sm:gap-x-6">
                  <div class="col-span-1">
                    <label for="client-name" class="block text-sm font-medium text-gray-700 pb-2">Client Name</label>
                    <input type="text" id="client-name" name="client-name" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:ring-primary focus:border-primary sm:text-sm" placeholder="Type here.." />
                  </div>
                  <div class="col-span-1">
                    <label for="client-email" class="block text-sm font-medium text-gray-700 pb-2">Client Email</label>
                    <input type="email" id="client-email" name="client-email" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:ring-primary focus:border-primary sm:text-sm" placeholder="Type here.." />
                  </div>
                  <div class="col-span-1">
                    <label for="invoice-number" class="block text-sm font-medium text-gray-700 pb-2">Invoice Number</label>
                    <input type="text" id="invoice-number" name="invoice-number" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:ring-primary focus:border-primary sm:text-sm" placeholder="Type here.." />
                  </div>
                  <div class="col-span-1">
                    <label for="invoice-date" class="block text-sm font-medium text-gray-700 pb-2">Invoice Date</label>
                    <input type="date" id="invoice-date" name="invoice-date" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:ring-primary focus:border-primary sm:text-sm" placeholder="Type here.." />
                  </div>
                  <div class="col-span-1">
                    <label for="due-date" class="block text-sm font-medium text-gray-700 pb-2">Due Date</label>
                    <input type="date" id="due-date" name="due-date" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:ring-primary focus:border-primary sm:text-sm" placeholder="Type here.." />
                  </div>
                  <div class="col-span-1">
                    <label for="quantity" class="block text-sm font-medium text-gray-700 pb-2">Resource</label>
                    <input type="number" id="quantity" name="quantity" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:ring-primary focus:border-primary sm:text-sm" placeholder="Type here.." />
                  </div>
                  <div class="col-span-1">
                    <label for="price" class="block text-sm font-medium text-gray-700 pb-2">Price (<strong>MWK</strong>)</label>
                    <input type="number" id="price" name="price" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:ring-primary focus:border-primary sm:text-sm" placeholder="Type here.." />
                  </div>
                  <div class="col-span-1">
                    <label for="total-amount" class="block text-sm font-medium text-gray-700 pb-2">Total Amount (<strong>MWK</strong>)</label>
                    <input type="number" id="total-amount" name="total-amount" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:ring-primary focus:border-primary sm:text-sm" placeholder="Calculated total" readonly />
                  </div>
                  <div class="sm:col-span-2">
                    <label for="notes" class="block text-sm font-medium text-gray-700 pb-2">Notes</label>
                    <textarea id="notes" name="notes" rows="4" class="mt-1 block w-full rounded-md border-gray-300 p-2 shadow-sm focus:ring-primary focus:border-primary sm:text-sm enhanced-input"
                      placeholder="Enter any additional notes"></textarea>
                  </div>
                </div>
              </div>
            </div>
            <div class="mt-4 flex justify-end gap-4">
              <div class="relative">
                <button type="button" onclick="closeInvoiceModal()"
                  class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all">
                  Cancel
                </button>
              </div>
              <div class="relative">
                <button type="button" onclick="saveInvoice()"
                  class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                  Save Invoice
                </button>
              </div>
            </div>
          </div>
        </div>
        <!-- Export Modal -->
        <div id="exportModal" class="modal">
          <div class="modal-content" style="max-width: 900px;">
            <span class="modal-close" onclick="closeExportModal()">×</span>

            <div class="mb-4 flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
              <div>
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Export My Invoices</h2>
                <p class="text-sm text-gray-500 mb-4">
                  Review your invoice data below and click "Export to CSV" to download the file.
                </p>
              </div>
              <div class="flex justify-end sm:justify-center mb-4">
                <div class="relative">
                  <button id="exportCsvBtn" onclick="exportTableToCSV('customer_invoices.csv')"
                  class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                    <div class="w-5 h-5 flex items-center justify-center mr-2">
                      <i class="ri-download-line"></i>
                    </div>
                    Export to CSV
                  </button>
                </div>

              </div>
            </div>
            <div class="overflow-x-auto">
              <table id="exportTable" class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th scope="col"
                      class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Invoice #
                    </th>
                    <th scope="col"
                      class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Client Name
                    </th>
                    <th scope="col"
                      class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Email
                    </th>
                    <th scope="col"
                      class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      License Category
                    </th>
                    <th scope="col"
                      class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Issue Date
                    </th>
                    <th scope="col"
                      class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Due Date
                    </th>
                    <th scope="col"
                      class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Amount (<strong>MWK</strong>)
                    </th>
                    <th scope="col"
                      class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm font-medium text-gray-900">INV-2025-001</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm font-medium text-gray-900">Acme Corporation</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-500"><EMAIL></div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">Internet Service Provider License</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">2025-03-11</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">2025-04-11</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">20,000,000.00</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span
                        class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Paid</span>
                    </td>
                  </tr>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm font-medium text-gray-900">INV-2025-002</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm font-medium text-gray-900">Acme Corporation</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-500"><EMAIL></div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">Radio Broadcasting License</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">2024-12-25</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">2025-01-25</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">6,565,000.00</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span
                        class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Pending</span>
                    </td>
                  </tr>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm font-medium text-gray-900">INV-2025-003</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm font-medium text-gray-900">Acme Corporation</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-500"><EMAIL></div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">TV Broadcasting License</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">2025-01-01</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">2025-02-01</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">50,000,000.00</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span
                        class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Overdue</span>
                    </td>
                  </tr>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm font-medium text-gray-900">INV-2023-001</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm font-medium text-gray-900">Acme Corporation</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-500"><EMAIL></div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">Mobile Network License</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">2023-10-01</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">2023-10-31</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">2,500,000.00</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span
                        class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Paid</span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <!-- Receive Payment Modal -->
        <div id="paymentModal" class="modal">
          <div class="modal-content">
            <span class="modal-close" onclick="closePaymentModal()">×</span>
            <h2 class="text-xl font-medium text-gray-900 border-b pb-2 mb-4">Create Payment Record</h2>
            <div class="bg-white shadow overflow-hidden sm:rounded-md">
              <div class="px-4 py-5 sm:p-6">
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 gap-y-6 sm:grid-cols-2 sm:gap-x-6">
                  <div class="col-span-1">
                    <label for="payment-invoice-number" class="block text-sm font-medium text-gray-700 pb-2">Invoice
                      Number</label>
                    <input type="text" id="payment-invoice-number" name="payment-invoice-number"
                      class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:ring-primary focus:border-primary sm:text-sm" placeholder="Type here.." />
                  </div>
                  <div class="col-span-1">
                    <label for="payment-client-name" class="block text-sm font-medium text-gray-700 pb-2">Client Name</label>
                    <input type="text" id="payment-client-name" name="payment-client-name" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:ring-primary focus:border-primary sm:text-sm" placeholder="Type here.." />
                  </div>
                  <div class="col-span-1">
                    <label for="payment-client-email" class="block text-sm font-medium text-gray-700 pb-2">Client
                      Email</label>
                    <input type="email" id="payment-client-email" name="payment-client-email"
                      class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:ring-primary focus:border-primary sm:text-sm" placeholder="Type here.." />
                  </div>
                  <div class="col-span-1">
                    <label for="payment-amount" class="block text-sm font-medium text-gray-700 pb-2">Payment Amount
                      (<strong>MWK</strong>)</label>
                    <input type="number" id="payment-amount" name="payment-amount" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:ring-primary focus:border-primary sm:text-sm" placeholder="Type here.." />
                  </div>
                  <div class="col-span-1">
                    <label for="payment-date" class="block text-sm font-medium text-gray-700 pb-2">Payment Date</label>
                    <input type="date" id="payment-date" name="payment-date" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:ring-primary focus:border-primary sm:text-sm" placeholder="Type here.." />
                  </div>
                  <div class="col-span-1">
                    <label for="payment-method" class="block text-sm font-medium text-gray-700 pb-2">Payment Method</label>
                    <select id="payment-method" name="payment-method" class="mt-1 block w-full rounded-md border-gray-300 p-2 shadow-sm focus:ring-primary focus:border-primary sm:text-sm">
                      <option value="">Select payment method</option>
                      <option value="bank-transfer">Bank Transfer</option>
                      <option value="credit-card">Credit Card</option>
                      <option value="cash">Cash</option>
                      <option value="mobile-money">Mobile Money</option>
                    </select>
                  </div>
                  <div class="sm:col-span-2">
                    <label for="payment-notes" class="block text-sm font-medium text-gray-700 pb-2">Notes</label>
                    <textarea id="payment-notes" name="payment-notes" rows="4" class="mt-1 block w-full rounded-md border-gray-300 p-2 shadow-sm focus:ring-primary focus:border-primary sm:text-sm enhanced-input"
                      placeholder="Enter any additional notes"></textarea>
                  </div>
                </div>
              </div>
            </div>
            <div class="mt-4 flex justify-end gap-4">
              <div class="relative">
                <button type="button" onclick="closePaymentModal()"
                  class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all">
                  Cancel
                </button>
              </div>
              <div class="relative">
                <button type="button" onclick="savePayment()"
                  class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                  Save Payment
                </button>
              </div>
            </div>
          </div>
        </div>
        <!-- Export Modal -->
        <div id="paymentExportModal" class="modal">
          <div class="modal-content" style="max-width: 900px;">
            <span class="modal-close" onclick="closePaymentExportModal()">×</span>

            <div class="mb-4 flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
              <div>
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Export My Payment Records</h2>
                <p class="text-sm text-gray-500 mb-4">
                  Review your payment data below and click "Export to CSV" to download the file.
                </p>
              </div>
              <div class="flex justify-end sm:justify-center mb-4">
                <div class="relative">
                  <button id="exportCsvBtn" onclick="exportTableToCSV('customer_invoices.csv')"
                  class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                    <div class="w-5 h-5 flex items-center justify-center mr-2">
                      <i class="ri-download-line"></i>
                    </div>
                    Export to CSV
                  </button>
                </div>

              </div>
            </div>
            <div class="overflow-x-auto">
              <table id="exportTable" class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th scope="col"
                      class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Invoice #
                    </th>
                    <th scope="col"
                      class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Client Name
                    </th>
                    <th scope="col"
                      class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Email
                    </th>
                    <th scope="col"
                      class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      License Category
                    </th>
                    <th scope="col"
                      class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Issue Date
                    </th>
                    <th scope="col"
                      class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Due Date
                    </th>
                    <th scope="col"
                      class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Amount (<strong>MWK</strong>)
                    </th>
                    <th scope="col"
                      class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm font-medium text-gray-900">INV-2025-001</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm font-medium text-gray-900">Acme Corporation</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-500"><EMAIL></div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">Internet Service Provider License</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">2025-03-11</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">2025-04-11</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">20,000,000.00</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span
                        class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Paid</span>
                    </td>
                  </tr>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm font-medium text-gray-900">INV-2025-002</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm font-medium text-gray-900">Acme Corporation</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-500"><EMAIL></div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">Radio Broadcasting License</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">2024-12-25</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">2025-01-25</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">6,565,000.00</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span
                        class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Pending</span>
                    </td>
                  </tr>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm font-medium text-gray-900">INV-2025-003</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm font-medium text-gray-900">Acme Corporation</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-500"><EMAIL></div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">TV Broadcasting License</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">2025-01-01</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">2025-02-01</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">50,000,000.00</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span
                        class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Overdue</span>
                    </td>
                  </tr>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm font-medium text-gray-900">INV-2023-001</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm font-medium text-gray-900">Acme Corporation</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-500"><EMAIL></div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">Mobile Network License</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">2023-10-01</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">2023-10-31</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">2,500,000.00</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span
                        class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Paid</span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- View Invoices Modal -->
        <div id="viewInvoicesModal" class="modal">
          <div class="modal-content" style="max-width: 1000px;">
            <span class="modal-close" onclick="closeViewInvoicesModal()">&times;</span>
            <div class="mb-4">
              <div>
                <h2 class="text-xl font-semibold text-gray-900 mb-2">My Invoices</h2>
                <p class="text-sm text-gray-500">
                  View your invoices and click on "View" to see individual invoice details or "Download" to get the PDF.
                </p>
              </div>
            </div>

            <!-- Customer Invoices Table -->
            <div class="overflow-x-auto">
              <table id="customerInvoicesTable" class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Invoice #
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Issue Date
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Due Date
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      License Category
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Amount (MWK)
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm font-medium text-gray-900">INV-2025-001</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">2025-03-11</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">2025-04-11</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">Internet Service Provider License</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">20,000,000.00</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Paid</span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <a href="customer-invoice.html" target="_blank" class="text-primary hover:text-primary-dark mr-3">View</a>
                      <a href="customer-invoice.html" download class="text-gray-600 hover:text-gray-900">Download</a>
                    </td>
                  </tr>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm font-medium text-gray-900">INV-2025-002</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">2024-12-25</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">2025-01-25</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">Radio Broadcasting License</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">6,565,000.00</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Pending</span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <a href="customer-invoice.html" target="_blank" class="text-primary hover:text-primary-dark mr-3">View</a>
                      <a href="customer-invoice.html" download class="text-gray-600 hover:text-gray-900">Download</a>
                    </td>
                  </tr>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm font-medium text-gray-900">INV-2025-003</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">2025-01-01</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">2025-02-01</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">TV Broadcasting License</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">50,000,000.00</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Overdue</span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <a href="customer-invoice.html" target="_blank" class="text-primary hover:text-primary-dark mr-3">View</a>
                      <a href="customer-invoice.html" download class="text-gray-600 hover:text-gray-900">Download</a>
                    </td>
                  </tr>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm font-medium text-gray-900">INV-2023-001</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">2023-10-01</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">2023-10-31</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">Mobile Network License</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">2,500,000.00</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Paid</span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <a href="customer-invoice.html" target="_blank" class="text-primary hover:text-primary-dark mr-3">View</a>
                      <a href="customer-invoice.html" download class="text-gray-600 hover:text-gray-900">Download</a>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>
  <script>
    // Existing JavaScript functions remain unchanged
    function toggleMobileSidebar() {
      const sidebar = document.getElementById('sidebar');
      const overlay = document.getElementById('mobileSidebarOverlay');
      sidebar.classList.toggle('mobile-sidebar-open');
      overlay.classList.toggle('show');
    }

    function toggleDropdown() {
      const dropdown = document.getElementById('userDropdown');
      dropdown.classList.toggle('show');
    }

    function showTab(tab) {
      const invoicesTab = document.getElementById('invoices-tab');
      const paymentsTab = document.getElementById('payments-tab');
      const invoicesContent = document.getElementById('invoices-content');
      const paymentsContent = document.getElementById('payments-content');

      if (tab === 'invoices') {
        invoicesTab.classList.add('active');
        paymentsTab.classList.remove('active');
        invoicesContent.classList.remove('hidden');
        paymentsContent.classList.add('hidden');
      } else {
        invoicesTab.classList.remove('active');
        paymentsTab.classList.add('active');
        invoicesContent.classList.add('hidden');
        paymentsContent.classList.remove('hidden');
      }
    }

    function openInvoiceModal() {
      document.getElementById('invoiceModal').classList.add('show');
    }

    function closeInvoiceModal() {
      document.getElementById('invoiceModal').classList.remove('show');
    }

    function saveInvoice() {
      console.log('Saving invoice...');
      closeInvoiceModal();
    }

    function showExportModal() {
      document.getElementById('exportModal').classList.add('show');
    }

    function closeExportModal() {
      document.getElementById('exportModal').classList.remove('show');
    }

    function exportTableToCSV(filename) {
      const table = document.getElementById('exportTable');
      const rows = table.querySelectorAll('tr');
      let csv = [];

      for (let i = 0; i < rows.length; i++) {
        const row = [];
        const cols = rows[i].querySelectorAll('td, th');

        for (let j = 0; j < cols.length; j++) {
          let text = cols[j].innerText.replace(/"/g, '""');
          if (cols[j].querySelector('span')) {
            text = cols[j].querySelector('span').innerText;
          }
          row.push('"' + text + '"');
        }

        csv.push(row.join(','));
      }

      const csvFile = new Blob([csv.join('\n')], { type: 'text/csv' });
      const downloadLink = document.createElement('a');
      downloadLink.download = filename;
      downloadLink.href = window.URL.createObjectURL(csvFile);
      downloadLink.style.display = 'none';
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);
    }

    // New JavaScript functions for Payment Modal
    function openPaymentModal() {
      document.getElementById('paymentModal').classList.add('show');
    }

    function closePaymentModal() {
      document.getElementById('paymentModal').classList.remove('show');
    }

    function savePayment() {
      const paymentData = {
        invoiceNumber: document.getElementById('payment-invoice-number').value,
        clientName: document.getElementById('payment-client-name').value,
        clientEmail: document.getElementById('payment-client-email').value,
        paymentAmount: document.getElementById('payment-amount').value,
        paymentDate: document.getElementById('payment-date').value,
        paymentMethod: document.getElementById('payment-method').value,
        notes: document.getElementById('payment-notes').value,
      };
      console.log('Saving payment:', paymentData);
      closePaymentModal();
    }

    function showPaymentExportModal() {
      document.getElementById('paymentExportModal').classList.add('show');
    }
    function closePaymentExportModal() {
      document.getElementById('paymentExportModal').classList.remove('show');
    }

    // View Invoices Modal Functions
    function openViewInvoicesModal() {
      document.getElementById('viewInvoicesModal').classList.add('show');
    }

    function closeViewInvoicesModal() {
      document.getElementById('viewInvoicesModal').classList.remove('show');
    }

    // Close modals when clicking outside
    window.onclick = function (event) {
      if (event.target.classList.contains('modal')) {
        closeInvoiceModal();
        closeExportModal();
        closePaymentModal();
        closePaymentExportModal();
        closeViewInvoicesModal();
      }
    };
  </script>
</body>

</html>