<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Export Financial Transactions - MACRA</title>
  <script src="https://cdn.tailwindcss.com/3.4.16"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: { primary: "#e02b20", secondary: "#6366f1" },
          borderRadius: {
            none: "0px",
            sm: "4px",
            DEFAULT: "8px",
            md: "12px",
            lg: "16px",
            xl: "20px",
            "2xl": "24px",
            "3xl": "32px",
            full: "9999px",
            button: "8px",
          },
        },
      },
    };
  </script>
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css" />
  <style>
    body {
      font-family: 'Inter', sans-serif;
      background-color: #f9fafb;
    }

    .export-card {
      transition: all 0.3s ease;
      border: 2px solid transparent;
    }

    .export-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
      border-color: #e02b20;
    }

    .export-card.selected {
      border-color: #e02b20;
      background-color: #fef2f2;
    }

    .progress-bar {
      transition: width 0.3s ease;
    }

    .dropdown-content {
      display: none;
      position: absolute;
      right: 0;
      top: 100%;
      z-index: 50;
    }

    .dropdown-content.show {
      display: block;
    }

    .side-nav {
      overflow: auto;
      -ms-overflow-style: none;
      height: 75vh;
    }

    .side-nav::-webkit-scrollbar {
      display: none;
    }

    .side-nav {
      scrollbar-width: none;
    }

    @media (max-width: 768px) {
      .mobile-sidebar-open {
        display: block !important;
        position: fixed;
        z-index: 50;
        height: 100vh;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
      }

      .mobile-sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 40;
        display: none;
      }

      .mobile-sidebar-overlay.show {
        display: block;
      }
    }
  </style>
</head>

<body>
  <div class="flex h-screen overflow-hidden">
    <!-- Mobile sidebar overlay -->
    <div id="mobileSidebarOverlay" class="mobile-sidebar-overlay"></div>

    <!-- Sidebar -->
    <aside id="sidebar" class="w-64 bg-white shadow-lg flex-shrink-0 hidden md:block">
      <div class="h-16 flex items-center px-6 border-b">
        <div class="flex items-center">
          <img src="../images/macra-logo.png" alt="Logo" class="max-h-12 w-auto">
        </div>
      </div>
      <nav class="mt-6 px-4 side-nav">
        <div class="space-y-1">
          <a href="../index.html" class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <i class="ri-dashboard-line"></i>
            </div>
            Dashboard
          </a>
          <a
              href="license/license-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-mail-send-line"></i>
              </div>
              Postal
            </a>

           <a
              href="spectrum/spectrum-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
             <i class="ri-signal-tower-line"></i>
              </div>
              Telecommunications
            </a>

              <a
              href="standards/standards-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-stack-line"></i>
              </div>
            Standards
            </a>

            <a
              href="clf/clf-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
               <i class="ri-collage-line"></i>
              </div>
            CLF
            </a>

              <a
              href="procurement/procurement-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
               <i class="ri-shopping-bag-line"></i>
              </div>
            Procurement
            </a>

            <a
              href="financial/accounts-finance.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                </svg>
              </div>   Accounts & Finance
          </a>
          <a href="../reports/reports.html" class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 7.5h1.5m-1.5 3h1.5m-7.5 3h7.5m-7.5 3h7.5m3-9h3.375c.621 0 1.125.504 1.125 1.125V18a2.25 2.25 0 0 1-2.25 2.25M16.5 7.5V18a2.25 2.25 0 0 0 2.25 2.25M16.5 7.5V4.875c0-.621-.504-1.125-1.125-1.125H4.125C3.504 3.75 3 4.254 3 4.875V18a2.25 2.25 0 0 0 2.25 2.25h13.5M6 7.5h3v3H6v-3Z" />
              </svg>
            </div>
            Reports & Analytics
          </a>
        </div>

        <div class="mt-8">
          <h3 class="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider">
            Settings
          </h3>
          <div class="mt-2 space-y-1">
            <a href="../user-management/user-management.html" class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z" />
                </svg>
              </div>
              User Management
            </a>
            <a href="../audit-trail.html" class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-shield-line"></i>
              </div>
              Audit Trail
            </a>
            <a href="../help-support.html" class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-question-line"></i>
              </div>
              Help & Support
            </a>
          </div>
        </div>
      </nav>
      <div class="absolute bottom-0 w-64 p-4 border-t">
        <a href="../user-management/user-profile.html" class="flex items-center hover:bg-gray-50 p-2 rounded-md">
          <img class="h-10 w-10 rounded-full" src="https://banner2.cleanpng.com/********/sac/9128e319522f66aba9edf95180a86e7e.webp" alt="Profile" />
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-900">Sarah Williams</p>
            <p class="text-xs text-gray-500">Finance Manager</p>
          </div>
        </a>
      </div>
    </aside>

    <!-- Main content -->
    <div class="flex-1 flex flex-col overflow-hidden">
      <!-- Top header -->
      <header class="bg-white shadow-sm z-10">
        <div class="flex items-center justify-between h-16 px-4 sm:px-6">
          <button id="mobileMenuBtn" type="button" onclick="toggleMobileSidebar()" class="md:hidden text-gray-500 hover:text-gray-700 focus:outline-none">
            <div class="w-6 h-6 flex items-center justify-center">
              <i class="ri-menu-line ri-lg"></i>
            </div>
          </button>
          <div class="flex-1 flex items-center justify-center px-2 lg:ml-6 lg:justify-start">
            <div class="max-w-lg w-full">
              <label for="search" class="sr-only">Search</label>
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <div class="w-5 h-5 flex items-center justify-center text-gray-400">
                    <i class="ri-search-line"></i>
                  </div>
                </div>
                <input id="search" name="search" class="block w-full pl-10 pr-3 py-3 border-2 border-gray-300 rounded-md leading-5 bg-gray-50 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary text-sm hover:bg-white transition-colors" placeholder="Search transactions..." type="search" />
              </div>
            </div>
          </div>
          <div class="flex items-center">
            <button type="button" class="flex-shrink-0 p-1 mr-4 text-gray-500 rounded-full hover:text-gray-700 focus:outline-none relative">
              <span class="sr-only">View notifications</span>
              <div class="w-6 h-6 flex items-center justify-center">
                <i class="ri-notification-3-line ri-lg"></i>
              </div>
              <span class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white"></span>
            </button>
            <div class="dropdown relative">
              <button type="button" onclick="toggleDropdown()" class="flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                <span class="sr-only">Open user menu</span>
                <img class="h-8 w-8 rounded-full" src="https://banner2.cleanpng.com/********/sac/9128e319522f66aba9edf95180a86e7e.webp" alt="Profile" />
              </button>
              <div id="userDropdown" class="dropdown-content mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
                <div class="py-1">
                  <a href="../user-management/user-profile.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Your Profile</a>
                  <a href="../account-settings.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Settings</a>
                  <a href="../auth/login.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Sign out</a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      <!-- Main content area -->
      <main class="flex-1 overflow-y-auto bg-gray-50 p-4 sm:p-6">
        <div class="max-w-7xl mx-auto">
          <!-- Page header -->
          <div class="mb-6">
            <div class="flex items-center justify-between">
              <div>
                <h1 class="text-2xl font-semibold text-gray-900">Export Financial Transactions</h1>
                <p class="mt-1 text-sm text-gray-500">
                  Generate and download comprehensive financial reports including payments, invoices, and transaction records.
                </p>
              </div>
              <div class="flex space-x-3">
                <button onclick="showExportHistory()" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                  <i class="ri-history-line mr-2"></i>
                  Export History
                </button>
                <button onclick="scheduleExport()" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                  <i class="ri-calendar-schedule-line mr-2"></i>
                  Schedule Export
                </button>
              </div>
            </div>
          </div>

          <!-- Export Categories -->
          <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            <!-- Transaction Records Export -->
            <div class="export-card bg-white rounded-lg shadow-md p-6 cursor-pointer" onclick="selectExportType('transactions')">
              <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                  <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6 text-green-600">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 18.75a60.07 60.07 0 0 1 15.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 0 1 3 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H3.75m0 0h-.375M3 6v.75c0 .414.336.75.75.75h.75m0-1.5h.375c.621 0 1.125.504 1.125 1.125v.375M3.75 6H3m0 0h.375A.375.375 0 0 1 3.75 6.375V6M3 6h.75A.75.75 0 0 1 4.5 6.75v.75m0-1.5H4.5m0 0h.375c.621 0 1.125.504 1.125 1.125V6m-1.5 0V6.375c0 .621.504 1.125 1.125 1.125H4.5m0-1.5V6c0-.621.504-1.125 1.125-1.125H4.5m0 0h.375c.621 0 1.125.504 1.125 1.125v.375M4.5 6v.75c0 .414.336.75.75.75H6m0-1.5h.375c.621 0 1.125.504 1.125 1.125V6m-1.5 0V6.375c0 .621.504 1.125 1.125 1.125H6m0-1.5V6c0-.621.504-1.125 1.125-1.125H6m0 0h.375c.621 0 1.125.504 1.125 1.125v.375M6 6v.75c0 .414.336.75.75.75h.75m0-1.5h.375c.621 0 1.125.504 1.125 1.125V6m-1.5 0V6.375c0 .621.504 1.125 1.125 1.125H7.5m0-1.5V6c0-.621.504-1.125 1.125-1.125H7.5" />
                    </svg>
                  </div>
                  <div class="ml-4">
                    <h3 class="text-lg font-medium text-gray-900">Transaction Records</h3>
                    <p class="text-sm text-gray-500">All payment transactions and transfers</p>
                  </div>
                </div>
                <div class="text-right">
                  <span class="text-2xl font-bold text-green-600">4,892</span>
                  <p class="text-xs text-gray-500">Total Records</p>
                </div>
              </div>
              <div class="space-y-2">
                <div class="flex justify-between text-sm">
                  <span class="text-gray-600">Completed Payments</span>
                  <span class="font-medium">3,456 payments</span>
                </div>
                <div class="flex justify-between text-sm">
                  <span class="text-gray-600">Pending Transactions</span>
                  <span class="font-medium text-yellow-600">234 pending</span>
                </div>
                <div class="flex justify-between text-sm">
                  <span class="text-gray-600">Last Updated</span>
                  <span class="font-medium">15 min ago</span>
                </div>
              </div>
            </div>

            <!-- Invoice Data Export -->
            <div class="export-card bg-white rounded-lg shadow-md p-6 cursor-pointer" onclick="selectExportType('invoices')">
              <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                  <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6 text-blue-600">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z" />
                    </svg>
                  </div>
                  <div class="ml-4">
                    <h3 class="text-lg font-medium text-gray-900">Invoice Data</h3>
                    <p class="text-sm text-gray-500">Customer invoices and billing records</p>
                  </div>
                </div>
                <div class="text-right">
                  <span class="text-2xl font-bold text-blue-600">2,156</span>
                  <p class="text-xs text-gray-500">Total Records</p>
                </div>
              </div>
              <div class="space-y-2">
                <div class="flex justify-between text-sm">
                  <span class="text-gray-600">Paid Invoices</span>
                  <span class="font-medium">1,789 invoices</span>
                </div>
                <div class="flex justify-between text-sm">
                  <span class="text-gray-600">Outstanding</span>
                  <span class="font-medium text-red-600">367 overdue</span>
                </div>
                <div class="flex justify-between text-sm">
                  <span class="text-gray-600">Last Updated</span>
                  <span class="font-medium">30 min ago</span>
                </div>
              </div>
            </div>

            <!-- Financial Reports Export -->
            <div class="export-card bg-white rounded-lg shadow-md p-6 cursor-pointer" onclick="selectExportType('reports')">
              <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                  <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6 text-purple-600">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z" />
                    </svg>
                  </div>
                  <div class="ml-4">
                    <h3 class="text-lg font-medium text-gray-900">Financial Reports</h3>
                    <p class="text-sm text-gray-500">Summary reports and analytics</p>
                  </div>
                </div>
                <div class="text-right">
                  <span class="text-2xl font-bold text-purple-600">847</span>
                  <p class="text-xs text-gray-500">Total Records</p>
                </div>
              </div>
              <div class="space-y-2">
                <div class="flex justify-between text-sm">
                  <span class="text-gray-600">Monthly Reports</span>
                  <span class="font-medium">24 reports</span>
                </div>
                <div class="flex justify-between text-sm">
                  <span class="text-gray-600">Annual Summaries</span>
                  <span class="font-medium">3 summaries</span>
                </div>
                <div class="flex justify-between text-sm">
                  <span class="text-gray-600">Last Updated</span>
                  <span class="font-medium">1 hour ago</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Export Configuration -->
          <div id="exportConfig" class="bg-white rounded-lg shadow-md p-6 mb-6" style="display: none;">
            <h3 class="text-lg font-medium text-gray-900 mb-6">Export Configuration</h3>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <!-- Left Column - Data Selection -->
              <div>
                <h4 class="text-md font-medium text-gray-900 mb-4">Data Selection</h4>

                <!-- Date Range -->
                <div class="mb-6">
                  <label class="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
                  <div class="grid grid-cols-2 gap-4">
                    <div>
                      <label class="block text-xs text-gray-500 mb-1">From</label>
                      <input type="date" id="startDate" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary text-sm">
                    </div>
                    <div>
                      <label class="block text-xs text-gray-500 mb-1">To</label>
                      <input type="date" id="endDate" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary text-sm">
                    </div>
                  </div>
                </div>

                <!-- Data Categories -->
                <div class="mb-6">
                  <label class="block text-sm font-medium text-gray-700 mb-3">Data Categories</label>
                  <div id="transactionCategories" class="space-y-3" style="display: none;">
                    <label class="flex items-center">
                      <input type="checkbox" checked class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                      <span class="ml-3 text-sm text-gray-700">Payment Transactions</span>
                    </label>
                    <label class="flex items-center">
                      <input type="checkbox" checked class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                      <span class="ml-3 text-sm text-gray-700">Transfer Records</span>
                    </label>
                    <label class="flex items-center">
                      <input type="checkbox" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                      <span class="ml-3 text-sm text-gray-700">Refund Transactions</span>
                    </label>
                    <label class="flex items-center">
                      <input type="checkbox" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                      <span class="ml-3 text-sm text-gray-700">Fee Collections</span>
                    </label>
                  </div>
                  <div id="invoiceCategories" class="space-y-3" style="display: none;">
                    <label class="flex items-center">
                      <input type="checkbox" checked class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                      <span class="ml-3 text-sm text-gray-700">Customer Invoices</span>
                    </label>
                    <label class="flex items-center">
                      <input type="checkbox" checked class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                      <span class="ml-3 text-sm text-gray-700">Payment Records</span>
                    </label>
                    <label class="flex items-center">
                      <input type="checkbox" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                      <span class="ml-3 text-sm text-gray-700">Credit Notes</span>
                    </label>
                    <label class="flex items-center">
                      <input type="checkbox" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                      <span class="ml-3 text-sm text-gray-700">Tax Information</span>
                    </label>
                  </div>
                  <div id="reportCategories" class="space-y-3" style="display: none;">
                    <label class="flex items-center">
                      <input type="checkbox" checked class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                      <span class="ml-3 text-sm text-gray-700">Revenue Reports</span>
                    </label>
                    <label class="flex items-center">
                      <input type="checkbox" checked class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                      <span class="ml-3 text-sm text-gray-700">Expense Reports</span>
                    </label>
                    <label class="flex items-center">
                      <input type="checkbox" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                      <span class="ml-3 text-sm text-gray-700">Cash Flow Analysis</span>
                    </label>
                    <label class="flex items-center">
                      <input type="checkbox" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                      <span class="ml-3 text-sm text-gray-700">Profit & Loss</span>
                    </label>
                  </div>
                </div>

                <!-- Filters -->
                <div class="mb-6">
                  <label class="block text-sm font-medium text-gray-700 mb-3">Filters</label>
                  <div class="space-y-4">
                    <div>
                      <label class="block text-xs text-gray-500 mb-1">Amount Range (MWK)</label>
                      <div class="grid grid-cols-2 gap-2">
                        <input type="number" placeholder="Min Amount" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary text-sm">
                        <input type="number" placeholder="Max Amount" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary text-sm">
                      </div>
                    </div>
                    <div>
                      <label class="block text-xs text-gray-500 mb-1">Transaction Status</label>
                      <select class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary text-sm">
                        <option value="">All Statuses</option>
                        <option value="completed">Completed</option>
                        <option value="pending">Pending</option>
                        <option value="failed">Failed</option>
                        <option value="cancelled">Cancelled</option>
                      </select>
                    </div>
                    <div>
                      <label class="block text-xs text-gray-500 mb-1">Payment Method</label>
                      <select class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary text-sm">
                        <option value="">All Methods</option>
                        <option value="bank_transfer">Bank Transfer</option>
                        <option value="mobile_money">Mobile Money</option>
                        <option value="credit_card">Credit Card</option>
                        <option value="cash">Cash</option>
                      </select>
                    </div>
                    <div>
                      <label class="block text-xs text-gray-500 mb-1">Customer Type</label>
                      <select class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary text-sm">
                        <option value="">All Customers</option>
                        <option value="individual">Individual</option>
                        <option value="business">Business</option>
                        <option value="government">Government</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Right Column - Export Options -->
              <div>
                <h4 class="text-md font-medium text-gray-900 mb-4">Export Options</h4>

                <!-- File Format -->
                <div class="mb-6">
                  <label class="block text-sm font-medium text-gray-700 mb-3">File Format</label>
                  <div class="grid grid-cols-2 gap-3">
                    <label class="flex items-center p-3 border border-gray-300 rounded-md cursor-pointer hover:bg-gray-50">
                      <input type="radio" name="format" value="excel" checked class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                      <div class="ml-3">
                        <div class="text-sm font-medium text-gray-900">Excel (.xlsx)</div>
                        <div class="text-xs text-gray-500">Spreadsheet format</div>
                      </div>
                    </label>
                    <label class="flex items-center p-3 border border-gray-300 rounded-md cursor-pointer hover:bg-gray-50">
                      <input type="radio" name="format" value="csv" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                      <div class="ml-3">
                        <div class="text-sm font-medium text-gray-900">CSV (.csv)</div>
                        <div class="text-xs text-gray-500">Comma separated</div>
                      </div>
                    </label>
                    <label class="flex items-center p-3 border border-gray-300 rounded-md cursor-pointer hover:bg-gray-50">
                      <input type="radio" name="format" value="pdf" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                      <div class="ml-3">
                        <div class="text-sm font-medium text-gray-900">PDF (.pdf)</div>
                        <div class="text-xs text-gray-500">Report format</div>
                      </div>
                    </label>
                    <label class="flex items-center p-3 border border-gray-300 rounded-md cursor-pointer hover:bg-gray-50">
                      <input type="radio" name="format" value="json" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                      <div class="ml-3">
                        <div class="text-sm font-medium text-gray-900">JSON (.json)</div>
                        <div class="text-xs text-gray-500">API format</div>
                      </div>
                    </label>
                  </div>
                </div>

                <!-- Additional Options -->
                <div class="mb-6">
                  <label class="block text-sm font-medium text-gray-700 mb-3">Additional Options</label>
                  <div class="space-y-3">
                    <label class="flex items-center">
                      <input type="checkbox" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                      <span class="ml-3 text-sm text-gray-700">Include customer details</span>
                    </label>
                    <label class="flex items-center">
                      <input type="checkbox" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                      <span class="ml-3 text-sm text-gray-700">Include transaction fees</span>
                    </label>
                    <label class="flex items-center">
                      <input type="checkbox" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                      <span class="ml-3 text-sm text-gray-700">Include tax calculations</span>
                    </label>
                    <label class="flex items-center">
                      <input type="checkbox" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                      <span class="ml-3 text-sm text-gray-700">Compress file (ZIP)</span>
                    </label>
                  </div>
                </div>

                <!-- Delivery Options -->
                <div class="mb-6">
                  <label class="block text-sm font-medium text-gray-700 mb-3">Delivery Options</label>
                  <div class="space-y-3">
                    <label class="flex items-center">
                      <input type="radio" name="delivery" value="download" checked class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                      <span class="ml-3 text-sm text-gray-700">Direct Download</span>
                    </label>
                    <label class="flex items-center">
                      <input type="radio" name="delivery" value="email" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                      <span class="ml-3 text-sm text-gray-700">Send via Email</span>
                    </label>
                    <label class="flex items-center">
                      <input type="radio" name="delivery" value="secure" class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                      <span class="ml-3 text-sm text-gray-700">Secure Portal Link</span>
                    </label>
                  </div>
                  <div id="emailOptions" class="mt-3" style="display: none;">
                    <input type="email" placeholder="Enter email address" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary text-sm">
                  </div>
                </div>

                <!-- Export Summary -->
                <div class="bg-gray-50 rounded-lg p-4">
                  <h5 class="text-sm font-medium text-gray-900 mb-2">Export Summary</h5>
                  <div class="space-y-1 text-sm text-gray-600">
                    <div class="flex justify-between">
                      <span>Estimated Records:</span>
                      <span id="estimatedRecords" class="font-medium">-</span>
                    </div>
                    <div class="flex justify-between">
                      <span>Estimated Size:</span>
                      <span id="estimatedSize" class="font-medium">-</span>
                    </div>
                    <div class="flex justify-between">
                      <span>Total Value:</span>
                      <span id="estimatedValue" class="font-medium">-</span>
                    </div>
                    <div class="flex justify-between">
                      <span>Processing Time:</span>
                      <span id="estimatedTime" class="font-medium">-</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-end space-x-3 mt-8 pt-6 border-t border-gray-200">
              <button onclick="resetExport()" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                <i class="ri-refresh-line mr-2"></i>
                Reset
              </button>
              <button onclick="previewExport()" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                <i class="ri-eye-line mr-2"></i>
                Preview
              </button>
              <button onclick="startExport()" class="inline-flex items-center px-6 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                <i class="ri-download-line mr-2"></i>
                Start Export
              </button>
            </div>
          </div>

          <!-- Export Progress -->
          <div id="exportProgress" class="bg-white rounded-lg shadow-md p-6 mb-6" style="display: none;">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-medium text-gray-900">Export in Progress</h3>
              <button onclick="cancelExport()" class="text-sm text-red-600 hover:text-red-800">Cancel Export</button>
            </div>

            <div class="mb-4">
              <div class="flex justify-between text-sm text-gray-600 mb-2">
                <span>Processing financial data...</span>
                <span id="progressPercent">0%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div id="progressBar" class="progress-bar bg-primary h-2 rounded-full" style="width: 0%"></div>
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
              <div class="text-center">
                <div class="text-2xl font-bold text-gray-900" id="processedRecords">0</div>
                <div class="text-gray-500">Records Processed</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-gray-900" id="currentSize">0 MB</div>
                <div class="text-gray-500">Current Size</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-gray-900" id="processedValue">MWK 0</div>
                <div class="text-gray-500">Total Value</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-gray-900" id="timeRemaining">--:--</div>
                <div class="text-gray-500">Time Remaining</div>
              </div>
            </div>
          </div>

          <!-- Export History -->
          <div id="exportHistory" class="bg-white rounded-lg shadow-md p-6" style="display: none;">
            <div class="flex items-center justify-between mb-6">
              <h3 class="text-lg font-medium text-gray-900">Export History</h3>
              <button onclick="hideExportHistory()" class="text-sm text-gray-500 hover:text-gray-700">
                <i class="ri-close-line"></i>
              </button>
            </div>

            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Export Type</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date Range</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Format</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Records</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Value</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Transaction Records</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Jan 1 - Jan 31, 2024</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Excel</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">3,456</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">MWK 45.2M</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Completed</span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button class="text-primary hover:text-primary-dark mr-3">Download</button>
                      <button class="text-gray-400 hover:text-gray-600">Delete</button>
                    </td>
                  </tr>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Invoice Data</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Dec 1 - Dec 31, 2023</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">PDF</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1,789</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">MWK 28.7M</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Completed</span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button class="text-primary hover:text-primary-dark mr-3">Download</button>
                      <button class="text-gray-400 hover:text-gray-600">Delete</button>
                    </td>
                  </tr>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Financial Reports</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Nov 1 - Nov 30, 2023</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">CSV</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">-</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">-</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Failed</span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button class="text-primary hover:text-primary-dark mr-3">Retry</button>
                      <button class="text-gray-400 hover:text-gray-600">Delete</button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>

  <script>
    let currentExportType = null;
    let exportInProgress = false;

    // Mobile sidebar functionality
    function toggleMobileSidebar() {
      const sidebar = document.getElementById('sidebar');
      const overlay = document.getElementById('mobileSidebarOverlay');

      sidebar.classList.toggle('mobile-sidebar-open');
      overlay.classList.toggle('show');
    }

    // Dropdown functionality
    function toggleDropdown() {
      const dropdown = document.getElementById('userDropdown');
      dropdown.classList.toggle('show');
    }

    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
      const dropdown = document.getElementById('userDropdown');
      const button = event.target.closest('button[onclick="toggleDropdown()"]');

      if (!button && !dropdown.contains(event.target)) {
        dropdown.classList.remove('show');
      }
    });

    // Export type selection
    function selectExportType(type) {
      currentExportType = type;

      // Update card selection
      document.querySelectorAll('.export-card').forEach(card => {
        card.classList.remove('selected');
      });
      event.currentTarget.classList.add('selected');

      // Show configuration panel
      document.getElementById('exportConfig').style.display = 'block';

      // Show appropriate categories
      document.getElementById('transactionCategories').style.display = type === 'transactions' ? 'block' : 'none';
      document.getElementById('invoiceCategories').style.display = type === 'invoices' ? 'block' : 'none';
      document.getElementById('reportCategories').style.display = type === 'reports' ? 'block' : 'none';

      // Update estimates
      updateExportEstimates();

      // Scroll to configuration
      document.getElementById('exportConfig').scrollIntoView({ behavior: 'smooth' });
    }

    // Update export estimates
    function updateExportEstimates() {
      if (!currentExportType) return;

      let baseRecords, baseValue;

      switch(currentExportType) {
        case 'transactions':
          baseRecords = 4892;
          baseValue = 45200000; // MWK 45.2M
          break;
        case 'invoices':
          baseRecords = 2156;
          baseValue = 28700000; // MWK 28.7M
          break;
        case 'reports':
          baseRecords = 847;
          baseValue = 0; // Reports don't have monetary value
          break;
        default:
          baseRecords = 1000;
          baseValue = 1000000;
      }

      const estimatedRecords = Math.floor(baseRecords * (0.7 + Math.random() * 0.3));
      const estimatedSize = (estimatedRecords * 0.008).toFixed(1);
      const estimatedTime = Math.ceil(estimatedRecords / 800);
      const estimatedValue = baseValue > 0 ? (baseValue * (0.7 + Math.random() * 0.3)) : 0;

      document.getElementById('estimatedRecords').textContent = estimatedRecords.toLocaleString();
      document.getElementById('estimatedSize').textContent = estimatedSize + ' MB';
      document.getElementById('estimatedTime').textContent = estimatedTime + ' min';

      if (estimatedValue > 0) {
        document.getElementById('estimatedValue').textContent = 'MWK ' + (estimatedValue / 1000000).toFixed(1) + 'M';
      } else {
        document.getElementById('estimatedValue').textContent = 'N/A';
      }
    }

    // Email delivery option toggle
    document.addEventListener('change', function(event) {
      if (event.target.name === 'delivery') {
        const emailOptions = document.getElementById('emailOptions');
        emailOptions.style.display = event.target.value === 'email' ? 'block' : 'none';
      }
    });

    // Reset export configuration
    function resetExport() {
      document.getElementById('exportConfig').style.display = 'none';
      document.querySelectorAll('.export-card').forEach(card => {
        card.classList.remove('selected');
      });
      currentExportType = null;

      // Reset form fields
      document.getElementById('startDate').value = '';
      document.getElementById('endDate').value = '';
      document.querySelectorAll('input[type="checkbox"]').forEach(cb => {
        cb.checked = cb.hasAttribute('checked');
      });
      document.querySelectorAll('input[type="radio"]').forEach(radio => {
        radio.checked = radio.hasAttribute('checked');
      });
    }

    // Preview export
    function previewExport() {
      if (!currentExportType) {
        alert('Please select an export type first.');
        return;
      }

      const typeName = currentExportType.charAt(0).toUpperCase() + currentExportType.slice(1);
      const format = document.querySelector('input[name="format"]:checked').value.toUpperCase();
      const records = document.getElementById('estimatedRecords').textContent;
      const size = document.getElementById('estimatedSize').textContent;
      const value = document.getElementById('estimatedValue').textContent;

      alert('Export Preview:\n\n' +
            'Type: ' + typeName + ' Data\n' +
            'Records: ' + records + '\n' +
            'Size: ' + size + '\n' +
            'Total Value: ' + value + '\n' +
            'Format: ' + format);
    }

    // Start export process
    function startExport() {
      if (!currentExportType) {
        alert('Please select an export type first.');
        return;
      }

      if (exportInProgress) {
        alert('An export is already in progress.');
        return;
      }

      // Hide configuration and show progress
      document.getElementById('exportConfig').style.display = 'none';
      document.getElementById('exportProgress').style.display = 'block';

      exportInProgress = true;
      simulateExportProgress();
    }

    // Simulate export progress
    function simulateExportProgress() {
      let progress = 0;
      const totalRecords = parseInt(document.getElementById('estimatedRecords').textContent.replace(/,/g, ''));
      const totalValue = document.getElementById('estimatedValue').textContent;

      const interval = setInterval(() => {
        progress += Math.random() * 12;
        if (progress > 100) progress = 100;

        // Update progress bar
        document.getElementById('progressBar').style.width = progress + '%';
        document.getElementById('progressPercent').textContent = Math.round(progress) + '%';

        // Update processed records
        const processedRecords = Math.floor((progress / 100) * totalRecords);
        document.getElementById('processedRecords').textContent = processedRecords.toLocaleString();

        // Update current size
        const currentSize = ((progress / 100) * parseFloat(document.getElementById('estimatedSize').textContent)).toFixed(1);
        document.getElementById('currentSize').textContent = currentSize + ' MB';

        // Update processed value
        if (totalValue !== 'N/A') {
          const valueNumber = parseFloat(totalValue.replace('MWK ', '').replace('M', ''));
          const processedValue = ((progress / 100) * valueNumber).toFixed(1);
          document.getElementById('processedValue').textContent = 'MWK ' + processedValue + 'M';
        } else {
          document.getElementById('processedValue').textContent = 'N/A';
        }

        // Update time remaining
        const timeRemaining = Math.ceil((100 - progress) / 8);
        document.getElementById('timeRemaining').textContent = timeRemaining + ':00';

        if (progress >= 100) {
          clearInterval(interval);
          completeExport();
        }
      }, 600);
    }

    // Complete export
    function completeExport() {
      exportInProgress = false;

      // Hide progress and show success
      document.getElementById('exportProgress').style.display = 'none';

      // Show success message and download
      alert('Financial export completed successfully! Your download will start automatically.');

      // Simulate file download
      const link = document.createElement('a');
      link.href = '#';
      link.download = currentExportType + '_financial_export_' + new Date().toISOString().split('T')[0] + '.xlsx';
      link.click();

      // Reset for next export
      resetExport();
    }

    // Cancel export
    function cancelExport() {
      if (confirm('Are you sure you want to cancel the export?')) {
        exportInProgress = false;
        document.getElementById('exportProgress').style.display = 'none';
        resetExport();
      }
    }

    // Show export history
    function showExportHistory() {
      document.getElementById('exportHistory').style.display = 'block';
      document.getElementById('exportHistory').scrollIntoView({ behavior: 'smooth' });
    }

    // Hide export history
    function hideExportHistory() {
      document.getElementById('exportHistory').style.display = 'none';
    }

    // Schedule export (placeholder)
    function scheduleExport() {
      alert('Schedule Export feature coming soon!\n\nThis will allow you to set up automated financial exports at regular intervals for compliance and reporting purposes.');
    }

    // Initialize page
    document.addEventListener('DOMContentLoaded', function() {
      // Set default date range (last 30 days)
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - 30);

      document.getElementById('endDate').value = endDate.toISOString().split('T')[0];
      document.getElementById('startDate').value = startDate.toISOString().split('T')[0];

      console.log('Financial Transactions Export interface initialized');
    });
  </script>
</body>
</html>
