<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MACRA Digital Portal - Login</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <style>
      tailwind.config = {
        theme: {
          extend: {
            colors: { primary: "#e02b20", secondary: "#6366f1" },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: #f9fafb;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Top Header Bar */
        .top-header {
            background: white;
            padding: 16px 0;
            border-bottom: 1px solid #e5e7eb;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }

        .top-header .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-logo {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .header-logo-container {
            width: 50px;
            height: 50px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            padding: 5px;
        }

        .header-logo-img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        .header-text {
            display: flex;
            flex-direction: column;
        }

        .header-title {
            font-size: 24px;
            font-weight: 700;
            color: #e02b20;
            margin: 0;
            line-height: 1;
        }

        .header-subtitle {
            font-size: 12px;
            color: #e02b20;
            margin: 0;
            font-weight: 500;
        }

        h2 {
            font-size: 24px;
            font-weight: 700;
            color: #e02b20;
            margin: 0;
            line-height: 1;
            margin-bottom: 10px;
        }

        .auth-buttons {
            display: flex;
            gap: 10px;
        }

        .btn-auth {
            padding: 8px 16px;
            border: none;
            border-radius: 8px;
            font-size: 13px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-login {
            background: transparent;
            color: #374151;
            border: 1px solid #d1d5db;
        }

        .btn-login:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .btn-signup {
            background: #e02b20;
            color: white;
        }

        .btn-signup:hover {
            background: #dc2626;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(224, 43, 32, 0.2);
        }

        /* Main Content */
        .main-container {
            display: flex;
            min-height: calc(100vh - 80px);
            align-items: flex-start;
            justify-content: center;
            padding: 20px 20px 40px 20px;
        }

        .content-wrapper {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            max-width: 1200px;
            width: 100%;
            align-items: center;
        }

        .content-wrapper-centered {
            display: flex;
            justify-content: center;
            align-items: center;
            max-width: 800px;
            width: 100%;
        }

        /* Left Side - Branding */
        .branding-section {
            text-align: center;
            color: #374151;
        }

        .logo-container {
            margin-bottom: 30px;
        }

        .logo {
            width: 120px;
            height: 120px;
            background: white;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            padding: 10px;
        }

        .logo img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        .main-title {
            font-size: 48px;
            font-weight: 700;
            margin-bottom: 15px;
            color: #111827;
        }

        .subtitle {
            font-size: 20px;
            margin-bottom: 10px;
            font-weight: 500;
            color: #e02b20;
        }

        .description {
            font-size: 16px;
            line-height: 1.6;
            color: #6b7280;
            max-width: 400px;
            margin: 0 auto 40px;
        }

        /* Services Section */
        .services-header {
            font-size: 32px;
            font-weight: 700;
            color: #e02b20;
            text-align: center;
            margin: 20px 0 15px 0;
            position: relative;
        }

        .services-header::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: #e02b20;
            border-radius: 2px;
        }

        .services-description {
            font-size: 16px;
            line-height: 1.6;
            color: #6b7280;
            max-width: 600px;
            margin: 0 auto 30px;
            text-align: center;
        }

        /* Service Categories */
        .service-categories {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            max-width: 1000px;
            margin: 0 auto;
            width: 100%;
        }

        .service-box {
            background: white;
            border-radius: 12px;
            padding: 25px 15px;
            text-align: center;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            border: 1px solid #e5e7eb;
            transition: all 0.3s ease;
            cursor: pointer;
            min-height: 140px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        /* Bottom row cards spanning 2 columns each to center them */
        .service-box:nth-child(5) {
            grid-column: 1 / 3;
        }

        .service-box:nth-child(6) {
            grid-column: 3 / 5;
        }

        .service-box:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 25px -3px rgba(224, 43, 32, 0.2), 0 4px 6px -2px rgba(224, 43, 32, 0.1);
            border-color: #e02b20;
        }

        .service-box:hover .service-icon {
            background: #dc2626;
            box-shadow: 0 4px 12px rgba(224, 43, 32, 0.3);
        }

        .service-icon {
            width: 60px;
            height: 60px;
            background: #e02b20;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            color: white;
            font-size: 24px;
        }

        .service-title {
            font-size: 16px;
            font-weight: 600;
            color: #e02b20;
            margin: 0 0 8px 0;
        }

        .service-description {
            font-size: 13px;
            color: #6b7280;
            margin: 0;
            line-height: 1.4;
        }

        /* Right Side - Login Form */
        .login-section {
            background: white;
            border-radius: 16px;
            padding: 40px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            border: 1px solid #e5e7eb;
        }

        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .login-title {
            font-size: 28px;
            color: #111827;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .login-subtitle {
            color: #6b7280;
            font-size: 14px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            color: #374151;
            font-weight: 500;
            font-size: 14px;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #d1d5db;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f9fafb;
        }

        .form-input:focus {
            outline: none;
            border-color: #e02b20;
            box-shadow: 0 0 0 3px rgba(224, 43, 32, 0.1);
            background: white;
        }

        .form-input:hover {
            background: white;
        }

        .forgot-password {
            text-align: right;
            margin-bottom: 25px;
        }

        .forgot-password a {
            color: #e02b20;
            text-decoration: none;
            font-size: 14px;
        }

        .forgot-password a:hover {
            text-decoration: underline;
        }

        .btn-primary {
            width: 100%;
            padding: 14px;
            background: #e02b20;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: #dc2626;
            transform: translateY(-1px);
            box-shadow: 0 8px 20px rgba(224, 43, 32, 0.3);
        }

        .divider {
            text-align: center;
            margin: 25px 0;
            position: relative;
            color: #6b7280;
            font-size: 14px;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e5e7eb;
            z-index: 1;
        }

        .divider span {
            background: white;
            padding: 0 15px;
            position: relative;
            z-index: 2;
        }

        .signup-link {
            text-align: center;
            margin-top: 20px;
            color: #6b7280;
        }

        .signup-link a {
            color: #e02b20;
            text-decoration: none;
            font-weight: 600;
        }

        .signup-link a:hover {
            text-decoration: underline;
            color: #dc2626;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .service-categories {
                max-width: 900px;
                gap: 15px;
            }

            .service-box {
                padding: 20px 12px;
            }
        }

        @media (max-width: 800px) {
            .content-wrapper {
                grid-template-columns: 1fr;
                gap: 40px;
                text-align: center;
            }

            .main-title {
                font-size: 36px;
            }

            .login-section {
                max-width: 400px;
                margin: 0 auto;
            }

            .services-header {
                font-size: 28px;
            }

            .service-categories {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
                max-width: 100%;
            }

            .service-box:nth-child(5) {
                grid-column: 1 / 2;
            }

            .service-box:nth-child(6) {
                grid-column: 2 / 3;
            }

            .service-box {
                padding: 18px 12px;
            }
        }

        @media (max-width: 768px) {
            .header-title {
                font-size: 20px;
            }

            .header-subtitle {
                font-size: 11px;
            }

            .header-logo-container {
                width: 40px;
                height: 40px;
            }
        }

        @media (max-width: 576px) {
            .top-header .container {
                flex-direction: column;
                gap: 10px;
            }

            .btn-auth {
                padding: 6px 12px;
                font-size: 12px;
            }

            .header-title {
                font-size: 18px;
            }

            .header-subtitle {
                font-size: 10px;
            }

            .header-logo-container {
                width: 35px;
                height: 35px;
            }
            
            .main-title {
                font-size: 28px;
            }

            .services-header {
                font-size: 24px;
            }

            .service-categories {
                grid-template-columns: 1fr;
                max-width: 280px;
                gap: 15px;
            }

            .service-box:nth-child(5),
            .service-box:nth-child(6) {
                grid-column: 1;
            }
            .service-box {
                padding: 20px 15px;
            }

            .service-icon {
                width: 50px;
                height: 50px;
                font-size: 20px;
            }

            .service-title {
                font-size: 14px;
            }

            .service-description {
                font-size: 12px;
            }
        }

        /* Enhanced form styles */
        .enhanced-input {
            appearance: none;
            display: block;
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #d1d5db;
            border-radius: 8px;
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            color: #374151;
            background-color: #f9fafb;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .enhanced-input:hover {
            background-color: white;
        }

        .enhanced-input:focus {
            outline: none;
            border-color: #e02b20;
            box-shadow: 0 0 0 3px rgba(224, 43, 32, 0.1);
            background-color: white;
        }

        .enhanced-button {
            display: flex;
            justify-content: center;
            width: 100%;
            padding: 14px 16px;
            border: 2px solid transparent;
            border-radius: 8px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            font-size: 16px;
            font-weight: 600;
            color: white;
            background-color: #e02b20;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .enhanced-button:hover {
            background-color: #dc2626;
            transform: translateY(-1px);
            box-shadow: 0 8px 20px rgba(224, 43, 32, 0.3);
        }

        /* Animation Effects */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .branding-section {
            animation: fadeInUp 0.8s ease-out;
        }

        .login-section {
            animation: fadeInUp 0.8s ease-out 0.2s both;
        }

        /* Background Pattern */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                radial-gradient(circle at 20% 50%, rgba(224, 43, 32, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(99, 102, 241, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(224, 43, 32, 0.03) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }
    </style>
</head>
<body>
    <!-- Top Header Bar -->
    <div class="top-header">
        <div class="container">
            <div class="header-logo">
                <div class="header-logo-container">
                    <!-- Using a placeholder/fallback in case image doesn't exist -->
                    <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ctext x='20' y='25' text-anchor='middle' font-family='Arial' font-size='12' fill='%23e02b20'%3EM%3C/text%3E%3C/svg%3E" alt="MACRA Logo" class="header-logo-img">
                </div>
                <div class="header-text">
                    <h1 class="header-title">MACRA</h1>
                    <p class="header-subtitle">Digital Portal</p>
                </div>
            </div>
            <div class="auth-buttons">
                <a href="javascript:void(0)" class="btn-auth btn-login" onclick="showLoginForm()">Log In</a>
                <a href="javascript:void(0)" class="btn-auth btn-signup" onclick="showSignupForm()">Sign Up</a>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-container">
        <div class="content-wrapper-centered">
            <!-- Services Section -->
            <div class="branding-section">
                <h2 class="services-header">Services</h2>
                <p class="services-description">Access licensing and information services across Postal, Telecommunications, Standards, CLF, Procurement, and Consumer Affairs. Create an account to get started.</p>
                <!-- Service Categories -->
                <div class="service-categories">
                    <div class="service-box">
                        <div class="service-icon">
                            <i class="fas fa-mail-bulk"></i>
                        </div>
                        <h3 class="service-title">Postal</h3>
                        <p class="service-description">Apply for Courier (International & Domestic) or Posts License (Public Postal Operator).</p>
                    </div>
                    
                    <div class="service-box">
                        <div class="service-icon">
                            <i class="fas fa-broadcast-tower"></i>
                        </div>
                        <h3 class="service-title">Telecommunications</h3>
                        <p class="service-description">Apply for a Telecommunications License, including Spectrum and Radio Dealer types.</p>
                    </div>
                    
                    <div class="service-box">
                        <div class="service-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <h3 class="service-title">Standards</h3>
                        <p class="service-description">Apply for Standards Licenses, including Type Approval Certificates and Short Code authorizations</p>
                    </div>
                    
                    <div class="service-box">
                        <div class="service-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h3 class="service-title">Converged Licensing Framework</h3>
                        <p class="service-description">Apply for CLF licenses to operate telecom infrastructure, offer network or application services (e.g. ISPs, MVNOs), and deliver content services like broadcasting and digital media</p>
                    </div>
                    
                    <div class="service-box">
                        <div class="service-icon">
                            <i class="fas fa-file-contract"></i>
                        </div>
                        <h3 class="service-title">Procurement</h3>
                        <p class="service-description">Submit bids for goods and service contracts and track the status of your applications.</p>
                    </div>
                    
                    <div class="service-box">
                        <div class="service-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h3 class="service-title">Consumer Affairs</h3>
                        <p class="service-description">Access Consumer Affairs services to lodge complaints, resolve telecom-related issues, and stay informed about your rights and responsibilities.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Show login form
        function showLoginForm() {
            alert('Redirecting to Login Page...\n\nThis would typically redirect to:\n- /auth/login (for Next.js)\n- login.html (for static site)');
            // Example redirect: window.location.href = 'login.html';
        }
        
        // Show signup form
        function showSignupForm() {
            alert('Redirecting to Sign Up Page...\n\nThis would typically redirect to:\n- /auth/signup (for Next.js)\n- signup.html (for static site)');
            // Example redirect: window.location.href = 'signup.html';
        }

        // Service box click handlers
        document.addEventListener('DOMContentLoaded', function() {
            console.log('MACRA Digital Portal Landing Page Loaded');
            
            // Add click handlers to service boxes
            const serviceBoxes = document.querySelectorAll('.service-box');
            serviceBoxes.forEach((box, index) => {
                box.addEventListener('click', function() {
                    const serviceNames = ['Postal', 'Telecommunications', 'Standards', 'CLF', 'Procurement', 'Consumer Affairs'];
                    alert(`Clicked on ${serviceNames[index]} service!\n\nThis would typically redirect to the ${serviceNames[index]} application page.`);
                });
            });
            
            // Add hover effects
            serviceBoxes.forEach(box => {
                box.style.cursor = 'pointer';
            });
        });
    </script>
</body>
</html>