<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Approve License Applications - Admin Portal</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: { primary: "#e02b20", secondary: "#6366f1", "primary-subtle": "#e4463c", "secondary-subtle": "#9FE2BF" },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"
    />
    <style type="text/tailwindcss">
      @layer components {
        .tab-heading {
          @apply flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0 mb-6 sm:items-start md:items-start lg:items-center;
        }
        .custom-form-label {
          @apply block text-sm font-medium text-gray-700 pb-2;
        }
        .enhanced-input {
          @apply appearance-none block w-full px-4 py-3 border-2 border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary sm:text-sm bg-gray-50 hover:bg-white transition-colors;
        }

        .enhanced-select {
          @apply block w-full px-4 py-3 border-2 border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary sm:text-sm bg-gray-50 hover:bg-white transition-colors;
        }

        .enhanced-checkbox {
          @apply h-5 w-5 text-primary focus:ring-primary border border-gray-300 rounded;
        }

        .main-button {
          @apply inline-flex justify-center py-3 px-4 border-2 border-transparent rounded-md shadow-md text-sm font-medium text-white bg-primary hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all;
        }

        .secondary-main-button {
          @apply inline-flex py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring focus:ring-secondary-subtle/50;
        }

        .license-card {
          @apply bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-lg hover:border-primary/20 transition-all duration-300 transform hover:-translate-y-1;
        }

        .license-card-icon {
          @apply w-12 h-12 rounded-lg flex items-center justify-center text-2xl;
        }

        .license-card-button {
          @apply w-full bg-primary text-white px-4 py-3 rounded-lg font-medium hover:bg-primary-subtle focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all duration-200 transform hover:scale-105;
        }

        .category-section {
          @apply mb-12;
        }

        .category-header {
          @apply flex items-center mb-6 p-4 bg-gradient-to-r from-primary/5 to-primary/10 rounded-lg border-l-4 border-primary;
        }

        .category-icon {
          @apply w-8 h-8 bg-primary text-white rounded-lg flex items-center justify-center mr-4;
        }

        .fade-in {
          animation: fadeIn 0.6s ease-in-out;
        }

        .slide-up {
          animation: slideUp 0.8s ease-out;
        }

        .inner-section {
          @apply mt-4 grid gap-y-6 gap-x-6 sm:grid-cols-2 lg:grid-cols-2 border-b border-gray-200 pb-4;
        }

        .btn-active-primary {
          @apply inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 hover:text-white bg-white border border-primary rounded-full hover:bg-primary transition transform hover:translate-x-1 after:content-['_↗'];
        }

        .sub-heading {
          @apply text-gray-700;
        }

        .card-bg {
          @apply bg-white rounded-lg border border-gray-200 hover:border-primary transition-all duration-200 hover:shadow-md;
        }

        .status-badge {
          @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
        }

        .status-pending {
          @apply bg-yellow-100 text-yellow-800;
        }

        .status-under-review {
          @apply bg-blue-100 text-blue-800;
        }

        .status-approved {
          @apply bg-green-100 text-green-800;
        }

        .status-rejected {
          @apply bg-red-100 text-red-800;
        }

        .status-complete-review {
          @apply bg-purple-100 text-purple-800;
        }

        .status-conditional-rejection {
          @apply bg-orange-100 text-orange-800;
        }

        /* License Management Dropdown Styles */
        .license-management-dropdown .rotate-180 {
          transform: rotate(180deg);
        }

        .license-management-dropdown button:hover {
          background-color: rgba(0, 0, 0, 0.05);
        }

        .application-card {
          @apply bg-white rounded-lg border border-gray-200 p-6 hover:border-primary cursor-pointer transition-all duration-200 hover:shadow-md;
        }

        .evaluation-section {
          @apply border border-gray-200 rounded-lg p-6;
        }

        .view-doc-btn {
          @apply inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-secondary hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-secondary ml-2;
        }

        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }

        @keyframes slideUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      }

      @layer utilities {
        :root {
          --color-primary: #e02b20;
          --color-secondary: #20d5e0;
          --color-primary-subtle: #e4463c;
          --color-secondary-subtle: #abeff3;
        }
      }
    </style>
    <style>
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
        font-family: 'Inter', sans-serif;
        background-color: #f9fafb;
      }

      .dropdown-content {
        display: none;
        position: absolute;
        right: 0;
        top: 100%;
        z-index: 50;
      }
      .dropdown-content.show {
        display: block;
      }

      .side-nav{
        overflow: auto;
        -ms-overflow-style: none;
        height: 75vh;
      }
      .side-nav::-webkit-scrollbar {
        display: none;
      }
      /* Hide scrollbar for Firefox */
      .side-nav {
        scrollbar-width: none;
      }

      /* Mobile sidebar styles */
      @media (max-width: 768px) {
        .mobile-sidebar-open {
          display: block !important;
          position: fixed;
          z-index: 50;
          height: 100vh;
          box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
        }

        .mobile-sidebar-overlay {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.5);
          z-index: 40;
          display: none;
        }

        .mobile-sidebar-overlay.show {
          display: block;
        }
      }

      .stagger-animation {
        animation-delay: calc(var(--stagger) * 0.1s);
      }
    </style>
  </head>
  <body>
    <div class="flex h-screen overflow-hidden">
      <!-- Mobile sidebar overlay -->
      <div id="mobileSidebarOverlay" class="mobile-sidebar-overlay"></div>

      <!-- Sidebar -->
      <aside id="sidebar" class="w-64 bg-white shadow-lg flex-shrink-0 hidden md:block">
        <div class="h-16 flex items-center px-6 border-b">
          <div class="flex items-center">
            <img src="../images/macra-logo.png" alt="Logo" class="max-h-12 w-auto">
          </div>
        </div>
        <nav class="mt-6 px-4 side-nav">
          <div class="space-y-1">
            <a
              href="../index.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-dashboard-line"></i>
              </div>
              Dashboard
            </a>
            <!-- License Management with Dropdown -->
            <div class="license-management-dropdown">
              <button
                onclick="toggleLicenseDropdown()"
                class="flex items-center justify-between w-full px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none"
              >
                <div class="flex items-center">
                  <div class="w-5 h-5 flex items-center justify-center mr-3">
                    <i class="ri-key-line"></i>
                  </div>
                  License Management
                </div>
                <div class="w-4 h-4 flex items-center justify-center">
                  <i class="ri-arrow-down-s-line transition-transform duration-200" id="licenseDropdownIcon"></i>
                </div>
              </button>

              <!-- Dropdown Menu -->
              <div id="licenseDropdownMenu" class="ml-8 mt-1 space-y-1 hidden">
                <a
                  href="license-management.html"
                  class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50"
                >
                  <div class="w-4 h-4 flex items-center justify-center mr-3">
                    <i class="ri-list-check-line"></i>
                  </div>
                  Manage Licenses
                </a>
                <a
                  href="approve_licenses.html"
                  class="flex items-center px-4 py-2 text-sm font-medium rounded-md bg-primary bg-opacity-10 text-primary"
                >
                  <div class="w-4 h-4 flex items-center justify-center mr-3">
                    <i class="ri-file-check-line"></i>
                  </div>
                  License Approvals
                </a>
              </div>
            </div>

           <a
              href="../spectrum/spectrum-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
  <path stroke-linecap="round" stroke-linejoin="round" d="M9.348 14.652a3.75 3.75 0 0 1 0-5.304m5.304 0a3.75 3.75 0 0 1 0 5.304m-7.425 2.121a6.75 6.75 0 0 1 0-9.546m9.546 0a6.75 6.75 0 0 1 0 9.546M5.106 18.894c-3.808-3.807-3.808-9.98 0-13.788m13.788 0c3.808 3.807 3.808 9.98 0 13.788M12 12h.008v.008H12V12Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z" />
</svg>
              </div>
              Spectrum Management
            </a>
          <a
              href="../financial/accounts-finance.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                </svg>
              </div>
              Accounts & Finance
            </a>
                 <a
              href="../reports/reports.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
  <path stroke-linecap="round" stroke-linejoin="round" d="M12 7.5h1.5m-1.5 3h1.5m-7.5 3h7.5m-7.5 3h7.5m3-9h3.375c.621 0 1.125.504 1.125 1.125V18a2.25 2.25 0 0 1-2.25 2.25M16.5 7.5V18a2.25 2.25 0 0 0 2.25 2.25M16.5 7.5V4.875c0-.621-.504-1.125-1.125-1.125H4.125C3.504 3.75 3 4.254 3 4.875V18a2.25 2.25 0 0 0 2.25 2.25h13.5M6 7.5h3v3H6v-3Z" />
</svg>
              </div>
              Reports & Analytics
            </a>

          </div>

          <div class="mt-8">
            <h3
              class="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider"
            >
              Settings
            </h3>
            <div class="mt-2 space-y-1">

               <a
                href="../user-management/user-management.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
  <path stroke-linecap="round" stroke-linejoin="round" d="M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z" />
</svg>
                </div>
                User Management
              </a>
              <a
                href="../audit-trail.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-shield-line"></i>
                </div>
                Audit Trail
              </a>
              <a
                href="../help-support.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-question-line"></i>
                </div>
                Help & Support
              </a>
            </div>
          </div>
        </nav>
        <div class="absolute bottom-0 w-64 p-4 border-t">
          <a href="profile.html" class="flex items-center hover:bg-gray-50 p-2 rounded-md">
            <img
              class="h-10 w-10 rounded-full"
              src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp"
              alt="Profile"
            />
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-900">Admin User</p>
              <p class="text-xs text-gray-500">MACRA Administrator</p>
            </div>
          </a>
        </div>
      </aside>
      <!-- Main content -->
      <div class="flex-1 flex flex-col overflow-hidden">
        <!-- Top header -->
        <header class="bg-white shadow-sm z-10">
          <div class="flex items-center justify-between h-16 px-4 sm:px-6">
            <button
              id="mobileMenuBtn"
              type="button"
              onclick="toggleMobileSidebar()"
              class="md:hidden text-gray-500 hover:text-gray-700 focus:outline-none"
            >
              <div class="w-6 h-6 flex items-center justify-center">
                <i class="ri-menu-line ri-lg"></i>
              </div>
            </button>
            <div class="flex-1 flex items-center justify-between">
              <div class="flex-1 flex justify-center px-2 lg:ml-6 lg:justify-start">
              </div>
              <div class="flex items-center">
                <button
                  type="button"
                  class="flex-shrink-0 p-1 mr-4 text-gray-500 rounded-full hover:text-gray-700 focus:outline-none relative"
                >
                  <span class="sr-only">View notifications</span>
                  <div class="w-6 h-6 flex items-center justify-center">
                    <i class="ri-notification-3-line ri-lg"></i>
                  </div>
                  <span
                    class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white"
                  ></span>
                </button>
                <div class="dropdown relative">
                  <button
                    type="button"
                    onclick="toggleDropdown()"
                    class="flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    <span class="sr-only">Open user menu</span>
                    <img
                      class="h-8 w-8 rounded-full"
                      src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp"
                      alt="Profile"
                    />
                  </button>
                  <div
                    id="userDropdown"
                    class="dropdown-content mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5"
                  >
                    <div class="py-1">
                      <a
                        href="profile.html"
                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        >Your Profile</a
                      >
                      <a
                        href="account-settings.html"
                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        >Settings</a
                      >
                      <a
                        href="../auth/login.html"
                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        >Sign out</a
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </header>

        <!-- Main content area -->
        <main class="flex-1 overflow-y-auto bg-gray-50 p-4 sm:p-6">
          <div class="max-w-7xl mx-auto">
            <!-- Page header -->
            <div class="tab-heading">
              <div>
                <h1 class="text-3xl font-bold text-gray-900">License Application Approval</h1>
                <p class="mt-2 text-gray-600">Approve or conditionally reject license applications that have completed evaluation.</p>
              </div>
              <div class="flex space-x-3">
                <select class="enhanced-select">
                  <option value="all">All Categories</option>
                  <option value="application">Application Service</option>
                  <option value="content">Content Service</option>
                  <option value="network-facility">Network Facility</option>
                  <option value="network-service">Network Service</option>
                  <option value="postal">Postal Services</option>
                </select>
                <select class="enhanced-select">
                  <option value="all">All Status</option>
                  <option value="pending">Pending</option>
                  <option value="under-review">Under Review</option>
                  <option value="approved">Approved</option>
                  <option value="rejected">Rejected</option>
                </select>
              </div>
            </div>

            <!-- Statistics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              <div class="bg-white rounded-lg border border-gray-200 p-6 hover:border-primary transition-colors">
                <div class="flex items-center">
                  <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                    <i class="ri-time-line text-2xl text-yellow-600"></i>
                  </div>
                  <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Pending Review</p>
                    <p class="text-2xl font-bold text-gray-900">12</p>
                  </div>
                </div>
              </div>
              <div class="bg-white rounded-lg border border-gray-200 p-6 hover:border-primary transition-colors">
                <div class="flex items-center">
                  <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="ri-eye-line text-2xl text-blue-600"></i>
                  </div>
                  <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Under Review</p>
                    <p class="text-2xl font-bold text-gray-900">8</p>
                  </div>
                </div>
              </div>
              <div class="bg-white rounded-lg border border-gray-200 p-6 hover:border-primary transition-colors">
                <div class="flex items-center">
                  <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <i class="ri-check-line text-2xl text-green-600"></i>
                  </div>
                  <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Approved Today</p>
                    <p class="text-2xl font-bold text-gray-900">5</p>
                  </div>
                </div>
              </div>
              <div class="bg-white rounded-lg border border-gray-200 p-6 hover:border-primary transition-colors">
                <div class="flex items-center">
                  <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                    <i class="ri-close-line text-2xl text-red-600"></i>
                  </div>
                  <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Rejected</p>
                    <p class="text-2xl font-bold text-gray-900">2</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Applications by Category -->
            <div class="space-y-8">
              <!-- Application Service Applications -->
              <div class="category-section">
                <div class="category-header">
                  <div class="category-icon">
                    <i class="ri-global-line"></i>
                  </div>
                  <div>
                    <h2 class="text-xl font-semibold text-gray-900">Application Service License Applications</h2>
                    <p class="text-sm text-gray-600">ISP and Individual license applications</p>
                  </div>
                  <div class="ml-auto">
                    <span class="status-badge status-pending">3 Pending</span>
                  </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <!-- ISP Application -->
                  <div class="application-card">
                    <div class="flex items-start justify-between mb-4">
                      <div class="flex items-center">
                        <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                          <i class="ri-global-line text-blue-600"></i>
                        </div>
                        <div>
                          <h3 class="text-lg font-semibold text-gray-900">TechConnect ISP</h3>
                          <p class="text-sm text-gray-500">ISP License Application</p>
                        </div>
                      </div>
                      <span class="status-badge status-pending">Pending</span>
                    </div>

                    <div class="space-y-2 mb-4">
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Application ID:</span>
                        <span class="font-medium">ASL-2024-001</span>
                      </div>
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Submitted:</span>
                        <span class="font-medium">Jan 15, 2024</span>
                      </div>
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">License Fee:</span>
                        <span class="font-medium text-primary">MWK 30,000,000</span>
                      </div>
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Processing Time:</span>
                        <span class="font-medium">30-45 days</span>
                      </div>
                    </div>

                    <div class="flex space-x-2">
                      <button onclick="viewPendingApplication('ASL-2024-001')" class="bg-secondary hover:bg-opacity-90 text-white px-4 py-2 rounded-md text-sm font-medium flex-1">
                        <i class="ri-eye-line mr-2"></i>View
                      </button>
                      <button onclick="assignApplication('ASL-2024-001')" class="bg-secondary hover:bg-opacity-90 text-white px-4 py-2 rounded-md text-sm font-medium flex-1">
                        <i class="ri-user-add-line mr-2"></i>Assign
                      </button>
                    </div>
                  </div>

                  <!-- Individual Application -->
                  <div class="application-card">
                    <div class="flex items-start justify-between mb-4">
                      <div class="flex items-center">
                        <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                          <i class="ri-user-line text-green-600"></i>
                        </div>
                        <div>
                          <h3 class="text-lg font-semibold text-gray-900">John Banda</h3>
                          <p class="text-sm text-gray-500">Individual License Application</p>
                        </div>
                      </div>
                      <span class="status-badge status-complete-review">Complete Review</span>
                    </div>

                    <div class="space-y-2 mb-4">
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Application ID:</span>
                        <span class="font-medium">ASL-2024-002</span>
                      </div>
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Submitted:</span>
                        <span class="font-medium">Jan 12, 2024</span>
                      </div>
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">License Fee:</span>
                        <span class="font-medium text-primary">MWK 30,000,000</span>
                      </div>
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Reviewer:</span>
                        <span class="font-medium">Sarah Mwale</span>
                      </div>
                    </div>

                    <div class="flex space-x-2">
                      <button onclick="openCommentsModal('ASL-2024-002')" class="main-button flex-1">
                        <i class="ri-message-line mr-2"></i>Review & Decide
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Content Service Applications -->
              <div class="category-section">
                <div class="category-header">
                  <div class="category-icon">
                    <i class="ri-tv-line"></i>
                  </div>
                  <div>
                    <h2 class="text-xl font-semibold text-gray-900">Content Service License Applications</h2>
                    <p class="text-sm text-gray-600">Radio and Television broadcasting applications</p>
                  </div>
                  <div class="ml-auto">
                    <span class="status-badge status-pending">2 Pending</span>
                  </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <!-- Radio Application -->
                  <div class="application-card">
                    <div class="flex items-start justify-between mb-4">
                      <div class="flex items-center">
                        <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center mr-3">
                          <i class="ri-radio-line text-red-600"></i>
                        </div>
                        <div>
                          <h3 class="text-lg font-semibold text-gray-900">Malawi University</h3>
                          <p class="text-sm text-gray-500">University Radio License</p>
                        </div>
                      </div>
                      <span class="status-badge status-pending">Pending</span>
                    </div>

                    <div class="space-y-2 mb-4">
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Application ID:</span>
                        <span class="font-medium">CSL-2024-001</span>
                      </div>
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Submitted:</span>
                        <span class="font-medium">Jan 18, 2024</span>
                      </div>
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">License Fee:</span>
                        <span class="font-medium text-primary">MWK 6,000,000</span>
                      </div>
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Processing Time:</span>
                        <span class="font-medium">45-60 days</span>
                      </div>
                    </div>

                    <div class="flex space-x-2">
                      <button onclick="viewPendingApplication('CSL-2024-001')" class="bg-secondary hover:bg-opacity-90 text-white px-4 py-2 rounded-md text-sm font-medium flex-1">
                        <i class="ri-eye-line mr-2"></i>View
                      </button>
                      <button onclick="assignApplication('CSL-2024-001')" class="bg-secondary hover:bg-opacity-90 text-white px-4 py-2 rounded-md text-sm font-medium flex-1">
                        <i class="ri-user-add-line mr-2"></i>Assign
                      </button>
                    </div>
                  </div>

                  <!-- TV Application -->
                  <div class="application-card">
                    <div class="flex items-start justify-between mb-4">
                      <div class="flex items-center">
                        <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                          <i class="ri-tv-line text-purple-600"></i>
                        </div>
                        <div>
                          <h3 class="text-lg font-semibold text-gray-900">Blantyre Broadcasting</h3>
                          <p class="text-sm text-gray-500">Television License</p>
                        </div>
                      </div>
                      <span class="status-badge status-under-review">Under Review</span>
                    </div>

                    <div class="space-y-2 mb-4">
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Application ID:</span>
                        <span class="font-medium">CSL-2024-002</span>
                      </div>
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Submitted:</span>
                        <span class="font-medium">Jan 10, 2024</span>
                      </div>
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">License Fee:</span>
                        <span class="font-medium text-primary">MWK 6,000,000</span>
                      </div>
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Reviewer:</span>
                        <span class="font-medium">Peter Chisale</span>
                      </div>
                    </div>

                    <div class="flex space-x-2">
                      <button onclick="openCommentsModal('CSL-2024-002')" class="main-button flex-1">
                        <i class="ri-message-line mr-2"></i>Review & Decide
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Network Facility Applications -->
              <div class="category-section">
                <div class="category-header">
                  <div class="category-icon">
                    <i class="ri-satellite-dish-line"></i>
                  </div>
                  <div>
                    <h2 class="text-xl font-semibold text-gray-900">Network Facility License Applications</h2>
                    <p class="text-sm text-gray-600">Infrastructure and satellite facility applications</p>
                  </div>
                  <div class="ml-auto">
                    <span class="status-badge status-pending">1 Pending</span>
                  </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <!-- Satellite Application -->
                  <div class="application-card">
                    <div class="flex items-start justify-between mb-4">
                      <div class="flex items-center">
                        <div class="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center mr-3">
                          <i class="ri-satellite-dish-line text-indigo-600"></i>
                        </div>
                        <div>
                          <h3 class="text-lg font-semibold text-gray-900">SatCom Malawi</h3>
                          <p class="text-sm text-gray-500">Satellite Facility License</p>
                        </div>
                      </div>
                      <span class="status-badge status-pending">Pending</span>
                    </div>

                    <div class="space-y-2 mb-4">
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Application ID:</span>
                        <span class="font-medium">NFL-2024-001</span>
                      </div>
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Submitted:</span>
                        <span class="font-medium">Jan 20, 2024</span>
                      </div>
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">License Fee:</span>
                        <span class="font-medium text-primary">MWK 2,500,000</span>
                      </div>
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Processing Time:</span>
                        <span class="font-medium">90-120 days</span>
                      </div>
                    </div>

                    <div class="flex space-x-2">
                      <button onclick="viewPendingApplication('NFL-2024-001')" class="bg-secondary hover:bg-opacity-90 text-white px-4 py-2 rounded-md text-sm font-medium flex-1">
                        <i class="ri-eye-line mr-2"></i>View
                      </button>
                      <button onclick="assignApplication('NFL-2024-001')" class="bg-secondary hover:bg-opacity-90 text-white px-4 py-2 rounded-md text-sm font-medium flex-1">
                        <i class="ri-user-add-line mr-2"></i>Assign
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Network Service Applications -->
              <div class="category-section">
                <div class="category-header">
                  <div class="category-icon">
                    <i class="ri-global-line"></i>
                  </div>
                  <div>
                    <h2 class="text-xl font-semibold text-gray-900">Network Service License Applications</h2>
                    <p class="text-sm text-gray-600">Mobile network and telecommunications service applications</p>
                  </div>
                  <div class="ml-auto">
                    <span class="status-badge status-under-review">1 Under Review</span>
                  </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <!-- Mobile Network Application -->
                  <div class="application-card">
                    <div class="flex items-start justify-between mb-4">
                      <div class="flex items-center">
                        <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                          <i class="ri-smartphone-line text-green-600"></i>
                        </div>
                        <div>
                          <h3 class="text-lg font-semibold text-gray-900">MobileNet Solutions</h3>
                          <p class="text-sm text-gray-500">Mobile Network License</p>
                        </div>
                      </div>
                      <span class="status-badge status-complete-review">Complete Review</span>
                    </div>

                    <div class="space-y-2 mb-4">
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Application ID:</span>
                        <span class="font-medium">NSL-2024-001</span>
                      </div>
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Submitted:</span>
                        <span class="font-medium">Jan 8, 2024</span>
                      </div>
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">License Fee:</span>
                        <span class="font-medium text-primary">MWK 1,800,000</span>
                      </div>
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Reviewer:</span>
                        <span class="font-medium">James Phiri</span>
                      </div>
                    </div>

                    <div class="flex space-x-2">
                      <button onclick="openCommentsModal('NSL-2024-001')" class="main-button flex-1">
                        <i class="ri-message-line mr-2"></i>Review & Decide
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Postal Service Applications -->
              <div class="category-section">
                <div class="category-header">
                  <div class="category-icon">
                    <i class="ri-mail-line"></i>
                  </div>
                  <div>
                    <h2 class="text-xl font-semibold text-gray-900">Postal Service License Applications</h2>
                    <p class="text-sm text-gray-600">Mail and courier service applications</p>
                  </div>
                  <div class="ml-auto">
                    <span class="status-badge status-approved">1 Approved</span>
                  </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <!-- Postal Application -->
                  <div class="application-card">
                    <div class="flex items-start justify-between mb-4">
                      <div class="flex items-center">
                        <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                          <i class="ri-mail-line text-orange-600"></i>
                        </div>
                        <div>
                          <h3 class="text-lg font-semibold text-gray-900">Express Courier MW</h3>
                          <p class="text-sm text-gray-500">Postal Service License</p>
                        </div>
                      </div>
                      <span class="status-badge status-approved">Approved</span>
                    </div>

                    <div class="space-y-2 mb-4">
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Application ID:</span>
                        <span class="font-medium">PSL-2024-001</span>
                      </div>
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Submitted:</span>
                        <span class="font-medium">Jan 5, 2024</span>
                      </div>
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">License Fee:</span>
                        <span class="font-medium text-primary">MWK 3,000,000</span>
                      </div>
                      <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Approved By:</span>
                        <span class="font-medium">Mary Kachale</span>
                      </div>
                    </div>

                    <div class="flex space-x-2">
                      <button onclick="viewApplication('PSL-2024-001')" class="main-button flex-1">
                        <i class="ri-eye-line mr-2"></i>View Details
                      </button>
                      <button onclick="generateLicense('PSL-2024-001')" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        <i class="ri-file-text-line mr-2"></i>Generate License
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>

    <!-- Pending Application Overview Modal -->
    <div id="pendingApplicationModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
      <div class="relative top-10 mx-auto p-5 border w-11/12 md:w-4/5 lg:w-3/4 shadow-lg rounded-md bg-white">
        <div class="mt-3">
          <!-- Modal Header -->
          <div class="flex items-center justify-between pb-4 border-b">
            <h3 class="text-lg font-semibold text-gray-900" id="pendingModalTitle">Application Overview</h3>
            <button onclick="closePendingApplicationModal()" class="text-gray-400 hover:text-gray-600">
              <i class="ri-close-line text-xl"></i>
            </button>
          </div>

          <!-- Modal Body - Scrollable -->
          <div class="py-4 max-h-96 overflow-y-auto">
            <!-- Application Basic Info -->
            <div class="mb-6 p-4 bg-gray-50 rounded-lg border-2" id="basicInfoSection">
              <div class="flex justify-between items-center mb-3">
                <h4 class="font-medium text-gray-900">Application Information</h4>
                <button onclick="confirmSection('basicInfo')" id="confirmBasicInfo" class="px-3 py-1 text-xs font-medium rounded-md bg-green-600 hover:bg-green-700 text-white">
                  <i class="ri-check-line mr-1"></i>Confirm
                </button>
              </div>
              <div id="pendingAppBasicInfo" class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <!-- Content will be populated here -->
              </div>
            </div>

            <!-- Applicant Details -->
            <div class="mb-6 p-4 bg-blue-50 rounded-lg border-2" id="applicantDetailsSection">
              <div class="flex justify-between items-center mb-3">
                <h4 class="font-medium text-gray-900 flex items-center">
                  <i class="ri-user-line mr-2"></i>Applicant Details
                </h4>
                <button onclick="confirmSection('applicantDetails')" id="confirmApplicantDetails" class="px-3 py-1 text-xs font-medium rounded-md bg-green-600 hover:bg-green-700 text-white">
                  <i class="ri-check-line mr-1"></i>Confirm
                </button>
              </div>
              <div id="pendingAppApplicantInfo" class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <!-- Content will be populated here -->
              </div>
            </div>

            <!-- Payment Information -->
            <div class="mb-6 p-4 bg-green-50 rounded-lg border-2" id="paymentInfoSection">
              <div class="flex justify-between items-center mb-3">
                <h4 class="font-medium text-gray-900 flex items-center">
                  <i class="ri-money-dollar-circle-line mr-2"></i>Payment Transactions
                </h4>
                <button onclick="confirmSection('paymentInfo')" id="confirmPaymentInfo" class="px-3 py-1 text-xs font-medium rounded-md bg-green-600 hover:bg-green-700 text-white">
                  <i class="ri-check-line mr-1"></i>Confirm
                </button>
              </div>
              <div id="pendingAppPaymentInfo" class="space-y-3">
                <!-- Payment transactions will be populated here -->
              </div>
            </div>

            <!-- Documents Submitted -->
            <div class="mb-6 p-4 bg-yellow-50 rounded-lg border-2" id="documentsSection">
              <div class="flex justify-between items-center mb-3">
                <h4 class="font-medium text-gray-900 flex items-center">
                  <i class="ri-file-list-line mr-2"></i>Documents Submitted
                </h4>
                <button onclick="confirmSection('documents')" id="confirmDocuments" class="px-3 py-1 text-xs font-medium rounded-md bg-green-600 hover:bg-green-700 text-white">
                  <i class="ri-check-line mr-1"></i>Confirm
                </button>
              </div>
              <div id="pendingAppDocuments" class="grid grid-cols-1 md:grid-cols-2 gap-2">
                <!-- Documents will be populated here -->
              </div>
            </div>

            <!-- Technical Requirements -->
            <div class="mb-4 p-4 bg-purple-50 rounded-lg border-2" id="technicalSection">
              <div class="flex justify-between items-center mb-3">
                <h4 class="font-medium text-gray-900 flex items-center">
                  <i class="ri-settings-line mr-2"></i>Technical Requirements
                </h4>
                <button onclick="confirmSection('technical')" id="confirmTechnical" class="px-3 py-1 text-xs font-medium rounded-md bg-green-600 hover:bg-green-700 text-white">
                  <i class="ri-check-line mr-1"></i>Confirm
                </button>
              </div>
              <div id="pendingAppTechnical" class="text-sm text-gray-700">
                <!-- Technical info will be populated here -->
              </div>
            </div>
          </div>

          <!-- Modal Footer -->
          <div class="flex items-center justify-end pt-4 border-t space-x-3">
            <button onclick="closePendingApplicationModal()" class="secondary-main-button">
              Close
            </button>
            <button onclick="rejectFromModal()" class="main-button">
              <i class="ri-close-line mr-2"></i>Reject
            </button>
            <button onclick="assignFromModal()" id="assignButton" class="bg-secondary hover:bg-opacity-90 text-white px-4 py-2 rounded-md text-sm font-medium opacity-50 cursor-not-allowed" disabled>
              <i class="ri-user-add-line mr-2"></i>Assign for Evaluation
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Comments Modal -->
    <div id="commentsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
      <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
          <!-- Modal Header -->
          <div class="flex items-center justify-between pb-4 border-b">
            <h3 class="text-lg font-semibold text-gray-900" id="modalTitle">Application Comments</h3>
            <button onclick="closeCommentsModal()" class="text-gray-400 hover:text-gray-600">
              <i class="ri-close-line text-xl"></i>
            </button>
          </div>

          <!-- Modal Body -->
          <div class="py-4 max-h-96 overflow-y-auto">
            <!-- Application Info -->
            <div class="mb-6 p-4 bg-gray-50 rounded-lg">
              <h4 class="font-medium text-gray-900 mb-2" id="modalAppInfo">Application Details</h4>
              <div class="text-sm text-gray-600" id="modalAppDetails"></div>
            </div>

            <!-- Evaluator Comments Section -->
            <div class="mb-6">
              <h4 class="font-medium text-gray-900 mb-3 flex items-center">
                <i class="ri-user-line mr-2"></i>Evaluator Comments
              </h4>
              <div id="evaluatorComments" class="space-y-3">
                <!-- Comments will be populated here -->
              </div>
            </div>

            <!-- Customer Comments Section -->
            <div class="mb-4">
              <h4 class="font-medium text-gray-900 mb-3 flex items-center">
                <i class="ri-message-line mr-2"></i>Comments for Customer
              </h4>
              <textarea
                id="customerComment"
                rows="4"
                class="enhanced-input w-full"
                placeholder="Enter comments that will be visible to the customer..."
              ></textarea>
            </div>
          </div>

          <!-- Modal Footer -->
          <div class="flex items-center justify-end pt-4 border-t space-x-3">
            <button onclick="closeCommentsModal()" class="secondary-main-button">
              Cancel
            </button>
            <button onclick="approveWithComments()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium">
              <i class="ri-check-line mr-2"></i>Approve
            </button>
            <button onclick="conditionalRejectWithComments()" class="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-md text-sm font-medium">
              <i class="ri-error-warning-line mr-2"></i>Conditional Rejection
            </button>
          </div>
        </div>
      </div>
    </div>

    <script>
      // Application data store
      let applications = {
        'ASL-2024-001': {
          id: 'ASL-2024-001',
          company: 'TechConnect ISP',
          type: 'ISP License Application',
          category: 'application',
          status: 'pending',
          submitted: 'Jan 15, 2024',
          fee: 'MWK 30,000,000',
          processingTime: '30-45 days',
          reviewer: null,
          evaluatorComments: [],
          applicantDetails: {
            contactPerson: 'James Mwanza',
            email: '<EMAIL>',
            phone: '+*********** 456',
            address: 'Plot 123, Industrial Area, Blantyre',
            registrationNumber: '**********',
            taxNumber: 'TIN987654321'
          },
          paymentTransactions: [
            {
              id: 'PAY-001-2024',
              amount: 'MWK 30,000,000',
              date: 'Jan 14, 2024',
              method: 'Bank Transfer',
              status: 'Completed',
              reference: 'BT240114001'
            }
          ],
          documents: [
            'Certificate of Incorporation',
            'Tax Clearance Certificate',
            'Business Plan',
            'Technical Specifications',
            'Financial Statements (3 years)',
            'Network Topology Diagram',
            'Coverage Area Maps'
          ],
          technicalRequirements: {
            serviceType: 'Internet Service Provider',
            coverageArea: 'Blantyre, Lilongwe, Mzuzu',
            expectedSubscribers: '50,000 within 3 years',
            infrastructure: 'Fiber optic backbone with wireless last-mile',
            bandwidth: '10 Gbps international gateway'
          }
        },
        'ASL-2024-002': {
          id: 'ASL-2024-002',
          company: 'John Banda',
          type: 'Individual License Application',
          category: 'application',
          status: 'complete-review',
          submitted: 'Jan 12, 2024',
          fee: 'MWK 30,000,000',
          reviewer: 'Sarah Mwale',
          evaluatorComments: [
            {
              evaluator: 'Sarah Mwale',
              date: 'Jan 20, 2024',
              section: 'Technical Capacity',
              comment: 'Technical documentation is comprehensive. Network topology is well-designed.',
              score: 85
            },
            {
              evaluator: 'Peter Chisale',
              date: 'Jan 22, 2024',
              section: 'Financial Capacity',
              comment: 'Financial statements show adequate capital. Bank guarantees are in order.',
              score: 90
            }
          ]
        },
        'CSL-2024-001': {
          id: 'CSL-2024-001',
          company: 'Malawi University',
          type: 'University Radio License',
          category: 'content',
          status: 'pending',
          submitted: 'Jan 18, 2024',
          fee: 'MWK 6,000,000',
          processingTime: '45-60 days',
          reviewer: null,
          evaluatorComments: [],
          applicantDetails: {
            contactPerson: 'Dr. Grace Phiri',
            email: '<EMAIL>',
            phone: '+*********** 890',
            address: 'University of Malawi, Zomba',
            registrationNumber: 'UNI001',
            taxNumber: 'EXEMPT'
          },
          paymentTransactions: [
            {
              id: 'PAY-002-2024',
              amount: 'MWK 6,000,000',
              date: 'Jan 17, 2024',
              method: 'Government Transfer',
              status: 'Completed',
              reference: 'GT240117002'
            }
          ],
          documents: [
            'University Registration Certificate',
            'Educational License',
            'Programming Schedule',
            'Technical Equipment List',
            'Studio Layout Plans',
            'Content Policy Document',
            'Educational Objectives Statement'
          ],
          technicalRequirements: {
            serviceType: 'Educational Radio Broadcasting',
            coverageArea: 'Zomba District and surrounding areas',
            frequency: '90.5 FM (requested)',
            transmissionPower: '1000 watts',
            programmingHours: '18 hours daily (6 AM - 12 AM)',
            contentFocus: 'Educational programs, university news, cultural content'
          }
        },
        'CSL-2024-002': {
          id: 'CSL-2024-002',
          company: 'Blantyre Broadcasting',
          type: 'Television License',
          category: 'content',
          status: 'under-review',
          submitted: 'Jan 10, 2024',
          fee: 'MWK 6,000,000',
          reviewer: 'Peter Chisale',
          evaluatorComments: [
            {
              evaluator: 'Peter Chisale',
              date: 'Jan 18, 2024',
              section: 'Programming Content',
              comment: 'Programming schedule meets local content requirements. Educational content is well-planned.',
              score: 88
            },
            {
              evaluator: 'Mary Kachale',
              date: 'Jan 19, 2024',
              section: 'Technical Infrastructure',
              comment: 'Broadcasting equipment specifications are adequate. Coverage area is appropriate.',
              score: 82
            }
          ]
        },
        'NFL-2024-001': {
          id: 'NFL-2024-001',
          company: 'SatCom Malawi',
          type: 'Satellite Facility License',
          category: 'network-facility',
          status: 'pending',
          submitted: 'Jan 20, 2024',
          fee: 'MWK 2,500,000',
          processingTime: '90-120 days',
          reviewer: null,
          evaluatorComments: [],
          applicantDetails: {
            contactPerson: 'Michael Tembo',
            email: '<EMAIL>',
            phone: '+*********** 567',
            address: 'Plot 456, Capital City, Lilongwe',
            registrationNumber: '**********',
            taxNumber: 'TIN123456789'
          },
          paymentTransactions: [
            {
              id: 'PAY-003-2024',
              amount: 'MWK 2,500,000',
              date: 'Jan 19, 2024',
              method: 'Bank Transfer',
              status: 'Completed',
              reference: 'BT240119003'
            }
          ],
          documents: [
            'Certificate of Incorporation',
            'Tax Clearance Certificate',
            'Satellite Equipment Specifications',
            'Earth Station Technical Plans',
            'Frequency Coordination Documents',
            'Environmental Impact Assessment',
            'Site Survey Report',
            'Insurance Documentation'
          ],
          technicalRequirements: {
            serviceType: 'Satellite Earth Station',
            facilityLocation: 'Lilongwe Satellite Park',
            antennaSize: '7.3 meter dish',
            frequencyBands: 'C-band and Ku-band',
            services: 'Internet backbone, VSAT services, broadcast uplink',
            coverage: 'National and regional coverage'
          }
        },
        'NSL-2024-001': {
          id: 'NSL-2024-001',
          company: 'MobileNet Solutions',
          type: 'Mobile Network License',
          category: 'network-service',
          status: 'complete-review',
          submitted: 'Jan 8, 2024',
          fee: 'MWK 1,800,000',
          reviewer: 'James Phiri',
          evaluatorComments: [
            {
              evaluator: 'James Phiri',
              date: 'Jan 15, 2024',
              section: 'Network Coverage',
              comment: 'Proposed coverage area is comprehensive. Network rollout plan is realistic.',
              score: 92
            },
            {
              evaluator: 'Sarah Mwale',
              date: 'Jan 17, 2024',
              section: 'Quality of Service',
              comment: 'QoS parameters meet regulatory standards. Customer service plan is adequate.',
              score: 87
            }
          ]
        },
        'PSL-2024-001': {
          id: 'PSL-2024-001',
          company: 'Express Courier MW',
          type: 'Postal Service License',
          category: 'postal',
          status: 'approved',
          submitted: 'Jan 5, 2024',
          fee: 'MWK 3,000,000',
          approvedBy: 'Mary Kachale',
          evaluatorComments: []
        }
      };

      // Toggle dropdown menu
      function toggleDropdown() {
        const dropdown = document.getElementById('userDropdown');
        dropdown.classList.toggle('show');

        // Prevent the click from propagating to the window event
        event.stopPropagation();
      }

      // Close dropdown when clicking outside
      window.addEventListener('click', function(event) {
        if (!event.target.matches('.dropdown button') && !event.target.closest('.dropdown button img')) {
          const dropdowns = document.getElementsByClassName('dropdown-content');
          for (let i = 0; i < dropdowns.length; i++) {
            const openDropdown = dropdowns[i];
            if (openDropdown.classList.contains('show')) {
              openDropdown.classList.remove('show');
            }
          }
        }
      });

      // Toggle mobile sidebar
      function toggleMobileSidebar() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('mobileSidebarOverlay');

        sidebar.classList.toggle('mobile-sidebar-open');
        overlay.classList.toggle('show');

        // Close sidebar when clicking on overlay
        overlay.onclick = function() {
          sidebar.classList.remove('mobile-sidebar-open');
          overlay.classList.remove('show');
        };
      }

      // Application management functions

      function approveApplication(applicationId) {
        const app = applications[applicationId];
        if (!app) {
          alert('Application not found!');
          return;
        }

        if (confirm('Are you sure you want to approve application ' + applicationId + '?')) {
          // Update application data
          app.status = 'approved';
          app.approvedBy = 'Current User'; // In real app, get from session
          app.approvedDate = new Date().toLocaleDateString();

          // Update UI
          updateApplicationStatus(applicationId, 'approved');
          updateStatistics();

          // Show success message with animation
          showNotification('Application ' + applicationId + ' has been approved successfully!', 'success');

          // Update button actions for approved application
          updateApplicationButtons(applicationId, 'approved');
        }
      }

      function rejectApplication(applicationId) {
        const app = applications[applicationId];
        if (!app) {
          alert('Application not found!');
          return;
        }

        const reason = prompt('Please provide a reason for rejection:');
        if (reason && reason.trim() !== '') {
          if (confirm('Are you sure you want to reject application ' + applicationId + '?')) {
            // Update application data
            app.status = 'rejected';
            app.rejectedBy = 'Current User'; // In real app, get from session
            app.rejectedDate = new Date().toLocaleDateString();
            app.rejectionReason = reason;

            // Update UI
            updateApplicationStatus(applicationId, 'rejected');
            updateStatistics();

            // Show notification
            showNotification('Application ' + applicationId + ' has been rejected.', 'error');

            // Update button actions for rejected application
            updateApplicationButtons(applicationId, 'rejected');
          }
        } else if (reason !== null) {
          alert('Please provide a valid reason for rejection.');
        }
      }

      function viewApplication(applicationId) {
        const app = applications[applicationId];
        if (!app) {
          alert('Application not found!');
          return;
        }

        // Store application data for viewing
        localStorage.setItem('currentApplication', JSON.stringify(app));
        window.location.href = 'evaluation-template.html?id=' + applicationId + '&mode=view';
      }

      function generateLicense(applicationId) {
        const app = applications[applicationId];
        if (!app || app.status !== 'approved') {
          alert('Can only generate licenses for approved applications!');
          return;
        }

        if (confirm('Generate license document for application ' + applicationId + '?')) {
          // Simulate license generation
          showNotification('Generating license document for ' + applicationId + '...', 'info');

          setTimeout(() => {
            // Simulate download
            const licenseData = {
              applicationId: applicationId,
              company: app.company,
              licenseType: app.type,
              issuedDate: new Date().toLocaleDateString(),
              validUntil: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toLocaleDateString()
            };

            downloadLicense(licenseData);
            showNotification('License document generated successfully!', 'success');
          }, 2000);
        }
      }

      function downloadLicense(licenseData) {
        // Create a simple text file for demonstration
        const content = `
MACRA LICENSE CERTIFICATE

Application ID: ${licenseData.applicationId}
Company/Applicant: ${licenseData.company}
License Type: ${licenseData.licenseType}
Issued Date: ${licenseData.issuedDate}
Valid Until: ${licenseData.validUntil}

This license is issued by the Malawi Communications Regulatory Authority (MACRA).
        `;

        const blob = new Blob([content], { type: 'text/plain' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `MACRA_License_${licenseData.applicationId}.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
      }

      function updateApplicationStatus(applicationId, status) {
        // Find the application card and update its status badge
        const applicationCards = document.querySelectorAll('.application-card');
        applicationCards.forEach(card => {
          const idElement = card.querySelector('.font-medium');
          if (idElement && idElement.textContent === applicationId) {
            const statusBadge = card.querySelector('.status-badge');
            if (statusBadge) {
              // Remove existing status classes
              statusBadge.classList.remove('status-pending', 'status-under-review', 'status-approved', 'status-rejected', 'status-complete-review', 'status-conditional-rejection');

              // Add new status class and text
              statusBadge.classList.add('status-' + status);
              const statusText = status.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase());
              statusBadge.textContent = statusText;
            }

            // Update reviewer info if status is under-review
            if (status === 'under-review') {
              const app = applications[applicationId];
              const detailsDiv = card.querySelector('.space-y-2');
              const reviewerRow = detailsDiv.querySelector('.reviewer-row');

              if (!reviewerRow && app.reviewer) {
                const newRow = document.createElement('div');
                newRow.className = 'flex justify-between text-sm reviewer-row';
                newRow.innerHTML = `
                  <span class="text-gray-600">Reviewer:</span>
                  <span class="font-medium">${app.reviewer}</span>
                `;
                detailsDiv.appendChild(newRow);
              }
            }
          }
        });
      }

      function updateApplicationButtons(applicationId, status) {
        const applicationCards = document.querySelectorAll('.application-card');
        applicationCards.forEach(card => {
          const idElement = card.querySelector('.font-medium');
          if (idElement && idElement.textContent === applicationId) {
            const buttonContainer = card.querySelector('.flex.space-x-2');

            if (status === 'approved') {
              buttonContainer.innerHTML = `
                <button onclick="viewApplication('${applicationId}')" class="main-button flex-1">
                  <i class="ri-eye-line mr-2"></i>View Details
                </button>
                <button onclick="generateLicense('${applicationId}')" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                  <i class="ri-file-text-line mr-2"></i>Generate License
                </button>
              `;
            } else if (status === 'rejected') {
              buttonContainer.innerHTML = `
                <button onclick="viewApplication('${applicationId}')" class="main-button flex-1">
                  <i class="ri-eye-line mr-2"></i>View Details
                </button>
                <button onclick="reactivateApplication('${applicationId}')" class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                  <i class="ri-refresh-line mr-2"></i>Reactivate
                </button>
              `;
            } else if (status === 'conditional-rejection') {
              buttonContainer.innerHTML = `
                <button onclick="viewApplication('${applicationId}')" class="main-button flex-1">
                  <i class="ri-eye-line mr-2"></i>View Details
                </button>
                <button onclick="reactivateApplication('${applicationId}')" class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                  <i class="ri-refresh-line mr-2"></i>Reactivate
                </button>
              `;
            }
          }
        });
      }

      function updateStatistics() {
        const stats = {
          pending: 0,
          'under-review': 0,
          'complete-review': 0,
          approved: 0,
          rejected: 0,
          'conditional-rejection': 0
        };

        Object.values(applications).forEach(app => {
          if (stats.hasOwnProperty(app.status)) {
            stats[app.status]++;
          }
        });

        // Combine under-review and complete-review for display
        const totalUnderReview = stats['under-review'] + stats['complete-review'];

        // Update statistics cards
        const statCards = document.querySelectorAll('.grid.grid-cols-1.md\\:grid-cols-4 .bg-white');
        if (statCards.length >= 4) {
          statCards[0].querySelector('.text-2xl').textContent = stats.pending;
          statCards[1].querySelector('.text-2xl').textContent = totalUnderReview;
          statCards[2].querySelector('.text-2xl').textContent = stats.approved;
          statCards[3].querySelector('.text-2xl').textContent = stats.rejected + stats['conditional-rejection'];
        }

        // Update category badges
        updateCategoryBadges();
      }

      function updateCategoryBadges() {
        const categoryStats = {
          application: { pending: 0, 'under-review': 0, 'complete-review': 0, approved: 0, rejected: 0, 'conditional-rejection': 0 },
          content: { pending: 0, 'under-review': 0, 'complete-review': 0, approved: 0, rejected: 0, 'conditional-rejection': 0 },
          'network-facility': { pending: 0, 'under-review': 0, 'complete-review': 0, approved: 0, rejected: 0, 'conditional-rejection': 0 },
          'network-service': { pending: 0, 'under-review': 0, 'complete-review': 0, approved: 0, rejected: 0, 'conditional-rejection': 0 },
          postal: { pending: 0, 'under-review': 0, 'complete-review': 0, approved: 0, rejected: 0, 'conditional-rejection': 0 }
        };

        Object.values(applications).forEach(app => {
          if (categoryStats[app.category] && categoryStats[app.category].hasOwnProperty(app.status)) {
            categoryStats[app.category][app.status]++;
          }
        });

        // Update category section badges
        const categoryHeaders = document.querySelectorAll('.category-header');
        categoryHeaders.forEach(header => {
          const badge = header.querySelector('.status-badge');
          const title = header.querySelector('h2').textContent;

          let category = '';
          if (title.includes('Application Service')) category = 'application';
          else if (title.includes('Content Service')) category = 'content';
          else if (title.includes('Network Facility')) category = 'network-facility';
          else if (title.includes('Network Service')) category = 'network-service';
          else if (title.includes('Postal Service')) category = 'postal';

          if (category && categoryStats[category]) {
            const pending = categoryStats[category].pending;
            const underReview = categoryStats[category]['under-review'] + categoryStats[category]['complete-review'];

            if (pending > 0) {
              badge.className = 'status-badge status-pending';
              badge.textContent = `${pending} Pending`;
            } else if (underReview > 0) {
              badge.className = 'status-badge status-under-review';
              badge.textContent = `${underReview} Under Review`;
            } else {
              badge.className = 'status-badge status-approved';
              badge.textContent = 'Up to date';
            }
          }
        });
      }

      function showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transform transition-all duration-300 translate-x-full`;

        // Set colors based on type
        const colors = {
          success: 'bg-green-500 text-white',
          error: 'bg-red-500 text-white',
          info: 'bg-blue-500 text-white',
          warning: 'bg-yellow-500 text-black'
        };

        notification.className += ` ${colors[type] || colors.info}`;
        notification.innerHTML = `
          <div class="flex items-center">
            <span class="flex-1">${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-2 text-lg">&times;</button>
          </div>
        `;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
          notification.classList.remove('translate-x-full');
        }, 100);

        // Auto remove after 5 seconds
        setTimeout(() => {
          if (notification.parentElement) {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
              if (notification.parentElement) {
                notification.remove();
              }
            }, 300);
          }
        }, 5000);
      }

      function reactivateApplication(applicationId) {
        const app = applications[applicationId];
        if (!app) return;

        if (confirm('Reactivate application ' + applicationId + ' for review?')) {
          app.status = 'pending';
          delete app.rejectedBy;
          delete app.rejectedDate;
          delete app.rejectionReason;

          updateApplicationStatus(applicationId, 'pending');
          updateStatistics();
          showNotification('Application ' + applicationId + ' has been reactivated.', 'info');

          // Reset buttons to original state
          const applicationCards = document.querySelectorAll('.application-card');
          applicationCards.forEach(card => {
            const idElement = card.querySelector('.font-medium');
            if (idElement && idElement.textContent === applicationId) {
              const buttonContainer = card.querySelector('.flex.space-x-2');
              buttonContainer.innerHTML = `
                <button onclick="viewPendingApplication('${applicationId}')" class="bg-secondary hover:bg-opacity-90 text-white px-4 py-2 rounded-md text-sm font-medium flex-1">
                  <i class="ri-eye-line mr-2"></i>View
                </button>
                <button onclick="assignApplication('${applicationId}')" class="bg-secondary hover:bg-opacity-90 text-white px-4 py-2 rounded-md text-sm font-medium flex-1">
                  <i class="ri-user-add-line mr-2"></i>Assign
                </button>
              `;
            }
          });
        }
      }

      // Pending Application Modal functions
      let currentPendingApplicationId = null;
      let confirmedSections = {
        basicInfo: false,
        applicantDetails: false,
        paymentInfo: false,
        documents: false,
        technical: false
      };

      function viewPendingApplication(applicationId) {
        const app = applications[applicationId];
        if (!app) {
          alert('Application not found!');
          return;
        }

        if (app.status !== 'pending') {
          alert('This function is only for pending applications.');
          return;
        }

        currentPendingApplicationId = applicationId;

        // Reset confirmed sections
        confirmedSections = {
          basicInfo: false,
          applicantDetails: false,
          paymentInfo: false,
          documents: false,
          technical: false
        };

        // Update modal content
        document.getElementById('pendingModalTitle').textContent = `${app.company} - ${app.type}`;

        // Basic Application Info
        document.getElementById('pendingAppBasicInfo').innerHTML = `
          <div><strong>Application ID:</strong> ${app.id}</div>
          <div><strong>Status:</strong> <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Pending</span></div>
          <div><strong>Submitted:</strong> ${app.submitted}</div>
          <div><strong>License Fee:</strong> ${app.fee}</div>
          <div><strong>Processing Time:</strong> ${app.processingTime}</div>
          <div><strong>Category:</strong> ${app.category.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}</div>
        `;

        // Applicant Details
        if (app.applicantDetails) {
          document.getElementById('pendingAppApplicantInfo').innerHTML = `
            <div><strong>Contact Person:</strong> ${app.applicantDetails.contactPerson}</div>
            <div><strong>Email:</strong> ${app.applicantDetails.email}</div>
            <div><strong>Phone:</strong> ${app.applicantDetails.phone}</div>
            <div><strong>Address:</strong> ${app.applicantDetails.address}</div>
            <div><strong>Registration Number:</strong> ${app.applicantDetails.registrationNumber}</div>
            <div><strong>Tax Number:</strong> ${app.applicantDetails.taxNumber}</div>
          `;
        }

        // Payment Information
        if (app.paymentTransactions && app.paymentTransactions.length > 0) {
          document.getElementById('pendingAppPaymentInfo').innerHTML = app.paymentTransactions.map(payment => `
            <div class="border border-gray-200 rounded-lg p-3 bg-white">
              <div class="flex justify-between items-start mb-2">
                <div class="font-medium text-gray-900">Payment ID: ${payment.id}</div>
                <div class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${payment.status === 'Completed' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}">
                  ${payment.status}
                </div>
              </div>
              <div class="grid grid-cols-2 gap-2 text-sm text-gray-600">
                <div><strong>Amount:</strong> ${payment.amount}</div>
                <div><strong>Date:</strong> ${payment.date}</div>
                <div><strong>Method:</strong> ${payment.method}</div>
                <div><strong>Reference:</strong> ${payment.reference}</div>
              </div>
            </div>
          `).join('');
        } else {
          document.getElementById('pendingAppPaymentInfo').innerHTML = '<div class="text-gray-500 text-sm italic">No payment information available.</div>';
        }

        // Documents
        if (app.documents && app.documents.length > 0) {
          document.getElementById('pendingAppDocuments').innerHTML = app.documents.map(doc => `
            <div class="flex items-center text-sm text-gray-700 p-2 bg-white rounded border">
              <i class="ri-file-text-line mr-2 text-gray-500"></i>
              ${doc}
            </div>
          `).join('');
        } else {
          document.getElementById('pendingAppDocuments').innerHTML = '<div class="text-gray-500 text-sm italic">No documents listed.</div>';
        }

        // Technical Requirements
        if (app.technicalRequirements) {
          const tech = app.technicalRequirements;
          document.getElementById('pendingAppTechnical').innerHTML = `
            <div class="space-y-2">
              ${Object.entries(tech).map(([key, value]) => `
                <div><strong>${key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}:</strong> ${value}</div>
              `).join('')}
            </div>
          `;
        } else {
          document.getElementById('pendingAppTechnical').innerHTML = '<div class="text-gray-500 text-sm italic">No technical requirements specified.</div>';
        }

        // Reset UI state
        resetConfirmationUI();
        updateAssignButtonState();

        // Show modal
        document.getElementById('pendingApplicationModal').classList.remove('hidden');
      }

      function resetConfirmationUI() {
        // Reset all confirm buttons to unconfirmed state
        const sections = ['basicInfo', 'applicantDetails', 'paymentInfo', 'documents', 'technical'];
        sections.forEach(section => {
          const button = document.getElementById(`confirm${section.charAt(0).toUpperCase() + section.slice(1)}`);
          const sectionDiv = document.getElementById(`${section}Section`);

          if (button && sectionDiv) {
            button.innerHTML = '<i class="ri-check-line mr-1"></i>Confirm';
            button.className = 'px-3 py-1 text-xs font-medium rounded-md bg-green-600 hover:bg-green-700 text-white';
            sectionDiv.className = sectionDiv.className.replace('border-green-500 bg-green-50', '').replace('border-2', 'border-2');
          }
        });
      }

      function confirmSection(sectionName) {
        confirmedSections[sectionName] = true;

        // Update button appearance
        const button = document.getElementById(`confirm${sectionName.charAt(0).toUpperCase() + sectionName.slice(1)}`);
        const sectionDiv = document.getElementById(`${sectionName}Section`);

        if (button && sectionDiv) {
          button.innerHTML = '<i class="ri-check-double-line mr-1"></i>Confirmed';
          button.className = 'px-3 py-1 text-xs font-medium rounded-md bg-green-800 text-white cursor-default';
          button.disabled = true;

          // Update section border to show confirmed state
          sectionDiv.className = sectionDiv.className.replace('border-2', 'border-2 border-green-500');
        }

        // Check if all sections are confirmed
        updateAssignButtonState();

        // Show notification
        showNotification(`${sectionName.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())} section confirmed.`, 'success');
      }

      function updateAssignButtonState() {
        const assignButton = document.getElementById('assignButton');
        const allConfirmed = Object.values(confirmedSections).every(confirmed => confirmed);

        if (allConfirmed) {
          assignButton.disabled = false;
          assignButton.className = 'bg-secondary hover:bg-opacity-90 text-white px-4 py-2 rounded-md text-sm font-medium';
        } else {
          assignButton.disabled = true;
          assignButton.className = 'bg-secondary hover:bg-opacity-90 text-white px-4 py-2 rounded-md text-sm font-medium opacity-50 cursor-not-allowed';
        }
      }

      function closePendingApplicationModal() {
        document.getElementById('pendingApplicationModal').classList.add('hidden');
        currentPendingApplicationId = null;
      }

      function assignApplication(applicationId) {
        const app = applications[applicationId];
        if (!app) {
          alert('Application not found!');
          return;
        }

        if (app.status !== 'pending') {
          alert('Only pending applications can be assigned for evaluation.');
          return;
        }

        if (confirm(`Assign application ${applicationId} for evaluation?\n\nThis will change the status to "Under Review" and make it available to evaluators.`)) {
          // Update application data
          app.status = 'under-review';
          app.assignedDate = new Date().toLocaleDateString();
          app.assignedBy = 'Current Admin'; // In real app, get from session

          // Update UI
          updateApplicationStatus(applicationId, 'under-review');
          updateStatistics();

          // Show success message
          showNotification(`Application ${applicationId} has been assigned for evaluation.`, 'success');

          // Update buttons - applications under review don't show assign buttons for admin
          updateApplicationButtonsForAssigned(applicationId);
        }
      }

      function assignFromModal() {
        if (currentPendingApplicationId) {
          const allConfirmed = Object.values(confirmedSections).every(confirmed => confirmed);
          if (!allConfirmed) {
            alert('Please confirm all sections before assigning the application for evaluation.');
            return;
          }
          closePendingApplicationModal();
          assignApplication(currentPendingApplicationId);
        }
      }

      function rejectFromModal() {
        if (!currentPendingApplicationId) return;

        const reason = prompt('Please provide a reason for rejecting this application:');
        if (reason && reason.trim() !== '') {
          if (confirm(`Are you sure you want to reject application ${currentPendingApplicationId}?\n\nReason: ${reason}`)) {
            const app = applications[currentPendingApplicationId];

            // Update application data
            app.status = 'rejected';
            app.rejectedBy = 'Current Admin'; // In real app, get from session
            app.rejectedDate = new Date().toLocaleDateString();
            app.rejectionReason = reason;

            // Update UI
            updateApplicationStatus(currentPendingApplicationId, 'rejected');
            updateStatistics();

            // Show notification
            showNotification(`Application ${currentPendingApplicationId} has been rejected.`, 'error');

            // Update button actions for rejected application
            updateApplicationButtons(currentPendingApplicationId, 'rejected');

            // Close modal
            closePendingApplicationModal();
          }
        } else if (reason !== null) {
          alert('Please provide a valid reason for rejection.');
        }
      }

      function updateApplicationButtonsForAssigned(applicationId) {
        const applicationCards = document.querySelectorAll('.application-card');
        applicationCards.forEach(card => {
          const idElement = card.querySelector('.font-medium');
          if (idElement && idElement.textContent === applicationId) {
            const buttonContainer = card.querySelector('.flex.space-x-2');
            buttonContainer.innerHTML = `
              <button onclick="openCommentsModal('${applicationId}')" class="main-button flex-1">
                <i class="ri-message-line mr-2"></i>Review & Decide
              </button>
            `;
          }
        });
      }

      // Modal functions
      let currentApplicationId = null;

      function openCommentsModal(applicationId) {
        const app = applications[applicationId];
        if (!app) {
          alert('Application not found!');
          return;
        }

        // Only allow for under-review or complete-review status
        if (app.status !== 'under-review' && app.status !== 'complete-review') {
          alert('Applications can only be approved or conditionally rejected when they are under review or complete review.');
          return;
        }

        currentApplicationId = applicationId;

        // Update modal content
        document.getElementById('modalTitle').textContent = `Application Comments - ${applicationId}`;
        document.getElementById('modalAppInfo').textContent = `${app.company} - ${app.type}`;
        document.getElementById('modalAppDetails').innerHTML = `
          <div class="grid grid-cols-2 gap-4 text-sm">
            <div><strong>Application ID:</strong> ${app.id}</div>
            <div><strong>Status:</strong> ${app.status.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}</div>
            <div><strong>Submitted:</strong> ${app.submitted}</div>
            <div><strong>Reviewer:</strong> ${app.reviewer || 'Not assigned'}</div>
            <div><strong>License Fee:</strong> ${app.fee}</div>
            <div><strong>Category:</strong> ${app.category.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}</div>
          </div>
        `;

        // Populate evaluator comments
        const commentsContainer = document.getElementById('evaluatorComments');
        if (app.evaluatorComments && app.evaluatorComments.length > 0) {
          commentsContainer.innerHTML = app.evaluatorComments.map(comment => `
            <div class="border border-gray-200 rounded-lg p-4 bg-gray-50">
              <div class="flex justify-between items-start mb-2">
                <div class="font-medium text-gray-900">${comment.evaluator}</div>
                <div class="text-sm text-gray-500">${comment.date}</div>
              </div>
              <div class="text-sm text-gray-600 mb-2"><strong>Section:</strong> ${comment.section}</div>
              <div class="text-sm text-gray-800 mb-2">${comment.comment}</div>
              <div class="text-sm">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${comment.score >= 85 ? 'bg-green-100 text-green-800' : comment.score >= 70 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'}">
                  Score: ${comment.score}/100
                </span>
              </div>
            </div>
          `).join('');
        } else {
          commentsContainer.innerHTML = '<div class="text-gray-500 text-sm italic">No evaluator comments available.</div>';
        }

        // Clear customer comment field
        document.getElementById('customerComment').value = '';

        // Show modal
        document.getElementById('commentsModal').classList.remove('hidden');
      }

      function closeCommentsModal() {
        document.getElementById('commentsModal').classList.add('hidden');
        currentApplicationId = null;
      }

      function approveWithComments() {
        if (!currentApplicationId) return;

        const customerComment = document.getElementById('customerComment').value.trim();
        if (!customerComment) {
          alert('Please enter comments for the customer before approving.');
          return;
        }

        const app = applications[currentApplicationId];
        if (confirm(`Are you sure you want to approve application ${currentApplicationId}?`)) {
          // Update application data
          app.status = 'approved';
          app.approvedBy = 'Current User'; // In real app, get from session
          app.approvedDate = new Date().toLocaleDateString();
          app.customerComments = customerComment;

          // Update UI
          updateApplicationStatus(currentApplicationId, 'approved');
          updateStatistics();

          // Show success message
          showNotification(`Application ${currentApplicationId} has been approved successfully!`, 'success');

          // Update button actions for approved application
          updateApplicationButtons(currentApplicationId, 'approved');

          // Close modal
          closeCommentsModal();
        }
      }

      function conditionalRejectWithComments() {
        if (!currentApplicationId) return;

        const customerComment = document.getElementById('customerComment').value.trim();
        if (!customerComment) {
          alert('Please enter comments explaining the conditions for the customer.');
          return;
        }

        const app = applications[currentApplicationId];
        if (confirm(`Are you sure you want to conditionally reject application ${currentApplicationId}?`)) {
          // Update application data
          app.status = 'conditional-rejection';
          app.conditionalRejectedBy = 'Current User'; // In real app, get from session
          app.conditionalRejectedDate = new Date().toLocaleDateString();
          app.customerComments = customerComment;

          // Update UI
          updateApplicationStatus(currentApplicationId, 'conditional-rejection');
          updateStatistics();

          // Show notification
          showNotification(`Application ${currentApplicationId} has been conditionally rejected.`, 'warning');

          // Update button actions for conditionally rejected application
          updateApplicationButtons(currentApplicationId, 'conditional-rejection');

          // Close modal
          closeCommentsModal();
        }
      }

      // Filter applications by category and status
      function filterApplications() {
        const categoryFilter = document.querySelector('select');
        const statusFilter = document.querySelectorAll('select')[1];

        const selectedCategory = categoryFilter.value;
        const selectedStatus = statusFilter.value;

        const categoryMappings = {
          'application': 'Application Service',
          'content': 'Content Service',
          'network-facility': 'Network Facility',
          'network-service': 'Network Service',
          'postal': 'Postal Service'
        };

        // Show/hide category sections
        const categorySections = document.querySelectorAll('.category-section');
        categorySections.forEach(section => {
          const title = section.querySelector('h2').textContent;
          const shouldShowCategory = selectedCategory === 'all' ||
            (categoryMappings[selectedCategory] && title.includes(categoryMappings[selectedCategory]));

          if (shouldShowCategory) {
            section.style.display = 'block';

            // Filter applications within this category
            const applicationCards = section.querySelectorAll('.application-card');
            applicationCards.forEach(card => {
              const statusBadge = card.querySelector('.status-badge');
              const currentStatus = statusBadge.textContent.toLowerCase().replace(' ', '-');
              const shouldShowStatus = selectedStatus === 'all' || currentStatus === selectedStatus;

              card.style.display = shouldShowStatus ? 'block' : 'none';
            });
          } else {
            section.style.display = 'none';
          }
        });

        showNotification(`Filtered by: ${selectedCategory === 'all' ? 'All Categories' : categoryMappings[selectedCategory] || selectedCategory}, ${selectedStatus === 'all' ? 'All Status' : selectedStatus}`, 'info');
      }

      // Add event listeners for filters
      document.addEventListener('DOMContentLoaded', function() {
        const selects = document.querySelectorAll('select');
        selects.forEach(select => {
          select.addEventListener('change', filterApplications);
        });

        // Initialize statistics
        updateStatistics();

        // Close modal when clicking outside
        document.getElementById('commentsModal').addEventListener('click', function(e) {
          if (e.target === this) {
            closeCommentsModal();
          }
        });

        // Close pending application modal when clicking outside
        document.getElementById('pendingApplicationModal').addEventListener('click', function(e) {
          if (e.target === this) {
            closePendingApplicationModal();
          }
        });

        // Initialize license dropdown as open since we're on the approvals page
        toggleLicenseDropdown();
      });

      // License Management Dropdown Toggle
      function toggleLicenseDropdown() {
        const menu = document.getElementById('licenseDropdownMenu');
        const icon = document.getElementById('licenseDropdownIcon');

        if (menu.classList.contains('hidden')) {
          menu.classList.remove('hidden');
          icon.classList.add('rotate-180');
        } else {
          menu.classList.add('hidden');
          icon.classList.remove('rotate-180');
        }
      }
    </script>
  </body>
</html>