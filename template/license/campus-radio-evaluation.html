<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>University Campus Radio Broadcasting License Evaluation - MACRA Portal</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: { primary: "#e02b20", secondary: "#6366f1" },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"
    />
    <style>
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
        font-family: 'Inter', sans-serif;
        background-color: #f9fafb;
      }

      /* Enhanced form styles */
      .enhanced-input {
        @apply appearance-none block w-full px-4 py-3 border-2 border-gray-300 rounded-md shadow-sm
        placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary
        sm:text-sm bg-gray-50 hover:bg-white transition-colors;
      }

      .enhanced-select {
        @apply block w-full px-4 py-3 border-2 border-gray-300 rounded-md shadow-sm
        focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary
        sm:text-sm bg-gray-50 hover:bg-white transition-colors;
      }

      .enhanced-button {
        @apply flex justify-center py-3 px-4 border-2 border-transparent rounded-md shadow-md
        text-sm font-medium text-white bg-primary hover:bg-opacity-90 focus:outline-none
        focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all;
      }

      .dropdown-content {
        display: none;
        position: absolute;
        right: 0;
        top: 100%;
        z-index: 50;
      }
      .dropdown-content.show {
        display: block;
      }

      .side-nav{
        overflow: auto;
        -ms-overflow-style: none;
        height: 75vh;
      }
      .side-nav::-webkit-scrollbar {
        display: none;
      }
      /* Hide scrollbar for Firefox */
      .side-nav {
        scrollbar-width: none;
      }

      /* Score input styles */
      .score-input {
        @apply w-20 text-center border-2 border-gray-300 rounded-md px-2 py-1 text-sm
        focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary;
      }

      .score-bar {
        @apply h-4 rounded-full transition-all duration-300;
      }

      /* Mobile sidebar styles */
      @media (max-width: 768px) {
        .mobile-sidebar-open {
          display: block !important;
          position: fixed;
          z-index: 50;
          height: 100vh;
          box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
        }

        .mobile-sidebar-overlay {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.5);
          z-index: 40;
          display: none;
        }

        .mobile-sidebar-overlay.show {
          display: block;
        }
      }

      .evaluation-criteria {
        @apply bg-green-50 border border-green-200 rounded-lg p-4 mb-6;
      }

      .criteria-item {
        @apply flex justify-between items-center py-2 border-b border-green-200 last:border-b-0;
      }

      .pass-fail-indicator {
        @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
      }

      .pass-indicator {
        @apply bg-green-100 text-green-800;
      }

      .fail-indicator {
        @apply bg-red-100 text-red-800;
      }

      .pending-indicator {
        @apply bg-yellow-100 text-yellow-800;
      }

      .campus-radio-section {
        @apply bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4;
      }

      .section-header {
        @apply bg-gradient-to-r from-green-500 to-blue-600 text-white px-4 py-2 rounded-t-lg font-medium;
      }

      .score-display {
        @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary text-white;
      }

      .marks-input {
        @apply w-16 text-center border-2 border-gray-300 rounded-md px-2 py-1 text-sm
        focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary;
      }
      
      /* PDF Viewer Modal Styles */
      .modal {
        display: none;
        position: fixed;
        z-index: 100;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: auto;
        background-color: rgba(0, 0, 0, 0.5);
      }

      .modal-content {
        background-color: #fefefe;
        margin: 2% auto;
        padding: 20px;
        border: 1px solid #888;
        border-radius: 8px;
        width: 90%;
        max-width: 1200px;
        height: 85vh;
        position: relative;
      }

      .pdf-viewer {
        width: 100%;
        height: calc(100% - 40px);
        border: none;
      }

      .close-modal {
        position: absolute;
        right: 20px;
        top: 10px;
        color: #aaa;
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
      }

      .close-modal:hover,
      .close-modal:focus {
        color: #000;
        text-decoration: none;
      }

      .view-doc-btn {
        @apply inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md 
        text-white bg-secondary hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 
        focus:ring-secondary ml-2;
      }
    </style>
  </head>
  <body>
    <div class="flex h-screen overflow-hidden">
      <!-- Mobile sidebar overlay -->
      <div id="mobileSidebarOverlay" class="mobile-sidebar-overlay"></div>

      <!-- Sidebar -->
      <aside id="sidebar" class="w-64 bg-white shadow-lg flex-shrink-0 hidden md:block">
        <div class="h-16 flex items-center px-6 border-b">
          <div class="flex items-center">
            <img src="../images/macra-logo.png" alt="Logo" class="max-h-12 w-auto">
          </div>
        </div>
        <nav class="mt-6 px-4 side-nav">
          <div class="space-y-1">
            <a
              href="../index.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-dashboard-line"></i>
              </div>
              Dashboard
            </a>
            <a
              href="license-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-key-line"></i>
              </div>
              License Management
            </a>
            <a
              href="evaluation-template.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md bg-primary bg-opacity-10 text-primary"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-file-check-line"></i>
              </div>
              License Evaluation
            </a>

           <a
              href="../spectrum/spectrum-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
  <path stroke-linecap="round" stroke-linejoin="round" d="M9.348 14.652a3.75 3.75 0 0 1 0-5.304m5.304 0a3.75 3.75 0 0 1 0 5.304m-7.425 2.121a6.75 6.75 0 0 1 0-9.546m9.546 0a6.75 6.75 0 0 1 0 9.546M5.106 18.894c-3.808-3.807-3.808-9.98 0-13.788m13.788 0c3.808 3.807 3.808 9.98 0 13.788M12 12h.008v.008H12V12Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z" />
</svg>

              </div>

              Spectrum Management
            </a>
          <a
              href="../financial/transaction-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
  <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z" />
</svg>

              </div>

              Financial Transactions
            </a>
                 <a
              href="../reports/reports.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
  <path stroke-linecap="round" stroke-linejoin="round" d="M12 7.5h1.5m-1.5 3h1.5m-7.5 3h7.5m-7.5 3h7.5m3-9h3.375c.621 0 1.125.504 1.125 1.125V18a2.25 2.25 0 0 1-2.25 2.25M16.5 7.5V18a2.25 2.25 0 0 0 2.25 2.25M16.5 7.5V4.875c0-.621-.504-1.125-1.125-1.125H4.125C3.504 3.75 3 4.254 3 4.875V18a2.25 2.25 0 0 0 2.25 2.25h13.5M6 7.5h3v3H6v-3Z" />
</svg>

              </div>

              Reports & Analytics
            </a>

          </div>

          <div class="mt-8">
            <h3
              class="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider"
            >
              Settings
            </h3>
            <div class="mt-2 space-y-1">

               <a
                href="../user-management/user-management.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
  <path stroke-linecap="round" stroke-linejoin="round" d="M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z" />
</svg>

                </div>
                User Management
              </a>
              <a
                href="../audit-trail.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-shield-line"></i>
                </div>
                Audit Trail
              </a>
              <a
                href="../help-support.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-question-line"></i>
                </div>
                Help & Support
              </a>
            </div>
          </div>
        </nav>
        <div class="absolute bottom-0 w-64 p-4 border-t">
          <a href="../user-management/user-profile.html" class="flex items-center hover:bg-gray-50 p-2 rounded-md">
            <img
              class="h-10 w-10 rounded-full"
              src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp"
              alt="Profile"
            />
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-900">Emily Banda</p>
              <p class="text-xs text-gray-500">Administrator</p>
            </div>
          </a>
        </div>
      </aside>

      <!-- Main content -->
      <div class="flex-1 flex flex-col overflow-hidden">
        <!-- Top navigation -->
        <header class="bg-white shadow-sm border-b border-gray-200">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
              <div class="flex items-center">
                <button
                  id="mobileSidebarToggle"
                  class="md:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary"
                >
                  <i class="ri-menu-line text-xl"></i>
                </button>
                <div class="ml-4 md:ml-0">
                  <nav class="flex space-x-8">
                    <a
                      href="evaluation-template.html"
                      class="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium"
                    >
                      All Evaluations
                    </a>
                    <a
                      href="individual-license-evaluation.html"
                      class="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium"
                    >
                      Individual License
                    </a>
                    <a
                      href="network-service-evaluation.html"
                      class="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium"
                    >
                      Network Service
                    </a>
                    <a
                      href="broadcasting-evaluation.html"
                      class="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium"
                    >
                      Broadcasting
                    </a>
                    <a
                      href="campus-radio-evaluation.html"
                      class="text-primary border-b-2 border-primary px-3 py-2 rounded-md text-sm font-medium"
                    >
                      Campus Radio
                    </a>
                  </nav>
                </div>
              </div>
              <div class="flex items-center space-x-4">
                <div class="relative">
                  <button
                    id="notificationDropdown"
                    class="p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary rounded-full"
                  >
                    <i class="ri-notification-line text-xl"></i>
                    <span class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-400 ring-2 ring-white"></span>
                  </button>
                </div>
                <div class="relative">
                  <button
                    id="userDropdown"
                    class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    <img
                      class="h-8 w-8 rounded-full"
                      src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp"
                      alt="Profile"
                    />
                  </button>
                  <div
                    id="userDropdownContent"
                    class="dropdown-content absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 ring-1 ring-black ring-opacity-5"
                  >
                    <a
                      href="../user-management/user-profile.html"
                      class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      Your Profile
                    </a>
                    <a
                      href="../help-support.html"
                      class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      Settings
                    </a>
                    <a
                      href="../login.html"
                      class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      Sign out
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </header>

        <!-- Page content -->
        <main class="flex-1 overflow-y-auto">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- Page header -->
            <div class="mb-6">
              <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                <div>
                  <h1 class="text-2xl font-semibold text-gray-900">University Campus Radio Broadcasting License Evaluation</h1>
                  <p class="mt-1 text-sm text-gray-500">
                    Evaluate and score university campus radio broadcasting license applications based on educational and community service criteria.
                  </p>
                </div>
                <div class="relative">
                  <a
                    href="license-management.html"
                    role="button"
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    <i class="ri-arrow-left-line mr-2"></i>
                    Back to License Management
                  </a>
                </div>
              </div>
            </div>

            <!-- Application Selection -->
            <div class="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
              <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                  Select Campus Radio Application to Evaluate
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div class="border border-gray-200 rounded-lg p-4 hover:border-primary cursor-pointer transition-colors" onclick="showEvaluationForm('CR-2024-001')">
                    <div class="flex items-center justify-between">
                      <div>
                        <h4 class="text-sm font-medium text-gray-900">University of Malawi Radio</h4>
                        <p class="text-xs text-gray-500">Application ID: CR-2024-001</p>
                        <p class="text-xs text-gray-500">Type: Campus Radio Broadcasting</p>
                        <p class="text-xs text-gray-500">Submitted: 2024-01-20</p>
                      </div>
                      <span class="pending-indicator">Pending</span>
                    </div>
                  </div>
                  <div class="border border-gray-200 rounded-lg p-4 hover:border-primary cursor-pointer transition-colors" onclick="showEvaluationForm('CR-2024-002')">
                    <div class="flex items-center justify-between">
                      <div>
                        <h4 class="text-sm font-medium text-gray-900">Mzuzu University FM</h4>
                        <p class="text-xs text-gray-500">Application ID: CR-2024-002</p>
                        <p class="text-xs text-gray-500">Type: Educational Broadcasting</p>
                        <p class="text-xs text-gray-500">Submitted: 2024-01-18</p>
                      </div>
                      <span class="pending-indicator">Pending</span>
                    </div>
                  </div>
                  <div class="border border-gray-200 rounded-lg p-4 hover:border-primary cursor-pointer transition-colors" onclick="showEvaluationForm('CR-2024-003')">
                    <div class="flex items-center justify-between">
                      <div>
                        <h4 class="text-sm font-medium text-gray-900">Lilongwe University Radio</h4>
                        <p class="text-xs text-gray-500">Application ID: CR-2024-003</p>
                        <p class="text-xs text-gray-500">Type: Campus Community Radio</p>
                        <p class="text-xs text-gray-500">Submitted: 2024-01-15</p>
                      </div>
                      <span class="pending-indicator">Pending</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Evaluation Criteria Overview -->
            <div class="evaluation-criteria mb-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">
                <i class="ri-information-line mr-2"></i>
                University Campus Radio Broadcasting License Evaluation Criteria
              </h3>
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                <div class="criteria-item">
                  <span class="text-sm font-medium text-gray-700">General Information</span>
                  <span class="text-sm font-bold text-primary">5 marks</span>
                </div>
                <div class="criteria-item">
                  <span class="text-sm font-medium text-gray-700">Legal Status</span>
                  <span class="text-sm font-bold text-primary">15 marks</span>
                </div>
                <div class="criteria-item">
                  <span class="text-sm font-medium text-gray-700">Demand, Need & Support</span>
                  <span class="text-sm font-bold text-primary">15 marks</span>
                </div>
                <div class="criteria-item">
                  <span class="text-sm font-medium text-gray-700">Programming</span>
                  <span class="text-sm font-bold text-primary">20 marks</span>
                </div>
              </div>
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div class="criteria-item">
                  <span class="text-sm font-medium text-gray-700">Finance</span>
                  <span class="text-sm font-bold text-primary">20 marks</span>
                </div>
                <div class="criteria-item">
                  <span class="text-sm font-medium text-gray-700">Technical</span>
                  <span class="text-sm font-bold text-primary">20 marks</span>
                </div>
                <div class="criteria-item">
                  <span class="text-sm font-medium text-gray-700">Conclusion</span>
                  <span class="text-sm font-bold text-primary">5 marks</span>
                </div>
                <div class="criteria-item">
                  <span class="text-sm font-medium text-gray-700">Total Score</span>
                  <span class="text-sm font-bold text-primary">100 marks</span>
                </div>
              </div>
              <div class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                <p class="text-sm text-yellow-800">
                  <i class="ri-alert-line mr-1"></i>
                  <strong>Minimum Score:</strong> 70% (70 marks out of 100) required for campus radio license approval.
                </p>
              </div>
            </div>

            <!-- Evaluation Form -->
            <div id="evaluationForm" class="bg-white shadow overflow-hidden sm:rounded-lg" style="display: none;">
              <div class="px-4 py-5 sm:p-6">
                <div class="flex items-center justify-between mb-6">
                  <div>
                    <h3 class="text-lg font-medium text-gray-900">Campus Radio Broadcasting License Evaluation</h3>
                    <p class="text-sm text-gray-500" id="selectedApplication">Evaluating: CR-2024-001 - University of Malawi Radio</p>
                  </div>
                  <div class="flex items-center space-x-4">
                    <div class="text-right">
                      <p class="text-sm text-gray-500">Total Score</p>
                      <p class="text-2xl font-bold text-primary" id="totalScore">0/100</p>
                    </div>
                  </div>
                </div>

                <form class="space-y-6">
                  <!-- Section 1: General Information (5 marks) -->
                  <div class="border border-gray-200 rounded-lg p-6">
                    <div class="flex items-center justify-between mb-4">
                      <h4 class="text-md font-medium text-gray-900">Section 1: General Information</h4>
                      <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-500">Weight: 5 marks</span>
                        <span class="text-sm font-medium text-primary" id="section1Score">0/5</span>
                      </div>
                    </div>
                    <div class="space-y-4">
                      <div class="flex items-center justify-between">
                        <label class="block text-sm font-medium text-gray-700">Application Completeness & Accuracy</label>
                        <div class="flex items-center">
                          <button type="button" class="view-doc-btn" onclick="openPdfModal('application-completeness')">
                            <i class="ri-file-text-line mr-1"></i> View Document
                          </button>
                          <input type="number" min="0" max="5" value="0" class="marks-input ml-2" onchange="updateScores()" data-section="1" data-max="5">
                        </div>
                      </div>
                      <div class="text-xs text-gray-500">
                        Evaluate the completeness and accuracy of the general information provided in the application.
                      </div>
                    </div>
                  </div>

                  <!-- Section 2: Legal Status (15 marks) -->
                  <div class="border border-gray-200 rounded-lg p-6">
                    <div class="flex items-center justify-between mb-4">
                      <h4 class="text-md font-medium text-gray-900">Section 2: Legal Status</h4>
                      <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-500">Weight: 15 marks</span>
                        <span class="text-sm font-medium text-primary" id="section2Score">0/15</span>
                      </div>
                    </div>
                    <div class="space-y-4">
                      <div class="flex items-center justify-between">
                        <label class="block text-sm font-medium text-gray-700">Applicant's Legal Form</label>
                        <div class="flex items-center">
                          <button type="button" class="view-doc-btn" onclick="openPdfModal('legal-form')">
                            <i class="ri-file-text-line mr-1"></i> View Document
                          </button>
                          <input type="number" min="0" max="3" value="0" class="marks-input ml-2" onchange="updateScores()" data-section="2" data-subsection="legal">
                          <span class="text-xs text-gray-500 ml-2">Max: 3</span>
                        </div>
                      </div>
                      <div class="flex items-center justify-between">
                        <label class="block text-sm font-medium text-gray-700">Management and Staffing</label>
                        <div class="flex items-center">
                          <button type="button" class="view-doc-btn" onclick="openPdfModal('management-staffing')">
                            <i class="ri-file-text-line mr-1"></i> View Document
                          </button>
                          <input type="number" min="0" max="3" value="0" class="marks-input ml-2" onchange="updateScores()" data-section="2" data-subsection="management">
                          <span class="text-xs text-gray-500 ml-2">Max: 3</span>
                        </div>
                      </div>
                      <div class="flex items-center justify-between">
                        <label class="block text-sm font-medium text-gray-700">Applicant's Profile</label>
                        <div class="flex items-center">
                          <button type="button" class="view-doc-btn" onclick="openPdfModal('applicant-profile')">
                            <i class="ri-file-text-line mr-1"></i> View Document
                          </button>
                          <input type="number" min="0" max="3" value="0" class="marks-input ml-2" onchange="updateScores()" data-section="2" data-subsection="profile">
                          <span class="text-xs text-gray-500 ml-2">Max: 3</span>
                        </div>
                      </div>
                      <div class="flex items-center justify-between">
                        <label class="block text-sm font-medium text-gray-700">External Assistance</label>
                        <div class="flex items-center">
                          <button type="button" class="view-doc-btn" onclick="openPdfModal('external-assistance')">
                            <i class="ri-file-text-line mr-1"></i> View Document
                          </button>
                          <input type="number" min="0" max="3" value="0" class="marks-input ml-2" onchange="updateScores()" data-section="2" data-subsection="external">
                          <span class="text-xs text-gray-500 ml-2">Max: 3</span>
                        </div>
                      </div>
                      <div class="flex items-center justify-between">
                        <label class="block text-sm font-medium text-gray-700">Other Interests</label>
                        <div class="flex items-center">
                          <button type="button" class="view-doc-btn" onclick="openPdfModal('other-interests')">
                            <i class="ri-file-text-line mr-1"></i> View Document
                          </button>
                          <input type="number" min="0" max="3" value="0" class="marks-input ml-2" onchange="updateScores()" data-section="2" data-subsection="interests">
                          <span class="text-xs text-gray-500 ml-2">Max: 3</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Section 3: Demand, Need, and Support (15 marks) -->
                  <div class="border border-gray-200 rounded-lg p-6">
                    <div class="flex items-center justify-between mb-4">
                      <h4 class="text-md font-medium text-gray-900">Section 3: Demand, Need, and Support</h4>
                      <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-500">Weight: 15 marks</span>
                        <span class="text-sm font-medium text-primary" id="section3Score">0/15</span>
                      </div>
                    </div>
                    <div class="space-y-4">
                      <div class="flex items-center justify-between">
                        <label class="block text-sm font-medium text-gray-700">Appeal of Programmed Services</label>
                        <div class="flex items-center">
                          <button type="button" class="view-doc-btn" onclick="openPdfModal('programmed-services')">
                            <i class="ri-file-text-line mr-1"></i> View Document
                          </button>
                          <input type="number" min="0" max="4" value="0" class="marks-input ml-2" onchange="updateScores()" data-section="3" data-subsection="appeal">
                          <span class="text-xs text-gray-500 ml-2">Max: 4</span>
                        </div>
                      </div>
                      <div class="flex items-center justify-between">
                        <label class="block text-sm font-medium text-gray-700">Demand and Local Support</label>
                        <div class="flex items-center">
                          <button type="button" class="view-doc-btn" onclick="openPdfModal('local-support')">
                            <i class="ri-file-text-line mr-1"></i> View Document
                          </button>
                          <input type="number" min="0" max="5" value="0" class="marks-input ml-2" onchange="updateScores()" data-section="3" data-subsection="demand">
                          <span class="text-xs text-gray-500 ml-2">Max: 5</span>
                        </div>
                      </div>
                      <div class="flex items-center justify-between">
                        <label class="block text-sm font-medium text-gray-700">Complaints and Codes of Operation</label>
                        <div class="flex items-center">
                          <button type="button" class="view-doc-btn" onclick="openPdfModal('codes-operation')">
                            <i class="ri-file-text-line mr-1"></i> View Document
                          </button>
                          <input type="number" min="0" max="2" value="0" class="marks-input ml-2" onchange="updateScores()" data-section="3" data-subsection="complaints">
                          <span class="text-xs text-gray-500 ml-2">Max: 2</span>
                        </div>
                      </div>
                      <div class="flex items-center justify-between">
                        <label class="block text-sm font-medium text-gray-700">Business Plan</label>
                        <div class="flex items-center">
                          <button type="button" class="view-doc-btn" onclick="openPdfModal('business-plan')">
                            <i class="ri-file-text-line mr-1"></i> View Document
                          </button>
                          <input type="number" min="0" max="4" value="0" class="marks-input ml-2" onchange="updateScores()" data-section="3" data-subsection="business">
                          <span class="text-xs text-gray-500 ml-2">Max: 4</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Section 5: Programming (20 marks) -->
                  <div class="border border-gray-200 rounded-lg p-6">
                    <div class="flex items-center justify-between mb-4">
                      <h4 class="text-md font-medium text-gray-900">Section 5: Programming</h4>
                      <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-500">Weight: 20 marks</span>
                        <span class="text-sm font-medium text-primary" id="section5Score">0/20</span>
                      </div>
                    </div>
                    <div class="space-y-4">
                      <div class="flex items-center justify-between">
                        <label class="block text-sm font-medium text-gray-700">Educational Programmes</label>
                        <div class="flex items-center">
                          <button type="button" class="view-doc-btn" onclick="openPdfModal('educational-programmes')">
                            <i class="ri-file-text-line mr-1"></i> View Document
                          </button>
                          <input type="number" min="0" max="5" value="0" class="marks-input ml-2" onchange="updateScores()" data-section="5" data-subsection="educational">
                          <span class="text-xs text-gray-500 ml-2">Max: 5</span>
                        </div>
                      </div>
                      <div class="flex items-center justify-between">
                        <label class="block text-sm font-medium text-gray-700">News and Editorial Policy</label>
                        <div class="flex items-center">
                          <button type="button" class="view-doc-btn" onclick="openPdfModal('editorial-policy')">
                            <i class="ri-file-text-line mr-1"></i> View Document
                          </button>
                          <input type="number" min="0" max="5" value="0" class="marks-input ml-2" onchange="updateScores()" data-section="5" data-subsection="news">
                          <span class="text-xs text-gray-500 ml-2">Max: 5</span>
                        </div>
                      </div>
                      <div class="flex items-center justify-between">
                        <label class="block text-sm font-medium text-gray-700">Programme Format</label>
                        <div class="flex items-center">
                          <button type="button" class="view-doc-btn" onclick="openPdfModal('programme-format')">
                            <i class="ri-file-text-line mr-1"></i> View Document
                          </button>
                          <input type="number" min="0" max="5" value="0" class="marks-input ml-2" onchange="updateScores()" data-section="5" data-subsection="format">
                          <span class="text-xs text-gray-500 ml-2">Max: 5</span>
                        </div>
                      </div>
                      <div class="flex items-center justify-between">
                        <label class="block text-sm font-medium text-gray-700">Promotion of Local Talent</label>
                        <div class="flex items-center">
                          <button type="button" class="view-doc-btn" onclick="openPdfModal('local-talent')">
                            <i class="ri-file-text-line mr-1"></i> View Document
                          </button>
                          <input type="number" min="0" max="5" value="0" class="marks-input ml-2" onchange="updateScores()" data-section="5" data-subsection="talent">
                          <span class="text-xs text-gray-500 ml-2">Max: 5</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Section 6: Finance (20 marks) -->
                  <div class="border border-gray-200 rounded-lg p-6">
                    <div class="flex items-center justify-between mb-4">
                      <h4 class="text-md font-medium text-gray-900">Section 6: Finance</h4>
                      <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-500">Weight: 20 marks</span>
                        <span class="text-sm font-medium text-primary" id="section6Score">0/20</span>
                      </div>
                    </div>
                    <div class="space-y-4">
                      <div class="flex items-center justify-between">
                        <label class="block text-sm font-medium text-gray-700">Financial Matters</label>
                        <div class="flex items-center">
                          <button type="button" class="view-doc-btn" onclick="openPdfModal('financial-matters')">
                            <i class="ri-file-text-line mr-1"></i> View Document
                          </button>
                          <input type="number" min="0" max="20" value="0" class="marks-input ml-2" onchange="updateScores()" data-section="6" data-max="20">
                          <span class="text-xs text-gray-500 ml-2">Max: 20</span>
                        </div>
                      </div>
                      <div class="text-xs text-gray-500">
                        Evaluate financial capacity, sustainability plan, budget adequacy, and funding sources.
                      </div>
                    </div>
                  </div>

                  <!-- Section 7: Technical (20 marks) -->
                  <div class="border border-gray-200 rounded-lg p-6">
                    <div class="flex items-center justify-between mb-4">
                      <h4 class="text-md font-medium text-gray-900">Section 7: Technical</h4>
                      <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-500">Weight: 20 marks</span>
                        <span class="text-sm font-medium text-primary" id="section7Score">0/20</span>
                      </div>
                    </div>
                    <div class="space-y-4">
                      <div class="flex items-center justify-between">
                        <label class="block text-sm font-medium text-gray-700">Technical Capacity</label>
                        <div class="flex items-center">
                          <button type="button" class="view-doc-btn" onclick="openPdfModal('technical-capacity')">
                            <i class="ri-file-text-line mr-1"></i> View Document
                          </button>
                          <input type="number" min="0" max="5" value="0" class="marks-input ml-2" onchange="updateScores()" data-section="7" data-subsection="capacity">
                          <span class="text-xs text-gray-500 ml-2">Max: 5</span>
                        </div>
                      </div>
                      <div class="flex items-center justify-between">
                        <label class="block text-sm font-medium text-gray-700">Transmission Equipment</label>
                        <div class="flex items-center">
                          <button type="button" class="view-doc-btn" onclick="openPdfModal('transmission-equipment')">
                            <i class="ri-file-text-line mr-1"></i> View Document
                          </button>
                          <input type="number" min="0" max="2" value="0" class="marks-input ml-2" onchange="updateScores()" data-section="7" data-subsection="equipment">
                          <span class="text-xs text-gray-500 ml-2">Max: 2</span>
                        </div>
                      </div>
                      <div class="flex items-center justify-between">
                        <label class="block text-sm font-medium text-gray-700">Maintenance of Equipment</label>
                        <div class="flex items-center">
                          <button type="button" class="view-doc-btn" onclick="openPdfModal('equipment-maintenance')">
                            <i class="ri-file-text-line mr-1"></i> View Document
                          </button>
                          <input type="number" min="0" max="3" value="0" class="marks-input ml-2" onchange="updateScores()" data-section="7" data-subsection="maintenance">
                          <span class="text-xs text-gray-500 ml-2">Max: 3</span>
                        </div>
                      </div>
                      <div class="flex items-center justify-between">
                        <label class="block text-sm font-medium text-gray-700">Coverage Target Area</label>
                        <div class="flex items-center">
                          <button type="button" class="view-doc-btn" onclick="openPdfModal('coverage-area')">
                            <i class="ri-file-text-line mr-1"></i> View Document
                          </button>
                          <input type="number" min="0" max="5" value="0" class="marks-input ml-2" onchange="updateScores()" data-section="7" data-subsection="coverage">
                          <span class="text-xs text-gray-500 ml-2">Max: 5</span>
                        </div>
                      </div>
                      <div class="flex items-center justify-between">
                        <label class="block text-sm font-medium text-gray-700">Roll-Out Plans</label>
                        <div class="flex items-center">
                          <button type="button" class="view-doc-btn" onclick="openPdfModal('rollout-plans')">
                            <i class="ri-file-text-line mr-1"></i> View Document
                          </button>
                          <input type="number" min="0" max="5" value="0" class="marks-input ml-2" onchange="updateScores()" data-section="7" data-subsection="rollout">
                          <span class="text-xs text-gray-500 ml-2">Max: 5</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Section 8: Conclusion (5 marks) -->
                  <div class="border border-gray-200 rounded-lg overflow-hidden">
                    <div class="section-header">
                      <div class="flex items-center justify-between">
                        <h4 class="text-md font-medium">Section 8: Conclusion</h4>
                        <span class="score-display" id="section8Score">0/5</span>
                      </div>
                    </div>
                    <div class="p-6 space-y-4">
                      <div class="flex items-center justify-between">
                        <label class="block text-sm font-medium text-gray-700">Other Matters</label>
                        <div class="flex items-center">
                          <button type="button" class="view-doc-btn" onclick="openPdfModal('other-matters')">
                            <i class="ri-file-text-line mr-1"></i> View Document
                          </button>
                          <input type="number" min="0" max="3" value="0" class="marks-input ml-2" onchange="updateScores()" data-section="8" data-subsection="other">
                          <span class="text-xs text-gray-500 ml-2">Max: 3</span>
                        </div>
                      </div>
                      <div class="flex items-center justify-between">
                        <label class="block text-sm font-medium text-gray-700">Declaration</label>
                        <div class="flex items-center">
                          <button type="button" class="view-doc-btn" onclick="openPdfModal('declaration')">
                            <i class="ri-file-text-line mr-1"></i> View Document
                          </button>
                          <input type="number" min="0" max="2" value="0" class="marks-input ml-2" onchange="updateScores()" data-section="8" data-subsection="declaration">
                          <span class="text-xs text-gray-500 ml-2">Max: 2</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Evaluation Summary -->
                  <div class="border border-gray-200 rounded-lg p-6 bg-gray-50">
                    <h4 class="text-md font-medium text-gray-900 mb-4">Evaluation Summary</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h5 class="text-sm font-medium text-gray-700 mb-3">Score Breakdown</h5>
                        <div class="space-y-2">
                          <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">Section 1: General Information</span>
                            <span class="text-sm font-medium" id="finalSection1">0/5</span>
                          </div>
                          <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">Section 2: Legal Status</span>
                            <span class="text-sm font-medium" id="finalSection2">0/15</span>
                          </div>
                          <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">Section 3: Demand, Need & Support</span>
                            <span class="text-sm font-medium" id="finalSection3">0/15</span>
                          </div>
                          <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">Section 5: Programming</span>
                            <span class="text-sm font-medium" id="finalSection5">0/20</span>
                          </div>
                          <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">Section 6: Finance</span>
                            <span class="text-sm font-medium" id="finalSection6">0/20</span>
                          </div>
                          <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">Section 7: Technical</span>
                            <span class="text-sm font-medium" id="finalSection7">0/20</span>
                          </div>
                          <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">Section 8: Conclusion</span>
                            <span class="text-sm font-medium" id="finalSection8">0/5</span>
                          </div>
                          <div class="border-t pt-2 mt-2">
                            <div class="flex justify-between items-center">
                              <span class="text-sm font-medium text-gray-900">Total Score</span>
                              <span class="text-lg font-bold text-primary" id="finalTotal">0/100 (0%)</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div>
                        <h5 class="text-sm font-medium text-gray-700 mb-3">Recommendation</h5>
                        <div id="recommendationBox" class="p-4 rounded-lg border">
                          <div id="recommendationContent">
                            <p class="text-sm text-gray-600">Complete the evaluation to see recommendation.</p>
                          </div>
                        </div>
                        <div class="mt-4">
                          <label class="block text-sm font-medium text-gray-700">Evaluator Notes</label>
                          <textarea rows="4" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm" placeholder="Add overall evaluation notes and recommendations for campus radio license..."></textarea>
                        </div>
                        <div class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                          <p class="text-sm text-blue-800">
                            <i class="ri-information-line mr-1"></i>
                            <strong>Campus Radio Focus:</strong> Evaluate educational content, community service, and university integration.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Action Buttons -->
                  <div class="flex justify-between items-center pt-6 border-t border-gray-200">
                    <button type="button" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                      <i class="ri-save-line mr-2"></i>
                      Save Draft
                    </button>
                    <div class="flex space-x-3">
                      <button type="button" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                        <i class="ri-close-line mr-2"></i>
                        Reject Application
                      </button>
                      <button type="button" class="enhanced-button">
                        <i class="ri-check-line mr-2"></i>
                        Approve Application
                      </button>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>

    <script>
      function showEvaluationForm(applicationId) {
        document.getElementById('evaluationForm').style.display = 'block';
        document.getElementById('evaluationForm').scrollIntoView({ behavior: 'smooth' });
      }

      function updateScores() {
        // Calculate section totals
        let section1Total = 0;
        let section2Total = 0;
        let section3Total = 0;
        let section5Total = 0;
        let section6Total = 0;
        let section7Total = 0;
        let section8Total = 0;

        // Section 1 (5 marks)
        const section1Inputs = document.querySelectorAll('input[data-section="1"]');
        section1Inputs.forEach(input => {
          section1Total += parseInt(input.value) || 0;
        });

        // Section 2 (15 marks)
        const section2Inputs = document.querySelectorAll('input[data-section="2"]');
        section2Inputs.forEach(input => {
          section2Total += parseInt(input.value) || 0;
        });

        // Section 3 (15 marks)
        const section3Inputs = document.querySelectorAll('input[data-section="3"]');
        section3Inputs.forEach(input => {
          section3Total += parseInt(input.value) || 0;
        });

        // Section 5 (20 marks)
        const section5Inputs = document.querySelectorAll('input[data-section="5"]');
        section5Inputs.forEach(input => {
          section5Total += parseInt(input.value) || 0;
        });

        // Section 6 (20 marks)
        const section6Inputs = document.querySelectorAll('input[data-section="6"]');
        section6Inputs.forEach(input => {
          section6Total += parseInt(input.value) || 0;
        });

        // Section 7 (20 marks)
        const section7Inputs = document.querySelectorAll('input[data-section="7"]');
        section7Inputs.forEach(input => {
          section7Total += parseInt(input.value) || 0;
        });

        // Section 8 (5 marks)
        const section8Inputs = document.querySelectorAll('input[data-section="8"]');
        section8Inputs.forEach(input => {
          section8Total += parseInt(input.value) || 0;
        });

        // Update section displays
        document.getElementById('section1Score').textContent = `${section1Total}/5`;
        document.getElementById('section2Score').textContent = `${section2Total}/15`;
        document.getElementById('section3Score').textContent = `${section3Total}/15`;
        document.getElementById('section5Score').textContent = `${section5Total}/20`;
        document.getElementById('section6Score').textContent = `${section6Total}/20`;
        document.getElementById('section7Score').textContent = `${section7Total}/20`;
        document.getElementById('section8Score').textContent = `${section8Total}/5`;

        // Calculate total
        const totalScore = section1Total + section2Total + section3Total + section5Total + section6Total + section7Total + section8Total;
        const percentage = Math.round((totalScore / 100) * 100);

        // Update total displays
        document.getElementById('totalScore').textContent = `${totalScore}/100`;
        document.getElementById('finalTotal').textContent = `${totalScore}/100 (${percentage}%)`;

        // Update final section scores
        document.getElementById('finalSection1').textContent = `${section1Total}/5`;
        document.getElementById('finalSection2').textContent = `${section2Total}/15`;
        document.getElementById('finalSection3').textContent = `${section3Total}/15`;
        document.getElementById('finalSection5').textContent = `${section5Total}/20`;
        document.getElementById('finalSection6').textContent = `${section6Total}/20`;
        document.getElementById('finalSection7').textContent = `${section7Total}/20`;
        document.getElementById('finalSection8').textContent = `${section8Total}/5`;

        // Update recommendation
        const recommendationBox = document.getElementById('recommendationBox');
        const recommendationContent = document.getElementById('recommendationContent');

        if (percentage >= 70) {
          recommendationBox.className = 'p-4 rounded-lg border bg-green-50 border-green-200';
          recommendationContent.innerHTML = `
            <div class="flex items-center">
              <i class="ri-check-circle-line text-green-600 mr-2"></i>
              <span class="text-sm font-medium text-green-800">RECOMMEND FOR APPROVAL</span>
            </div>
            <p class="text-sm text-green-700 mt-1">Score: ${percentage}% (≥70% required)</p>
          `;
        } else if (percentage >= 60) {
          recommendationBox.className = 'p-4 rounded-lg border bg-yellow-50 border-yellow-200';
          recommendationContent.innerHTML = `
            <div class="flex items-center">
              <i class="ri-alert-circle-line text-yellow-600 mr-2"></i>
              <span class="text-sm font-medium text-yellow-800">CONDITIONAL APPROVAL</span>
            </div>
            <p class="text-sm text-yellow-700 mt-1">Score: ${percentage}% - Consider additional requirements</p>
          `;
        } else {
          recommendationBox.className = 'p-4 rounded-lg border bg-red-50 border-red-200';
          recommendationContent.innerHTML = `
            <div class="flex items-center">
              <i class="ri-close-circle-line text-red-600 mr-2"></i>
              <span class="text-sm font-medium text-red-800">RECOMMEND FOR REJECTION</span>
            </div>
            <p class="text-sm text-red-700 mt-1">Score: ${percentage}% (Below 70% minimum)</p>
          `;
        }
      }

      // Toggle dropdown functionality
      document.getElementById('userDropdown').addEventListener('click', function() {
        const dropdown = document.getElementById('userDropdownContent');
        dropdown.classList.toggle('show');
      });

      // Close dropdown when clicking outside
      window.addEventListener('click', function(event) {
        if (!event.target.matches('#userDropdown') && !event.target.closest('#userDropdown')) {
          const dropdown = document.getElementById('userDropdownContent');
          dropdown.classList.remove('show');
        }
      });

      // PDF Viewer Modal Functions
      function openPdfModal(documentType) {
        // For demo purposes, we'll use a single sample PDF file
        // In a real application, you would have different PDFs for each document type
        const pdfSource = '../sample-documents/sample.pdf';
        document.getElementById('pdfViewer').src = pdfSource;
        
        // Update modal title
        const documentTitle = documentType.split('-').map(word => 
          word.charAt(0).toUpperCase() + word.slice(1)
        ).join(' ');
        document.getElementById('pdfModalTitle').textContent = documentTitle + ' Document';
        
        // Show the modal
        document.getElementById('pdfModal').style.display = 'block';
      }

      function closePdfModal() {
        document.getElementById('pdfModal').style.display = 'none';
      }

      // Close modal when clicking outside of it
      window.onclick = function(event) {
        const modal = document.getElementById('pdfModal');
        if (event.target == modal) {
          modal.style.display = 'none';
        }
      }
    </script>

    <!-- PDF Viewer Modal -->
    <div id="pdfModal" class="modal">
      <div class="modal-content">
        <span class="close-modal" onclick="closePdfModal()">&times;</span>
        <h2 id="pdfModalTitle" class="text-xl font-semibold mb-4">Document Viewer</h2>
        <iframe id="pdfViewer" class="pdf-viewer" src="" frameborder="0"></iframe>
      </div>
    </div>
  </body>
</html>
