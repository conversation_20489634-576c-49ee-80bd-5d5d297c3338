@startuml
|Applicant|
:Access online portal;
:Select license type;
note right: REQ_P01
:Select sub-type;
note right: REQ_P02
:Enter address details;
:Upload documents;
note right: REQ_P06
:Submit application;

|System|
:Validate address via MNAS;
note right: REQ_P03

if (Address valid?) then (yes)
  :Save application;
  :Route to Macra Staff;
  note right: REQ_P05

  |Macra Staff|
  :Review application and record evaluations;
  note right: REQ_P07

  if (Approved by committee?) then (yes)
    :Board decision;
    note right: REQ_P07

    if (Approved by Board?) then (yes)
      |System|
      :Generate invoice;
      note right: REQ_P08
      :Notify applicant (approval + invoice);
      note right: REQ_P10

      |Applicant|
      :Make payment;
      note right: REQ_P09

      |System|
      :Validate payment;
      note right: REQ_P09

      if (Payment valid?) then (yes)
        :Notify applicant (payment confirmed);
        note right: REQ_P10

        |Macra Staff|
        :Generate license with QR;
        note right: REQ_P11

        |System|
        :Send license;
        :Track license validity;
        note right: REQ_P11, REQ_P12
        :Notify applicant (license issued);
        note right: REQ_P10

        |Applicant|
        :View license;
        stop
      else (no)
        :Notify applicant (payment failed);
        note right: REQ_P10
        |Applicant|
        :Retry payment;
        note right: REQ_P09
      endif

    else (no)
      |System|
      :Update status: Rejected;
      :Notify applicant;
      note right: REQ_P10
      stop
    endif

  else (no)
    if (Pending info?) then (yes)
      |System|
      :Update status: Pending Info;
      :Notify applicant;
      note right: REQ_P10
      |Applicant|
      :Upload additional documents;
      note right: REQ_P06
    else (no)
      |System|
      :Update status: Rejected;
      :Notify applicant;
      note right: REQ_P10
      stop
    endif
  endif

else (no)
  :Notify applicant (invalid address);
  note right: REQ_P10
  |Applicant|
  :Revise address;
endif
@enduml
