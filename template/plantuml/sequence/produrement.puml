@startuml
actor User
actor DepartmentalManager
actor ProcurementOfficer
actor ProcurementManager
actor HeadOfDepartment
actor HeadOfProcurement
actor DirectorOfFinance
actor DG
actor Supplier
actor IPDC
actor FinanceModule

User -> System: Initiate Direct/Procurement-Based Requisition
System -> DepartmentalManager: Route for Authorization
DepartmentalManager -> System: Authorize Requisition
System -> ProcurementOfficer: Route for Verification
System -> ProcurementManager: Assign Procurement Officer
ProcurementOfficer -> System: Verify Requisition
System -> HeadOfDepartment: Route for Approval
HeadOfDepartment -> System: Approve Requisition
alt RFQ (if under MWK 100M)
    ProcurementOfficer -> System: Prepare RFQ
    System -> Supplier: Send RFQ
    Supplier -> System: Submit Quotation
    ProcurementOfficer -> System: Evaluate Quotations
    System -> IPDC: Submit Evaluation
else Tendering (NCB/OIB)
    ProcurementOfficer -> System: Create Tender Advertisement
    System -> Supplier: Publish Tender
    Supplier -> System: Submit Tender by Deadline
    System -> ProcurementOfficer: Conduct Tender Opening
end alt
System -> ProcurementManager: Generate LPO
ProcurementManager -> HeadOfProcurement: Route LPO for Approval
HeadOfProcurement -> DirectorOfFinance: Route LPO for Approval
DirectorOfFinance -> DG: Route LPO for Approval
DG -> System: Approve LPO
System -> Supplier: Share Approved LPO (Email/Portal)
Supplier -> System: Record Delivery Details
ProcurementOfficer -> System: Submit Inspection/Verification Report
System -> System: Generate GRN
Supplier -> System: Upload Invoice & Payment Claim
System -> FinanceModule: Generate Payment Voucher
FinanceModule -> System: Trigger EFT Payment
System -> Supplier: Notify Payment Processed
Supplier -> System: Download Proof of Payment
System -> User: Display Dashboard with Progress & Tracking
System -> Supplier: Display Dashboard with Progress & Tracking
System -> IPDC: Generate IPDC Reports
System -> FinanceModule: Generate Financial Summaries

@enduml