<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Spectrum License Application - Digital Portal Dashboard</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: { primary: "#e02b20", secondary: "#6366f1" },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"
    />
    <style>
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
        font-family: 'Inter', sans-serif;
        background-color: #f9fafb;
      }

      /* Enhanced form styles */
      .enhanced-input {
        @apply appearance-none block w-full px-4 py-3 border-2 border-gray-300 rounded-md shadow-sm
        placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary
        sm:text-sm bg-gray-50 hover:bg-white transition-colors;
      }

      .enhanced-select {
        @apply block w-full px-4 py-3 border-2 border-gray-300 rounded-md shadow-sm
        focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary
        sm:text-sm bg-gray-50 hover:bg-white transition-colors;
      }

      .enhanced-checkbox {
        @apply h-5 w-5 text-primary focus:ring-2 focus:ring-primary border-2 border-gray-300 rounded;
      }

      .enhanced-button {
        @apply flex justify-center py-3 px-4 border-2 border-transparent rounded-md shadow-md
        text-sm font-medium text-white bg-primary hover:bg-opacity-90 focus:outline-none
        focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all;
      }
      .side-nav{
        overflow: auto;
        -ms-overflow-style: none;
        height: 75vh;
      }
      .side-nav::-webkit-scrollbar {
        display: none;
      }
      .side-nav {
        scrollbar-width: none;
      }
    </style>
  </head>
  <body>
    <div class="flex h-screen overflow-hidden">
      <!-- Sidebar -->
      <aside class="w-64 bg-white shadow-lg flex-shrink-0 hidden md:block">
        <div class="h-16 flex items-center px-6 border-b">
          <div class="flex items-center">
            <img src="../images/macra-logo.png" alt="Logo" class="max-h-12 w-auto">
          </div>
        </div>
        <nav class="mt-6 px-4 side-nav">
          <div class="space-y-1">
            <a
              href="../index.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-dashboard-line"></i>
              </div>
              Dashboard
            </a>
            <a
              href="../user-management/user-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-user-line"></i>
              </div>
              User Management
            </a>
            <a
              href="../license/license-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-key-line"></i>
              </div>
              License Management
            </a>
            <a
              href="spectrum-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z" />
                </svg>
              </div>
              Spectrum Management
            </a>
            <a
              href="../financial/transaction-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-exchange-dollar-line"></i>
              </div>
              Transaction Management
            </a>
            <a
              href="../reports/reports.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-file-chart-line"></i>
              </div>
              Reports
            </a>
          </div>
          <div class="mt-8">
            <h3
              class="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider"
            >
              Spectrum Tools
            </h3>
            <div class="mt-2 space-y-1">
              <a
                href="../frequency-allocation.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-radar-line"></i>
                </div>
                Frequency Allocation
              </a>
              <a
                href="spectrum-monitoring.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-pulse-line"></i>
                </div>
                Spectrum Monitoring
              </a>
              <a
                href="interference-management.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-error-warning-line"></i>
                </div>
                Interference Management
              </a>
              <a
                href="spectrum-license-application.html"
                class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md bg-primary bg-opacity-10 text-primary"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-file-list-3-line"></i>
                </div>
                License Application
              </a>
            </div>
          </div>
          <div class="mt-8">
            <h3
              class="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider"
            >
              Settings
            </h3>
            <div class="mt-2 space-y-1">
              <a
                href="../user-management/user-profile.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-user-settings-line"></i>
                </div>
                My Profile
              </a>
              <a
                href="../system-settings.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-settings-3-line"></i>
                </div>
                Settings
              </a>
            </div>
          </div>
        </nav>
      </aside>

      <!-- Main content -->
      <div class="flex-1 flex flex-col overflow-hidden">
        <!-- Top navigation -->
        <header class="bg-white shadow-sm z-10">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
              <div class="flex">
                <div class="flex-shrink-0 flex items-center md:hidden">
                  <img src="../images/macra-logo.png" alt="Logo" class="h-8 w-auto">
                </div>
                <button
                  type="button"
                  class="md:hidden inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary"
                >
                  <span class="sr-only">Open main menu</span>
                  <div class="w-6 h-6 flex items-center justify-center">
                    <i class="ri-menu-line ri-lg"></i>
                  </div>
                </button>
              </div>
              <div
                class="flex-1 flex items-center justify-center px-2 lg:ml-6 lg:justify-start"
              >
                <div class="max-w-lg w-full">
                  <label for="search" class="sr-only">Search</label>
                  <div class="relative">
                    <div
                      class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                    >
                      <div
                        class="w-5 h-5 flex items-center justify-center text-gray-400"
                      >
                        <i class="ri-search-line"></i>
                      </div>
                    </div>
                    <input
                      id="search"
                      name="search"
                      class="block w-full pl-10 pr-3 py-3 border-2 border-gray-300 rounded-md leading-5 bg-gray-50 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary text-sm hover:bg-white transition-colors"
                      placeholder="Search for applications..."
                      type="search"
                    />
                  </div>
                </div>
              </div>
              <div class="flex items-center">
                <div class="flex-shrink-0 relative ml-4">
                  <div>
                    <button
                      type="button"
                      class="bg-white rounded-full flex focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                    >
                      <span class="sr-only">View notifications</span>
                      <div
                        class="w-6 h-6 flex items-center justify-center text-gray-400"
                      >
                        <i class="ri-notification-3-line"></i>
                      </div>
                    </button>
                  </div>
                </div>
                <div class="ml-4 relative flex-shrink-0">
                  <div>
                    <button
                      type="button"
                      class="bg-white rounded-full flex text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                    >
                      <span class="sr-only">Open user menu</span>
                      <img
                        class="h-8 w-8 rounded-full"
                        src="https://images.unsplash.com/photo-1550525811-e5869dd03032?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
                        alt=""
                      />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </header>

        <!-- Main content area -->
        <main class="flex-1 overflow-y-auto bg-gray-50 p-4 sm:p-6">
          <div class="max-w-7xl mx-auto">
            <!-- Page header -->
            <div class="mb-6">
              <div class="flex items-center justify-between">
                <h1 class="text-2xl font-semibold text-gray-900">Spectrum License Application</h1>
                <div>
                  <button
                    type="button"
                    class="inline-flex items-center px-4 py-3 border-2 border-transparent shadow-md text-sm font-medium rounded-md text-white bg-primary hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all"
                  >
                    <div class="w-5 h-5 flex items-center justify-center mr-2">
                      <i class="ri-add-line"></i>
                    </div>
                    New Application
                  </button>
                </div>
              </div>
              <p class="mt-1 text-sm text-gray-500">
                Apply for and manage spectrum license applications.
              </p>
            </div>

            <!-- Application form -->
            <div class="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
              <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900">
                  New Spectrum License Application
                </h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500">
                  Fill out the form below to apply for a new spectrum license.
                </p>
              </div>
              <div class="px-4 py-5 sm:p-6">
                <form>
                  <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    <div class="sm:col-span-3">
                      <label for="applicant-name" class="block text-sm font-medium text-gray-700">
                        Applicant Name
                      </label>
                      <div class="mt-1">
                        <input type="text" name="applicant-name" id="applicant-name" class="enhanced-input">
                      </div>
                    </div>

                    <div class="sm:col-span-3">
                      <label for="organization" class="block text-sm font-medium text-gray-700">
                        Organization
                      </label>
                      <div class="mt-1">
                        <input type="text" name="organization" id="organization" class="enhanced-input">
                      </div>
                    </div>

                    <div class="sm:col-span-3">
                      <label for="email" class="block text-sm font-medium text-gray-700">
                        Email Address
                      </label>
                      <div class="mt-1">
                        <input type="email" name="email" id="email" autocomplete="email" class="enhanced-input">
                      </div>
                    </div>

                    <div class="sm:col-span-3">
                      <label for="phone" class="block text-sm font-medium text-gray-700">
                        Phone Number
                      </label>
                      <div class="mt-1">
                        <input type="tel" name="phone" id="phone" class="enhanced-input">
                      </div>
                    </div>

                    <div class="sm:col-span-3">
                      <label for="license-type" class="block text-sm font-medium text-gray-700">
                        License Type
                      </label>
                      <div class="mt-1">
                        <select id="license-type" name="license-type" class="enhanced-select">
                          <option>Broadcasting</option>
                          <option>Mobile Communications</option>
                          <option>Fixed Wireless</option>
                          <option>Satellite</option>
                          <option>Amateur Radio</option>
                          <option>Other</option>
                        </select>
                      </div>
                    </div>

                    <div class="sm:col-span-3">
                      <label for="frequency-band" class="block text-sm font-medium text-gray-700">
                        Frequency Band Requested
                      </label>
                      <div class="mt-1">
                        <input type="text" name="frequency-band" id="frequency-band" placeholder="e.g., 87.5-108 MHz" class="enhanced-input">
                      </div>
                    </div>

                    <div class="sm:col-span-3">
                      <label for="bandwidth" class="block text-sm font-medium text-gray-700">
                        Bandwidth Required
                      </label>
                      <div class="mt-1">
                        <input type="text" name="bandwidth" id="bandwidth" placeholder="e.g., 200 kHz" class="enhanced-input">
                      </div>
                    </div>

                    <div class="sm:col-span-3">
                      <label for="location" class="block text-sm font-medium text-gray-700">
                        Geographic Location
                      </label>
                      <div class="mt-1">
                        <input type="text" name="location" id="location" class="enhanced-input">
                      </div>
                    </div>

                    <div class="sm:col-span-6">
                      <label for="purpose" class="block text-sm font-medium text-gray-700">
                        Purpose of Use
                      </label>
                      <div class="mt-1">
                        <textarea id="purpose" name="purpose" rows="3" class="enhanced-input"></textarea>
                      </div>
                      <p class="mt-2 text-sm text-gray-500">Brief description of how the spectrum will be used.</p>
                    </div>

                    <div class="sm:col-span-6">
                      <label for="documents" class="block text-sm font-medium text-gray-700">
                        Supporting Documents
                      </label>
                      <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                        <div class="space-y-1 text-center">
                          <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                          </svg>
                          <div class="flex text-sm text-gray-600">
                            <label for="file-upload" class="relative cursor-pointer bg-white rounded-md font-medium text-primary hover:text-primary focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary">
                              <span>Upload files</span>
                              <input id="file-upload" name="file-upload" type="file" class="sr-only" multiple>
                            </label>
                            <p class="pl-1">or drag and drop</p>
                          </div>
                          <p class="text-xs text-gray-500">
                            PDF, DOC, DOCX, JPG, PNG up to 10MB
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="mt-6 flex justify-end">
                    <button type="button" class="bg-white py-3 px-4 border-2 border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all">
                      Cancel
                    </button>
                    <button type="submit" class="ml-3 inline-flex justify-center py-3 px-4 border-2 border-transparent shadow-md text-sm font-medium rounded-md text-white bg-primary hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all">
                      Submit Application
                    </button>
                  </div>
                </form>
              </div>
            </div>

            <!-- Recent applications -->
            <div class="bg-white shadow overflow-hidden sm:rounded-md">
              <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900">
                  Recent Applications
                </h3>
              </div>
              <ul class="divide-y divide-gray-200">
                <li>
                  <div class="px-4 py-4 sm:px-6">
                    <div class="flex items-center justify-between">
                      <div class="flex items-center">
                        <div class="flex-shrink-0 h-10 w-10 rounded-full bg-purple-100 flex items-center justify-center">
                          <i class="ri-file-list-3-line text-purple-600"></i>
                        </div>
                        <div class="ml-4">
                          <div class="text-sm font-medium text-gray-900">
                            Application #APP-2023-10-001
                          </div>
                          <div class="text-sm text-gray-500">
                            FM Broadcasting (87.5-108 MHz)
                          </div>
                        </div>
                      </div>
                      <div class="ml-2 flex-shrink-0 flex">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                          Under Review
                        </span>
                      </div>
                    </div>
                    <div class="mt-2 sm:flex sm:justify-between">
                      <div class="sm:flex">
                        <div class="flex items-center text-sm text-gray-500">
                          <i class="ri-building-line flex-shrink-0 mr-1.5 text-gray-400"></i>
                          <p>Capital Radio Ltd</p>
                        </div>
                        <div class="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6">
                          <i class="ri-map-pin-line flex-shrink-0 mr-1.5 text-gray-400"></i>
                          <p>Lilongwe Region</p>
                        </div>
                      </div>
                      <div class="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                        <i class="ri-calendar-line flex-shrink-0 mr-1.5 text-gray-400"></i>
                        <p>Applied on: Oct 10, 2023</p>
                      </div>
                    </div>
                  </div>
                </li>
                <li>
                  <div class="px-4 py-4 sm:px-6">
                    <div class="flex items-center justify-between">
                      <div class="flex items-center">
                        <div class="flex-shrink-0 h-10 w-10 rounded-full bg-purple-100 flex items-center justify-center">
                          <i class="ri-file-list-3-line text-purple-600"></i>
                        </div>
                        <div class="ml-4">
                          <div class="text-sm font-medium text-gray-900">
                            Application #APP-2023-09-045
                          </div>
                          <div class="text-sm text-gray-500">
                            5G Mobile (3.5 GHz)
                          </div>
                        </div>
                      </div>
                      <div class="ml-2 flex-shrink-0 flex">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                          Approved
                        </span>
                      </div>
                    </div>
                    <div class="mt-2 sm:flex sm:justify-between">
                      <div class="sm:flex">
                        <div class="flex items-center text-sm text-gray-500">
                          <i class="ri-building-line flex-shrink-0 mr-1.5 text-gray-400"></i>
                          <p>Telecom Networks Inc.</p>
                        </div>
                        <div class="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6">
                          <i class="ri-map-pin-line flex-shrink-0 mr-1.5 text-gray-400"></i>
                          <p>Nationwide</p>
                        </div>
                      </div>
                      <div class="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                        <i class="ri-calendar-line flex-shrink-0 mr-1.5 text-gray-400"></i>
                        <p>Applied on: Sep 15, 2023</p>
                      </div>
                    </div>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </main>
      </div>
    </div>
  </body>
</html>
