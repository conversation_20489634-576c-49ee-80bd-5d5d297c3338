<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Add User - Digital Portal Dashboard</title>
  <script src="https://cdn.tailwindcss.com/3.4.16"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css"
    integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg=="
    crossorigin="anonymous" referrerpolicy="no-referrer" />
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: { primary: "#e02b20", secondary: "#6366f1" },
          borderRadius: {
            none: "0px",
            sm: "4px",
            DEFAULT: "8px",
            md: "12px",
            lg: "16px",
            xl: "20px",
            "2xl": "24px",
            "3xl": "32px",
            full: "9999px",
            button: "8px",
          },
        },
      },
    };
  </script>
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css" />
  <link rel="stylesheet" href="../assets/main.css">
  <script src="https://cdn.tailwindcss.com/3.4.16"></script>
  <script>
    tailwind.config = {
      darkMode: 'class',
      theme: {
        extend: {
          colors: { primary: "#e02b20", secondary: "#6366f1", "primary-subtle": "#e4463c", "secondary-subtle": "#9FE2BF"},
          borderRadius: {
            none: "0px",
            sm: "4px",
            DEFAULT: "8px",
            md: "12px",
            lg: "16px",
            xl: "20px",
            "2xl": "24px",
            "3xl": "32px",
            full: "9999px",
            button: "8px",
          },
        },
      },
    };
  </script>
  <style type="text/tailwindcss">
    @layer components {
      .custom-form-label {
        @apply block text-sm font-medium text-gray-700 pb-2;
      }
      .enhanced-input {
        @apply appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm;
      }
  
      .enhanced-select {
        @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm;
      }
  
      .enhanced-checkbox {
        @apply h-5 w-5 text-primary focus:ring-primary border border-gray-300 rounded;
      }
  
      .main-button {
        @apply inline-flex py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-subtle hover:bg-primary focus:ring focus:ring-primary-subtle;
      }
  
      .secondary-main-button {
        @apply inline-flex py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring focus:ring-secondary-subtle/50;
      }
  
      .custom-input {
        @apply mt-1 block w-full px-3 py-2 border border-secondary-subtle rounded-md placeholder-gray-400 text-sm focus:shadow-md focus:shadow-secondary-subtle focus:border-secondary-subtle;
      }
  
      .form-section {
        @apply flex-col flex gap-y-2 lg:border-b divide-solid border-gray-300;
      }
  
      .inner-form-section {
        @apply mt-4 grid gap-y-6 gap-x-6 sm:grid-cols-2 lg:grid-cols-2;
      }
  
      .tab-heading {
        @apply flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0 mb-6;
      }
  
      .form-group {
        @apply mb-6;
      }
  
      .photo-upload {
        @apply flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0;
      }
  
      .profile-icon {
        @apply min-h-40 max-h-60 max-w-60 text-gray-300;
      }
  
      .upload-info {
        @apply flex-col space-y-2;
      }
  
      .file-input {
        @apply relative bg-white shadow-sm text-sm leading-4 font-medium text-gray-700 hover:bg-gray-50;
      }
  
      .helper-text {
        @apply text-xs text-gray-500;
      }
  
    }
  
    @layer utilities {
      :root {
        --color-primary: #e02b20;
        --color-secondary: #20d5e0;
        --color-primary-subtle: #e4463c;
        --color-secondary-subtle: #abeff3;
      }
    }
  
  </style>

  <style>
    :where([class^="ri-"])::before {
      content: "\f3c2";
    }

    body {
      font-family: 'Inter', sans-serif;
      background-color: #f9fafb;
    }

    .dropdown-content {
      display: none;
      position: absolute;
      right: 0;
      top: 100%;
      z-index: 50;
    }

    .dropdown-content.show {
      display: block;
    }

    .side-nav {
      overflow: auto;
      -ms-overflow-style: none;
      height: 75vh;
    }

    .side-nav::-webkit-scrollbar {
      display: none;
    }

    /* Hide scrollbar for Firefox */
    .side-nav {
      scrollbar-width: none;
    }

    /* Mobile sidebar styles */
    @media (max-width: 768px) {
      .mobile-sidebar-open {
        display: block !important;
        position: fixed;
        z-index: 50;
        height: 100vh;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
      }

      .mobile-sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 40;
        display: none;
      }

      .mobile-sidebar-overlay.show {
        display: block;
      }
    }
  </style>
</head>

<body>
  <!-- Theme Toggle Script -->
  <script>
    // Theme management
    function initTheme() {
      const savedTheme = localStorage.getItem('theme');
      const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

      if (savedTheme === 'dark' || (!savedTheme && systemPrefersDark)) {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }
    }

    function toggleTheme() {
      const isDark = document.documentElement.classList.contains('dark');
      if (isDark) {
        document.documentElement.classList.remove('dark');
        localStorage.setItem('theme', 'light');
      } else {
        document.documentElement.classList.add('dark');
        localStorage.setItem('theme', 'dark');
      }
    }

    // Initialize theme on page load
    initTheme();
  </script>
  <div class="flex h-screen overflow-hidden">
    <!-- Mobile sidebar overlay -->
    <div id="mobileSidebarOverlay" class="mobile-sidebar-overlay"></div>

    <!-- Sidebar -->
    <aside id="sidebar" class="w-64 bg-white dark:bg-gray-800 shadow-lg flex-shrink-0 hidden md:block">
      <div class="h-16 flex items-center px-6 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between w-full">
          <img src="../images/macra-logo.png" alt="Logo" class="max-h-12 w-auto">
          <button
            onclick="toggleTheme()"
            class="p-2 rounded-md text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            title="Toggle theme"
          >
            <i class="ri-moon-line dark:hidden"></i>
            <i class="ri-sun-line hidden dark:block"></i>
          </button>
        </div>
      </div>
      <nav class="mt-6 px-4 side-nav">
        <div class="space-y-1">
          <a href="../index.html"
            class=" flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 ">
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <i class="ri-dashboard-line"></i>
            </div>
            Dashboard
          </a>
          <a href="../license/license-management.html"
            class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <i class="ri-key-line"></i>
            </div>
            License Management
          </a>

          <a href="../spectrum/spectrum-management.html"
            class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                stroke="currentColor" class="size-6">
                <path stroke-linecap="round" stroke-linejoin="round"
                  d="M9.348 14.652a3.75 3.75 0 0 1 0-5.304m5.304 0a3.75 3.75 0 0 1 0 5.304m-7.425 2.121a6.75 6.75 0 0 1 0-9.546m9.546 0a6.75 6.75 0 0 1 0 9.546M5.106 18.894c-3.808-3.807-3.808-9.98 0-13.788m13.788 0c3.808 3.807 3.808 9.98 0 13.788M12 12h.008v.008H12V12Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z" />
              </svg>

            </div>

            Spectrum Management
          </a>
          </a>
          <a href="../financial/accounts-finance.html"
            class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                stroke="currentColor" class="size-6">
                <path stroke-linecap="round" stroke-linejoin="round"
                  d="M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
              </svg>
            </div>
            Accounts & Finance
          </a>

          <a href="../reports/reports.html"
            class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                stroke="currentColor" class="size-6">
                <path stroke-linecap="round" stroke-linejoin="round"
                  d="M12 7.5h1.5m-1.5 3h1.5m-7.5 3h7.5m-7.5 3h7.5m3-9h3.375c.621 0 1.125.504 1.125 1.125V18a2.25 2.25 0 0 1-2.25 2.25M16.5 7.5V18a2.25 2.25 0 0 0 2.25 2.25M16.5 7.5V4.875c0-.621-.504-1.125-1.125-1.125H4.125C3.504 3.75 3 4.254 3 4.875V18a2.25 2.25 0 0 0 2.25 2.25h13.5M6 7.5h3v3H6v-3Z" />
              </svg>

            </div>

            Reports & Analytics
          </a>

        </div>

        <div class="mt-8">
          <h3 class="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider">
            Settings
          </h3>
          <div class="mt-2 space-y-1">

            <a href="user-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md bg-primary bg-opacity-10 text-primary">
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                  stroke="currentColor" class="size-6">
                  <path stroke-linecap="round" stroke-linejoin="round"
                    d="M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z" />
                </svg>

              </div>
              User Management
            </a>
            <a href="../audit-trail.html"
              class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-shield-line"></i>
              </div>
              Audit Trail
            </a>
            <a href="../help-support.html"
              class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-question-line"></i>
              </div>
              Help & Support
            </a>
          </div>
        </div>
      </nav>
      <div class="absolute bottom-0 w-64 p-4 border-t">
        <a href="user-profile.html" class="flex items-center hover:bg-gray-50 p-2 rounded-md">
          <img class="h-10 w-10 rounded-full"
            src="https://banner2.cleanpng.com/********/sac/9128e319522f66aba9edf95180a86e7e.webp" alt="Profile" />
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-900">Emily Banda</p>
            <p class="text-xs text-gray-500">Administrator</p>
          </div>
        </a>
      </div>
    </aside>
    <!-- Main content -->
    <div class="flex-1 flex flex-col overflow-hidden">
      <!-- Top header -->
      <header class="bg-white dark:bg-gray-800 shadow-sm z-10">
        <div class="flex items-center justify-between h-16 px-4 sm:px-6">
          <button id="mobileMenuBtn" type="button" onclick="toggleMobileSidebar()"
            class="md:hidden text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none">
            <div class="w-6 h-6 flex items-center justify-center">
              <i class="ri-menu-line"></i>
            </div>
          </button>
          <div class="flex-1 flex items-center justify-center px-2 lg:ml-6 lg:justify-start">
            <div class="max-w-lg w-full">
              <label for="search" class="sr-only">Search</label>
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <div class="w-5 h-5 flex items-center justify-center text-gray-400">
                    <i class="ri-search-line"></i>
                  </div>
                </div>
                <input id="search" name="search"
                  class="block w-full pl-10 pr-3 py-2 border border-gray-200 rounded-md leading-5 bg-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary text-sm"
                  placeholder="Search for licenses, users, or transactions..." type="search" />
              </div>
            </div>
          </div>
          <div class="flex items-center">
            <button type="button"
              class="flex-shrink-0 p-1 mr-4 text-gray-500 rounded-full hover:text-gray-700 focus:outline-none relative">
              <span class="sr-only">View notifications</span>
              <div class="w-6 h-6 flex items-center justify-center">
                <i class="ri-notification-3-line ri-lg"></i>
              </div>
              <span class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white"></span>
            </button>
            <div class="dropdown relative">
              <button type="button" onclick="toggleDropdown()"
                class="flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                <span class="sr-only">Open user menu</span>
                <img class="h-8 w-8 rounded-full"
                  src="https://banner2.cleanpng.com/********/sac/9128e319522f66aba9edf95180a86e7e.webp" alt="Profile" />
              </button>
              <div id="userDropdown"
                class="dropdown-content mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
                <div class="py-1">
                  <a href="user-profile.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Your
                    Profile</a>
                  <a href="../account-settings.html"
                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Settings</a>
                  <a href="../auth/login.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Sign
                    out</a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      <!-- Main content area -->
      <main class="flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 p-4 sm:p-6">
        <div class="max-w-7xl mx-auto">
          <!-- Page header-->
          <div class="tab-heading">
            <div>
              <h1 class="text-2xl font-semibold text-gray-900 dark:text-gray-100">Add New User</h1>
            </div>
            <div class="relative">
              <a href="./user-management.html" class="main-button" role="button">
                <div class="w-5 h-5 flex items-center justify-center mr-2">
                  <i class="ri-arrow-left-line"></i>
                </div>
                Back to Users
              </a>
            </div>
          </div>
          <!-- Add User Form -->
          <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg">
            <div class="px-4 py-5 sm:p-6">
              <form id="addUserForm" class="grid gap-6 sm:grid-cols-2 lg:grid-cols-2 sm:gap-x-6">
                <!-- Basic Information Section -->
                <div class="form-section">
                  <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-gray-100">Basic Information</h3>
                  <div class="grid mb-4 gap-x-4 sm:grid-cols-2 lg:grid-cols-1">
                    <!-- Profile Photo -->
                    <div class="form-group">
                      <label for="profilePhoto" class="custom-form-label">Profile Photo</label>
                      <div class="photo-upload">
                        <svg class="profile-icon" fill="currentColor" viewBox="0 0 24 24">
                          <path
                            d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                        </svg>
                        <div class="upload-info">
                          <input type="file" name="profilePhoto" id="profilePhoto" class="file-input">
                          <p class="helper-text">JPG, PNG or GIF up to 2MB</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="inner-form-section">
                    <!-- First Name -->
                    <div class="grid mb-4 gap-x-4">
                      <label for="firstName" class="custom-form-label">First Name</label>
                      <div class="mt-1">
                        <input type="text" name="firstName" id="firstName" class="custom-input" required>
                      </div>
                    </div>
                    <div class="grid mb-4 gap-x-4">
                      <!-- Last Name -->
                      <label for="lastName" class="custom-form-label">Last Name</label>
                      <div class="mt-1">
                        <input type="text" name="lastName" id="lastName" class="custom-input" required>
                      </div>
                    </div>
                    <div class="grid mb-4 gap-x-4">
                      <!-- Email -->
                      <label for="email" class="custom-form-label">Email Address</label>
                      <div class="mt-1">
                        <input type="email" name="email" id="email" autocomplete="email" class="custom-input" required>
                      </div>
                    </div>
                    <div class="grid mb-4 gap-x-4">
                      <!-- Phone Number -->
                      <label for="phone" class="custom-form-label">Phone Number</label>
                      <div class="mt-1">
                        <input type="tel" name="phone" id="phone" class="custom-input">
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Account Information Section -->
                <div class="form-section">
                  <h3 class="text-lg font-medium leading-6 text-gray-900">Account Information</h3>
                  <div class="inner-form-section">
                    <!-- Username -->
                    <div>
                      <label for="username" class="custom-form-label">Username</label>
                      <div class="mt-1">
                        <input type="text" name="username" id="username" class="custom-input" required>
                      </div>
                    </div>

                    <!-- Status -->
                    <div>
                      <label for="status" class="custom-form-label">Status</label>
                      <div class="mt-1">
                        <select id="status" name="status" class="custom-input" required>
                          <option value="active">Active</option>
                          <option value="inactive">Inactive</option>
                          <option value="pending">Pending</option>
                        </select>
                      </div>
                    </div>

                    <!-- Password -->
                    <div>
                      <label for="password" class="custom-form-label">Password</label>
                      <div class="mt-1">
                        <input type="password" name="password" id="password" class="custom-input" required>
                      </div>
                      <p class="mt-1 text-xs text-gray-500">Password must be at least 8 characters and include a number
                        and special character.</p>
                    </div>

                    <!-- Confirm Password -->
                    <div>
                      <label for="confirmPassword" class="custom-form-label">Confirm Password</label>
                      <div class="mt-1">
                        <input type="password" name="confirmPassword" id="confirmPassword" class="custom-input"
                          required>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Role & Permissions Section -->
                <div class="form-section border-none">
                  <h3 class="text-lg font-medium leading-6 text-gray-900">Role & Permissions</h3>
                  <div class="inner-form-section">
                    <!-- Role Selection as Checkboxes -->
                    <div class="sm:col-span-2">
                      <label class="custom-form-label mb-2">User Roles</label>
                      <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
                        <label
                          class="flex items-center space-x-2 bg-gray-50 rounded-md px-3 py-2 cursor-pointer hover:bg-primary/10 transition">
                          <input type="checkbox" name="role" value="administrator"
                            class="form-checkbox h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary">
                          <span class="text-sm text-gray-700">Administrator</span>
                        </label>
                        <label
                          class="flex items-center space-x-2 bg-gray-50 rounded-md px-3 py-2 cursor-pointer hover:bg-primary/10 transition">
                          <input type="checkbox" name="role" value="manager"
                            class="form-checkbox h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary">
                          <span class="text-sm text-gray-700">Manager</span>
                        </label>
                        <label
                          class="flex items-center space-x-2 bg-gray-50 rounded-md px-3 py-2 cursor-pointer hover:bg-primary/10 transition">
                          <input type="checkbox" name="role" value="user"
                            class="form-checkbox h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary">
                          <span class="text-sm text-gray-700">User</span>
                        </label>
                        <label
                          class="flex items-center space-x-2 bg-gray-50 rounded-md px-3 py-2 cursor-pointer hover:bg-primary/10 transition">
                          <input type="checkbox" name="role" value="auditor"
                            class="form-checkbox h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary">
                          <span class="text-sm text-gray-700">Auditor</span>
                        </label>
                        <label
                          class="flex items-center space-x-2 bg-gray-50 rounded-md px-3 py-2 cursor-pointer hover:bg-primary/10 transition">
                          <input type="checkbox" name="role" value="finance"
                            class="form-checkbox h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary">
                          <span class="text-sm text-gray-700">Finance</span>
                        </label>
                        <label
                          class="flex items-center space-x-2 bg-gray-50 rounded-md px-3 py-2 cursor-pointer hover:bg-primary/10 transition">
                          <input type="checkbox" name="role" value="support"
                            class="form-checkbox h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary">
                          <span class="text-sm text-gray-700">Support</span>
                        </label>
                        <label
                          class="flex items-center space-x-2 bg-gray-50 rounded-md px-3 py-2 cursor-pointer hover:bg-primary/10 transition">
                          <input type="checkbox" name="role" value="compliance"
                            class="form-checkbox h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary">
                          <span class="text-sm text-gray-700">Compliance</span>
                        </label>
                        <label
                          class="flex items-center space-x-2 bg-gray-50 rounded-md px-3 py-2 cursor-pointer hover:bg-primary/10 transition">
                          <input type="checkbox" name="role" value="spectrum"
                            class="form-checkbox h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary">
                          <span class="text-sm text-gray-700">Spectrum Manager</span>
                        </label>
                        <label
                          class="flex items-center space-x-2 bg-gray-50 rounded-md px-3 py-2 cursor-pointer hover:bg-primary/10 transition">
                          <input type="checkbox" name="role" value="license"
                            class="form-checkbox h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary">
                          <span class="text-sm text-gray-700">License Manager</span>
                        </label>
                        <label
                          class="flex items-center space-x-2 bg-gray-50 rounded-md px-3 py-2 cursor-pointer hover:bg-primary/10 transition">
                          <input type="checkbox" name="role" value="report"
                            class="form-checkbox h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary">
                          <span class="text-sm text-gray-700">Report Viewer</span>
                        </label>
                      </div>
                      <p class="mt-1 text-xs text-gray-500">The role determines what permissions the user will have.</p>
                    </div>
                  </div>
                </div>

                <!-- Additional Information Section -->
                <div class="form-section border-none">
                  <h3 class="text-lg font-medium leading-6 text-gray-900">Additional Information</h3>
                  <div class="inner-form-section">
                    <!-- Job Title -->
                    <div class="flex-col">
                      <label for="jobTitle" class="custom-form-label">Job Title</label>
                      <input type="text" name="jobTitle" id="jobTitle" class="custom-input">
                    </div>

                    <!-- Employee ID -->
                    <div class="flex-col">
                      <label for="employeeId" class="custom-form-label">Employee ID</label>
                      <div class="mt-1">
                        <input type="text" name="employeeId" id="employeeId" class="custom-input">
                      </div>
                    </div>

                                        <!-- Department -->
                    <div class="sm:col-span-1">
                      <label for="department" class="custom-form-label">Department</label>
                      <div class="mt-1">
                        <select id="department" name="department" class="custom-input">
                          <option value="">Select a department</option>
                          <option value="it">IT</option>
                          <option value="finance">Finance</option>
                          <option value="operations">Operations</option>
                          <option value="hr">Human Resources</option>
                          <option value="marketing">Marketing</option>
                        </select>
                      </div>
                    </div>

                    <!-- Notes -->
                    <div class="sm:col-span-2 flex-col">
                      <label for="notes" class="custom-form-label">Notes</label>
                      <div class="mt-1">
                        <textarea id="notes" name="notes" rows="3" class="custom-input"></textarea>
                      </div>
                      <p class="mt-1 text-xs text-gray-500">Add any additional information about this user.</p>
                    </div>
                  </div>
                </div>

                <!-- Form Actions -->
                <div class="flex-row flex justify-start space-x-3 sm:justify-end space-x-2">
                  <button type="reset" class="secondary-main-button relative">Reset</button>
                  <button type="submit" onclick="saveUser()" class="main-button relative">
                    Save User
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>

  <script>
    // Toggle dropdown menu
    function toggleDropdown() {
      const dropdown = document.getElementById('userDropdown');
      dropdown.classList.toggle('show');

      // Prevent the click from propagating to the window event
      event.stopPropagation();
    }

    // Close dropdown when clicking outside
    window.addEventListener('click', function (event) {
      if (!event.target.matches('.dropdown button') && !event.target.closest('.dropdown button img')) {
        const dropdowns = document.getElementsByClassName('dropdown-content');
        for (let i = 0; i < dropdowns.length; i++) {
          const openDropdown = dropdowns[i];
          if (openDropdown.classList.contains('show')) {
            openDropdown.classList.remove('show');
          }
        }
      }
    });

    // Toggle mobile sidebar
    function toggleMobileSidebar() {
      const sidebar = document.getElementById('sidebar');
      const overlay = document.getElementById('mobileSidebarOverlay');

      sidebar.classList.toggle('mobile-sidebar-open');
      overlay.classList.toggle('show');

      // Close sidebar when clicking on overlay
      overlay.onclick = function () {
        sidebar.classList.remove('mobile-sidebar-open');
        overlay.classList.remove('show');
      };
    }

    // Save user function
    function saveUser() {
      // Get form values
      const firstName = document.getElementById('firstName').value;
      const lastName = document.getElementById('lastName').value;
      const email = document.getElementById('email').value;
      const password = document.getElementById('password').value;
      const confirmPassword = document.getElementById('confirmPassword').value;
      const role = document.getElementById('role').value;

      // Validate form
      if (!firstName || !lastName || !email || !password || !confirmPassword || !role) {
        alert('Please fill in all required fields');
        return;
      }

      if (password !== confirmPassword) {
        alert('Passwords do not match');
        return;
      }

      // In a real application, you would send this data to the server
      // For this demo, we'll just show a success message and redirect
      alert(`User ${firstName} ${lastName} added successfully with role: ${role}`);

      // Redirect to user management page
      window.location.href = 'user-management.html';
    }

    // Add User Modal logic
    const addUserBtn = document.getElementById('addUserBtn');
    const addUserModal = document.getElementById('addUserModal');
    const addUserModalForm = document.getElementById('addUserModalForm');

    if (addUserBtn && addUserModal) {
      addUserBtn.onclick = function () {
        addUserModal.classList.remove('hidden');
      };
    }
    function closeAddUserModal() {
      addUserModal.classList.add('hidden');
    }
    window.closeAddUserModal = closeAddUserModal;

    // Optional: Prevent modal form submission (for demo)
    if (addUserModalForm) {
      addUserModalForm.onsubmit = function (e) {
        e.preventDefault();
        // Here you would handle form submission (e.g., send data to backend)
        closeAddUserModal();
      };
    }
  </script>
</body>

</html>